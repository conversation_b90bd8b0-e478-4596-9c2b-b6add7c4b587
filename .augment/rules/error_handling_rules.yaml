---
# 错误处理规则 - 定义错误处理的规范
name: "错误处理规则"
description: "flight_fare项目错误处理的规范"
version: "1.0.0"
rules:
  # 异常捕获规则
  exception_catching:
    - rule: "specific_exception"
      description: "捕获具体的异常类型"
      message: "捕获具体的异常类型，而不是笼统的Exception"
      severity: "warning"
      examples:
        correct:
          - "try:\n    ...\nexcept ValueError:\n    ..."
        incorrect:
          - "try:\n    ...\nexcept Exception:\n    ..."

    - rule: "exception_logging"
      description: "记录异常信息"
      message: "记录异常信息，便于问题排查"
      severity: "warning"

    - rule: "exception_propagation"
      description: "适当传播异常"
      message: "适当传播异常，避免吞噬异常"
      severity: "warning"

  # 错误码规则
  error_code:
    - rule: "consistent_error_code"
      description: "使用一致的错误码体系"
      message: "使用一致的错误码体系，便于客户端处理"
      severity: "warning"

    - rule: "error_code_documentation"
      description: "错误码应有文档说明"
      message: "错误码应有文档说明，便于理解和使用"
      severity: "info"

  # 错误恢复规则
  error_recovery:
    - rule: "retry_mechanism"
      description: "实施重试机制"
      message: "对于可恢复的错误，实施重试机制"
      severity: "warning"

    - rule: "fallback_strategy"
      description: "实施降级策略"
      message: "对于不可恢复的错误，实施降级策略"
      severity: "warning"

  # 业务错误处理规则 - flight_fare项目相关
  business_error:
    - rule: "book_ticket_not_enough"
      description: "处理票量不足错误"
      message: "在生单过程中发现升舱情况时，需要删除查询缓存（包括任务key和变价标记等）"
      severity: "error"

    - rule: "book_price_error"
      description: "处理价格错误"
      message: "处理价格错误，清除相关缓存"
      severity: "error"

  # 缓存错误处理规则
  cache_error:
    - rule: "cache_update_error"
      description: "处理缓存更新错误"
      message: "在实时验价收到结果后需要更新一份到查询缓存，改动点在verify_book_result函数"
      severity: "error"

    - rule: "cache_clear_error"
      description: "处理缓存清除错误"
      message: "在生单过程中发现升舱情况时，需要为verify_book_result函数添加单元测试，验证查询缓存（包括任务key和变价标记等）被正确删除"
      severity: "error"
