---
# 数据库交互规则 - 定义数据库操作的规范
name: "数据库交互规则"
description: "flight_fare项目数据库操作的规范"
version: "1.0.0"
rules:
  # 数据库连接规则
  database_connection:
    - rule: "connection_pooling"
      description: "使用连接池管理数据库连接"
      message: "使用连接池管理数据库连接，避免频繁创建和销毁连接"
      severity: "warning"
    
    - rule: "connection_error_handling"
      description: "妥善处理数据库连接错误"
      message: "妥善处理数据库连接错误，包括重试和降级策略"
      severity: "error"

  # 数据库查询规则
  database_query:
    - rule: "orm_usage"
      description: "优先使用ORM进行数据库操作"
      message: "优先使用ORM进行数据库操作，避免直接使用SQL语句"
      severity: "info"
    
    - rule: "query_optimization"
      description: "优化数据库查询，避免N+1查询问题"
      message: "优化数据库查询，避免N+1查询问题"
      severity: "warning"
    
    - rule: "index_usage"
      description: "合理使用索引提高查询效率"
      message: "合理使用索引提高查询效率"
      severity: "warning"

  # 数据库事务规则
  database_transaction:
    - rule: "transaction_management"
      description: "正确管理数据库事务"
      message: "正确管理数据库事务，确保数据一致性"
      severity: "error"
    
    - rule: "transaction_isolation"
      description: "设置适当的事务隔离级别"
      message: "设置适当的事务隔离级别，避免并发问题"
      severity: "warning"

  # 数据库结果处理规则
  database_result:
    - rule: "result_type_conversion"
      description: "数据库封装后，返回的都是dict或list[dict]，不是orm对象"
      message: "数据库封装后，返回的都是dict或list[dict]，不是orm对象，在单元测试中mock_get应该返回dict或list[dict]而不是ORM对象"
      severity: "error"
    
    - rule: "result_validation"
      description: "验证数据库操作结果"
      message: "验证数据库操作结果，确保数据完整性"
      severity: "warning"

  # 缓存规则
  cache:
    - rule: "cache_usage"
      description: "合理使用缓存减轻数据库负担"
      message: "合理使用缓存减轻数据库负担"
      severity: "info"
    
    - rule: "cache_invalidation"
      description: "正确处理缓存失效"
      message: "正确处理缓存失效，确保数据一致性"
      severity: "warning"
    
    - rule: "cache_key_naming"
      description: "缓存键命名应遵循一定规范"
      message: "缓存键命名应遵循一定规范，避免冲突"
      severity: "info"
