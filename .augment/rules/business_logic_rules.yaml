---
# 业务逻辑规则 - 定义flight_fare项目业务逻辑的规范
name: "业务逻辑规则"
description: "flight_fare项目业务逻辑的规范"
version: "1.0.0"
rules:
  # 票价处理规则
  fare_processing:
    - rule: "fare_snapshot_structure"
      description: "fare_snapshot结构规则"
      message: "fare_snapshot在生产环境中有复杂的结构，包含flight_info和products部分，其中products包含成人和儿童的价格详情，包括src_cny_total字段"
      severity: "error"

    - rule: "fare_calculation"
      description: "票价计算规则"
      message: "票价计算需要考虑汇率转换、税费等因素"
      severity: "error"

  # 缓存处理规则
  cache_processing:
    - rule: "cache_update"
      description: "缓存更新规则"
      message: "在实时验价收到结果后需要更新一份到查询缓存，改动点在verify_book_result函数"
      severity: "error"

    - rule: "cache_clear"
      description: "缓存清除规则"
      message: "在生单过程中发现升舱情况时，需要删除查询缓存（包括任务key和变价标记等），改动点在verify_book_result函数"
      severity: "error"

    - rule: "cache_reuse"
      description: "缓存逻辑复用规则"
      message: "将update_cache_from_verify_result函数应用到crawler_callback_views.py的search_result回调中，实现缓存更新逻辑的复用"
      severity: "warning"

    - rule: "avoid_duplicate_conversion"
      description: "避免重复转换"
      message: "在cache_services.py中注释掉parse_exchange函数调用是因为外层已经调用过，内部重复调用会导致多次转换造成数值不正确"
      severity: "error"

  # 查询处理规则 - flight_fare项目核心功能
  search_processing:
    - rule: "search_flow"
      description: "查询流程规则"
      message: "flight_fare项目中与报价查询(search)相关的所有流程点，包括数据获取逻辑、缓存逻辑、投放逻辑和查询计算逻辑等"
      severity: "info"

    - rule: "search_result_handling"
      description: "查询结果处理规则"
      message: "查询结果需要进行格式化、排序和过滤，确保返回给客户端的数据符合要求"
      severity: "warning"

  # 验价处理规则 - flight_fare项目核心功能
  verify_processing:
    - rule: "verify_result_handling"
      description: "验价结果处理规则"
      message: "验价结果需要更新到缓存，并根据结果决定是否需要清除相关缓存"
      severity: "error"

    - rule: "verify_task_management"
      description: "验价任务管理规则"
      message: "条件'VerifyTask.task_keep_end_time >= datetime.now().strftime('%Y-%m-%d %H:%M:%S')'是在flight系统中比较任务保持结束时间与当前时间的正确语法"
      severity: "info"

  # 数据解析规则 - 通用规则
  data_parsing:
    - rule: "exchange_rate_handling"
      description: "汇率处理规则"
      message: "汇率转换需要考虑精度问题，避免舍入误差"
      severity: "warning"

    - rule: "data_format_validation"
      description: "数据格式验证规则"
      message: "接收到的数据需要进行格式验证，确保符合预期"
      severity: "warning"
