---
# 代码风格规则 - 定义代码风格和格式的规范
name: "代码风格规则"
description: "flight_fare项目代码风格和格式的规范"
version: "1.0.0"
rules:
  # 代码组织规则
  code_organization:
    - rule: "directory_structure"
      description: "遵循项目目录结构"
      message: "执行文件操作前先确认模块绝对路径，然后切换到目标位置，再执行调整"
      severity: "warning"
    
    - rule: "file_location_verification"
      description: "每次生成和修改文件前需要校对目录"
      message: "每次生成和修改文件前需要校对目录，确保文件位置正确"
      severity: "warning"
    
    - rule: "absolute_path_usage"
      description: "修改不同模块时使用绝对路径"
      message: "工作区包含多个独立项目，修改不同模块时应使用绝对路径"
      severity: "warning"

  # 代码风格规则
  code_style:
    - rule: "import_organization"
      description: "导入语句应按照标准库、第三方库、本地模块的顺序组织"
      message: "导入语句应按照标准库、第三方库、本地模块的顺序组织"
      severity: "info"
    
    - rule: "function_docstring"
      description: "函数应有文档字符串说明功能、参数和返回值"
      message: "函数应有文档字符串说明功能、参数和返回值"
      severity: "info"
    
    - rule: "class_docstring"
      description: "类应有文档字符串说明功能和属性"
      message: "类应有文档字符串说明功能和属性"
      severity: "info"

  # 代码质量规则
  code_quality:
    - rule: "avoid_duplicate_code"
      description: "避免重复代码，对重复代码进行抽象和重构"
      message: "避免重复代码，对重复代码进行抽象和重构"
      severity: "warning"
    
    - rule: "function_length"
      description: "函数不应过长，应保持单一职责"
      message: "函数不应过长，应保持单一职责"
      severity: "info"
    
    - rule: "variable_naming"
      description: "变量命名应清晰表达其用途"
      message: "变量命名应清晰表达其用途"
      severity: "info"

  # 包管理规则
  package_management:
    - rule: "use_package_managers"
      description: "使用适当的包管理器进行依赖管理"
      message: "使用适当的包管理器进行依赖管理，而不是手动编辑包配置文件"
      severity: "warning"
      examples:
        correct:
          - "pip install package_name"
          - "pip uninstall package_name"
        incorrect:
          - "直接编辑requirements.txt"
    
    - rule: "package_version_specification"
      description: "在依赖管理中指定版本号"
      message: "在依赖管理中指定版本号，避免版本不兼容问题"
      severity: "info"
