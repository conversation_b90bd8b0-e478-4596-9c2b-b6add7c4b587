---
# API接口规则 - 定义API接口设计的规范
name: "API接口规则"
description: "flight_fare项目API接口设计的规范"
version: "1.0.0"
rules:
  # API设计规则
  api_design:
    - rule: "restful_api"
      description: "遵循RESTful API设计原则"
      message: "遵循RESTful API设计原则，使用适当的HTTP方法和状态码"
      severity: "info"

    - rule: "api_versioning"
      description: "API应有版本控制"
      message: "API应有版本控制，便于兼容性管理"
      severity: "warning"

    - rule: "api_documentation"
      description: "API应有完善的文档"
      message: "API应有完善的文档，包括参数说明和返回值说明"
      severity: "warning"

  # 请求处理规则
  request_handling:
    - rule: "input_validation"
      description: "验证API输入参数"
      message: "验证API输入参数，确保数据有效性"
      severity: "error"

    - rule: "parameter_missing_handling"
      description: "正确处理参数缺失情况"
      message: "验证API请求中的必要参数，缺失时返回适当的错误码"
      severity: "error"
      examples:
        correct:
          - "return {'code': 422, 'message': 'Missing required parameter: task_key'}"
        incorrect:
          - "继续处理请求而不返回错误"

    - rule: "request_timeout"
      description: "设置适当的请求超时时间"
      message: "设置适当的请求超时时间，避免长时间阻塞"
      severity: "warning"

  # 响应处理规则
  response_handling:
    - rule: "consistent_response_format"
      description: "保持一致的响应格式"
      message: "保持一致的响应格式，便于客户端处理"
      severity: "warning"

    - rule: "error_response"
      description: "返回明确的错误信息"
      message: "返回明确的错误信息，包括错误码和错误描述"
      severity: "warning"

    - rule: "pagination"
      description: "大数据量结果应分页返回"
      message: "大数据量结果应分页返回，避免响应过大"
      severity: "info"

  # API安全规则
  api_security:
    - rule: "authentication"
      description: "API应有适当的认证机制"
      message: "API应有适当的认证机制，确保安全性"
      severity: "error"

    - rule: "rate_limiting"
      description: "实施API访问频率限制"
      message: "实施API访问频率限制，防止滥用"
      severity: "warning"

    - rule: "sensitive_data_handling"
      description: "妥善处理敏感数据"
      message: "妥善处理敏感数据，避免泄露"
      severity: "error"

  # 外部API调用规则
  external_api:
    - rule: "external_api_error_handling"
      description: "妥善处理外部API调用错误"
      message: "妥善处理外部API调用错误，包括重试和降级策略"
      severity: "error"

    - rule: "external_api_timeout"
      description: "设置适当的外部API调用超时时间"
      message: "设置适当的外部API调用超时时间，避免长时间阻塞"
      severity: "warning"
      examples:
        correct:
          - "forward_by_curl_cffi应正确处理timeout参数"
