---
# 测试规则 - 定义单元测试的规范和最佳实践
name: "测试规则"
description: "flight_fare项目单元测试的规范和最佳实践"
version: "1.0.0"
rules:
  # 测试文件组织规则
  test_organization:
    - rule: "debug_tests_location"
      description: "调试用途的测试文件应放在tests/debug目录下"
      pattern: "tests/debug/**/*.py"
      message: "调试用途的测试文件应放在tests/debug目录下"
      severity: "warning"
    
    - rule: "view_tests_location"
      description: "视图相关的测试应放在tests/view_tests目录下"
      pattern: "tests/view_tests/**/*.py"
      message: "视图相关的测试应放在tests/view_tests目录下"
      severity: "warning"
    
    - rule: "service_tests_location"
      description: "服务相关的测试应放在tests/services_tests目录下"
      pattern: "tests/services_tests/**/*.py"
      message: "服务相关的测试应放在tests/services_tests目录下"
      severity: "warning"

  # Mock策略规则
  mock_strategy:
    - rule: "mock_only_external_dependencies"
      description: "只对数据库（MySQL、MongoDB、Redis）和外部API接口进行mock，保留嵌套函数调用"
      message: "只对数据库和外部API接口进行mock，保留嵌套函数调用以测试链路完整性"
      severity: "error"
      examples:
        correct:
          - "@patch('app.services.cache_services.FlightFareMongoService')"
          - "@patch('app.services.fetch_rule_services.get_rule')"
        incorrect:
          - "@patch('app.services.cache_services.crawler_callback_services')"
          - "@patch('app.services.cache_services')"
    
    - rule: "mock_data_validation"
      description: "mock数据应符合定义要求和格式"
      message: "mock时需要检查数据定义，接口定义，以保证mock数据符合定义要求和格式"
      severity: "error"
    
    - rule: "missing_definition_handling"
      description: "遇到找不到定义的情况时，暂停执行，提示需要哪些数据"
      message: "遇到找不到定义的情况时，暂停执行，提示需要哪些数据，等人工输入后继续"
      severity: "warning"

  # 测试覆盖率规则
  test_coverage:
    - rule: "core_logic_coverage"
      description: "核心业务逻辑必须有单元测试覆盖"
      message: "核心业务逻辑必须有单元测试覆盖"
      severity: "error"
    
    - rule: "api_test_coverage"
      description: "对自身API建立单元测试用例"
      message: "对自身API建立单元测试用例"
      severity: "warning"
    
    - rule: "test_import_location"
      description: "在test方法内引入依赖避免循环引用"
      message: "在单元测试中应在test方法内引入依赖，避免循环引用"
      severity: "warning"

  # 测试执行规则
  test_execution:
    - rule: "pytest_configuration"
      description: "使用pytest.ini配置测试执行环境"
      message: "使用pytest.ini配置测试执行环境，排除debug目录"
      severity: "info"
      examples:
        correct:
          - "addopts = --disable-warnings --log-cli-level=debug --capture=no --ignore=tests/debug"
    
    - rule: "test_failure_handling"
      description: "测试失败时，先确定问题是在测试文件还是源代码"
      message: "测试失败时，先确定问题是在测试文件还是源代码；如果是源代码问题，停止并等待人工验证"
      severity: "warning"
