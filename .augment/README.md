# Augment Code MDC规则

本目录包含了Augment Code的Model-Driven Code (MDC) 规则，用于指导flight_fare项目的开发和维护。

## 规则文件说明

规则文件位于`.augment/rules`目录下，按照不同的关注点进行分类：

1. **业务逻辑规则** (`business_logic_rules.yaml`)
   - 定义flight_fare项目业务逻辑的规范
   - 包括票价处理、缓存处理、查询处理、验价处理等规则

2. **API接口规则** (`api_rules.yaml`)
   - 定义API接口设计的规范
   - 包括API设计、请求处理、响应处理、API安全、外部API调用等规则

3. **数据库交互规则** (`database_rules.yaml`)
   - 定义数据库操作的规范
   - 包括数据库连接、查询、事务、结果处理、缓存等规则

4. **错误处理规则** (`error_handling_rules.yaml`)
   - 定义错误处理的规范
   - 包括异常捕获、错误码、错误恢复、业务错误处理、缓存错误处理等规则

5. **测试规则** (`testing_rules.yaml`)
   - 定义单元测试的规范和最佳实践
   - 包括测试文件组织、Mock策略、测试覆盖率、测试执行等规则

6. **代码风格规则** (`code_style_rules.yaml`)
   - 定义代码风格和格式的规范
   - 包括代码组织、代码风格、代码质量、包管理等规则

## 规则严重程度

规则按照严重程度分为以下几个级别：

- **error**: 必须遵循的规则，违反将导致严重问题
- **warning**: 强烈建议遵循的规则，违反可能导致问题
- **info**: 建议遵循的规则，有助于提高代码质量

## 使用方法

Augment Code会自动检查代码是否符合这些规则，并在违反规则时提供相应的提示和建议。

开发人员应当：

1. 在开发新功能或修改现有功能时，参考相关规则
2. 在提交代码前，确保代码符合这些规则
3. 在代码审查时，使用这些规则作为评审标准

## 规则维护

如需添加、修改或删除规则，请修改相应的规则文件，并确保：

1. 遵循YAML格式
2. 提供清晰的规则描述和消息
3. 设置适当的严重程度
4. 必要时提供正确和错误的示例
