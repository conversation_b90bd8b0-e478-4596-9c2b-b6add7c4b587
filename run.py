import asyncio
from loguru import logger
import typer
from app.api import fast_api_app
from app.config import settings

cli = typer.Typer()


@cli.command()
def init_super_admin(username: str, password: str, is_super: bool = False):
    """初始化超级管理员"""
    import uvloop
    from app.services import admin_services

    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

    asyncio.run(admin_services.create_admin(username=username, password=password, is_super=is_super, roles=''))


@cli.command()
def send_search_task(interval: int = 5, batch_size: int = 500):
    """创建搜索任务"""
    from app.services import task_services

    asyncio.run(task_services.create_search_tasks(interval=interval, batch_size=batch_size))


@cli.command()
def send_verify_task(interval: int = 1):
    """创建验价任务"""
    from app.services import task_services

    asyncio.run(task_services.create_verify_tasks(interval=interval))


@cli.command()
def update_city_and_airports(csv_file: str):
    """更新城市与机场对应关系"""
    from app.services import base_data_service

    asyncio.run(base_data_service.update_city_and_airports(csv_file=csv_file))


@cli.command()
def append_airport_code(airport_code: str, city_code: str, city_en: str, country_code: str):
    """调整机场代码"""
    from app.services import base_data_service

    asyncio.run(
        base_data_service.append_airport_code(
            airport_code=airport_code, city_code=city_code, city_en=city_en, country_code=country_code
        )
    )


@cli.command()
def shopping_push(interval: int = 5, concurrency: int = 1):
    """主动更新平台缓存"""
    from app.services import task_services

    asyncio.run(task_services.shopping_push(interval=interval, concurrency=concurrency))


@cli.command()
def tb_fetch(storagestate_file: str, concurrency: int = 10):
    """执行淘宝比价数据抓取 旧（web模式）"""
    from app.services import task_services

    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(
            task_services.run_tb_data_fetch(storagestate_file=storagestate_file, concurrency=concurrency)
        )
    finally:
        pass


@cli.command()
def tb_fetch_api(concurrency: int = 10, interval: int = 1, max_qpm: int = 175):
    """执行淘宝比价数据抓取 新（api模式）- 持续循环模式

    Args:
        concurrency (int, optional): 并发数. Defaults to 10.
        interval (int, optional): 轮次间隔秒数，内部已做180QPM限制. Defaults to 1.
        max_qpm (int, optional): 最大请求数，180为平台限制. Defaults to 175.
    """
    from app.services import task_services
    import time
    from loguru import logger

    loop = asyncio.get_event_loop()
    try:
        while True:
            try:
                logger.info("开始新一轮淘宝比价数据抓取")
                loop.run_until_complete(
                    task_services.run_tb_price_compare_fetch(concurrency=concurrency, max_requests_per_minute=max_qpm)
                )
                logger.info("本轮淘宝比价数据抓取完成")
            except KeyboardInterrupt:
                logger.info("收到中断信号，退出循环")
                break
            except Exception as e:
                logger.exception(f"淘宝比价数据抓取出现异常: {e}")
            finally:
                logger.info(f"等待 {interval} 秒后开始下一轮抓取")
                time.sleep(interval)
    finally:
        loop.close()


@cli.command()
def send_scan_task(interval: int = 10):
    """创建扫描出票任务"""
    from app.services import task_services

    asyncio.run(task_services.send_scan_book_tasks(interval=interval))


@cli.command()
def create_airline_account(airline_code: str, username: str, password: str, aes_key: str):
    """创建航空公司账号"""
    from app.services import airline_account_service
    from commons.utils import CipherUtils

    encrypted_password = CipherUtils.aes_encrypt(key=aes_key, plaintext=password)
    asyncio.run(
        airline_account_service.create_airline_account(
            airline_code=airline_code, username=username, password=encrypted_password
        )
    )


@cli.command()
def switch_airline_account(airline_code: str, username: str):
    """切换航空公司账号"""
    from app.services import airline_account_service

    asyncio.run(airline_account_service.switch_airline_account(airline_code=airline_code, username=username))


@cli.command()
def debug():
    """调试"""

    from app.services import public_services
    from app.services import debug_services
    from app.services import base_data_service

    asyncio.run(
        # public_services.search(
        #     ota_code='taobao',
        #     dep_airport_code='SIN',
        #     arr_airport_code='CAN',
        #     dep_date='2024-08-26',
        #     adult=1,
        #     child=0,
        #     infant=0,
        # )
        # public_services.search_simple(
        #     ota_code='taobao',
        #     dep_airport_code='PVG',
        #     arr_airport_code='ICN',
        #     dep_date='2024-10-20',
        #     adult=1,
        #     child=0,
        #     infant=0,
        # )
        # debug_services.debug()
        base_data_service.get_city_code(airport_code='PVG')
    )
    from app.config import new_settings

    logger.info(new_settings.to_dict())
    logger.info(new_settings.get('airline_code_maps'))


if __name__ == '__main__':
    cli()
