from datetime import date
import hashlib
import json
import re
import time
from typing import Any, ClassVar, Dict
from urllib.parse import urlparse
import uuid
import orjson
from pydantic.main import IncEx
from loguru import logger
from pydantic import BaseModel, ConfigDict, Field, PrivateAttr, model_validator


# 并不是所有model都派生于BaseRequest
GLOBAL_MODEL_CONFIG = ConfigDict(
    # 不允许空字符串
    min_extra_fields=1,
    # 自动去除空格
    str_strip_whitespace=True,
    #
    json_encoders={date: lambda v: v.isoformat()},
)


def generate_request_id():
    from commons.extensions.logger_extras import log_uid

    if str(log_uid):
        return str(log_uid)
    return hashlib.md5(f'{uuid.uuid4()}'.encode('utf-8')).hexdigest()


class BaseRequestModel(BaseModel):
    model_config = GLOBAL_MODEL_CONFIG
    # ClassVar 不会被当做模型字段
    # 可用来定义常量
    # 基类保留的字段
    request_method: ClassVar[str] = 'POST'
    endpoint: ClassVar[str] = '/'
    content_type: ClassVar[str] = 'json'
    encrypt: ClassVar[bool] = False

    request_exclude: ClassVar[IncEx] = None

    # 接口通用属性
    # 通过 exclude=True 从 model_dump 中排除

    _headers: Dict[str, Any] = PrivateAttr(default_factory=lambda: {'X-Request-ID': generate_request_id()})
    _fix_url: str = PrivateAttr(default=None)

    # request_id: str = Field(default_factory=generate_request_id, description='请求唯一标识')

    # 使用 @model_validator 进行填充
    # @model_validator(mode='before')
    # @classmethod
    # def set_defaults(cls, values: Dict[str, Any]) -> Dict[str, Any]:
    #     # 自动生成 requestId 和自动填充 customerId
    #     if 'request_id' not in values:
    #         values['request_id'] = str(uuid.uuid4())  # 自动生成 requestId

    #     return values

    def get_url_path(self, host) -> str:
        """
        动态替换 URL 模板中的占位符。
        """
        if self._fix_url:
            return self._fix_url

        endpoint = host + self.endpoint

        try:
            # 这里改利用request_exclude进行控制
            # 比起在字段上直接设置exclude=True的方法，更加灵活
            # 比如有的字段只出现在url，有的字段既出现在url，又出现在字段中

            # 在填充url的时候，不需要填充空值
            # 同时不设置exclude_unset=True，避免默认值被忽略
            # 不设置exclude
            replacements = self.model_dump(exclude_none=True)
            endpoint = endpoint.format_map(replacements)
            endpoint = endpoint.lower()
        except KeyError as e:
            raise ValueError(f"Error formatting URL: Missing value for placeholder {e}")
        return endpoint

    def request_kwargs_dump(self, host, url=None):
        request_dict = {'url': self.get_url_path(host)}
        if self._headers:
            request_dict['headers'] = self._headers
        # exclude_unset=True 会导致 使用默认值的字段被忽略
        # 所以这里不要设置 exclude_unset=True
        # 这里同时设置了exclude，排除了request_exclude中仅做url填充的字段
        send_data = self.model_dump(exclude_none=True, exclude=self.request_exclude)
        if self.request_method.upper() == 'POST':
            if self.content_type == 'json':
                request_dict['json'] = send_data
            else:
                request_dict['data'] = send_data
        elif self.request_method.upper() == 'GET':
            request_dict['params'] = send_data
        else:
            logger.info(f"{self.request_method} 参数默认用data发送")
            request_dict['data'] = send_data
        return request_dict


class SdkClient:
    def __init__(self, host: str, **kwargs):
        self.host = host
        self.aes_key = kwargs.get('aes_key')
        self.timeout = kwargs.get('timeout', 30)
        self.response_headers = None

    def send(self, request: BaseRequestModel):

        kwargs = request.request_kwargs_dump(self.host)

        return self.request(method=request.request_method.upper(), **kwargs)

    def request(self, method, url, **kwargs):
        import requests

        cont_time = time.time()
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        result = None
        try:
            response = requests.request(method.upper(), url, **kwargs)
            self.response_headers = response.headers
            result = response.json()
        except (json.decoder.JSONDecodeError, orjson.JSONDecodeError):
            result = None
        except Exception as e:
            logger.exception(e)
            raise
        finally:
            # logger.debug(f'kwargs: {kwargs}, resp_headers: {self.response_headers},  response: {result}')
            # logger.info('api request', method=request.request_method, **kwargs, response=result)
            parsed_url = urlparse(url)

            logger.bind(write_tag="api_es_log").info(
                '',
                api_type="send",
                api_path=parsed_url.path,
                request=orjson.dumps(kwargs, default=str).decode("utf-8"),
                response=orjson.dumps(result, default=str).decode("utf-8"),
                status=result.get('status', '') if result else '',
                code=result.get('code', -1) if result else -1,
                message=result.get('message', '') if result else '接口异常',
                cost_time=time.time() - cont_time,
            )
        return result

    async def send_async(self, request: BaseRequestModel):

        kwargs = request.request_kwargs_dump(self.host)
        return await self.request_async(method=request.request_method.upper(), **kwargs)

    async def request_async(self, method, url, **kwargs):
        from curl_cffi import requests

        cont_time = time.time()
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.timeout
        result = None

        async with requests.AsyncSession() as s:
            try:
                response = await s.request(method, url, **kwargs)
                self.response_headers = response.headers
                if 'application/json' in response.headers.get('content-type', ''):
                    result = response.json()
                else:
                    result = response.text
            # json decode错误
            except (json.decoder.JSONDecodeError, orjson.JSONDecodeError):
                result = None
            except Exception as e:
                logger.exception(e)
                raise
            finally:
                # logger.debug(f'kwargs: {kwargs}, resp_headers: {self.response_headers},  response: {result}')
                parsed_url = urlparse(url)
                logger.bind(write_tag="api_es_log").info(
                    '',
                    api_type="send",
                    api_path=parsed_url.path,
                    request=orjson.dumps(kwargs, default=str).decode("utf-8"),
                    response=orjson.dumps(result, default=str).decode("utf-8"),
                    status=result.get('status', '') if result else '',
                    code=result.get('code', -1) if result else -1,
                    message=result.get('message', '') if result else '接口异常',
                    cost_time=time.time() - cont_time,
                )

        return result
