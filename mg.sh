#!/bin/bash

# 检查参数数量是否正确
if [ $# -ne 3 ]; then
    echo "Usage: $0 <remote_branch> <merge_branch> <develop_branch>"
    exit 1
fi

# 获取参数
REMOTE_BRANCH=$1
MERGE_BRANCH=$2
DEVELOP_BRANCH=$3

# 删除本地临时合并分支
git branch -D $MERGE_BRANCH

# Fetch 远端开发分支到合并分支
git fetch origin $REMOTE_BRANCH:$MERGE_BRANCH

# 切换前对本地分支未提交内容进行暂存
git stash

# 切换到合并分支
git checkout $MERGE_BRANCH

# 合并本地开发分支
git merge $DEVELOP_BRANCH

# 检查是否有冲突
if [ $? -ne 0 ]; then
    echo "合并产生冲突，请解决冲突后再继续。"
    exit 1
fi

# Push 合并分支到远端开发分支
git push origin $MERGE_BRANCH:$DEVELOP_BRANCH

# 检查是否 Push 成功
if [ $? -ne 0 ]; then
    echo "Push 合并分支到远端开发分支失败，请检查后再尝试。"
    exit 1
fi

# 切换回开发分支
git checkout $DEVELOP_BRANCH

# 拉取远端最新内容
git pull origin $DEVELOP_BRANCH:$DEVELOP_BRANCH

# 切换后取回暂存内容
git stash pop

# 输出提示信息
echo "合并完成，已切换回开发分支，并拉取了最新内容。"


