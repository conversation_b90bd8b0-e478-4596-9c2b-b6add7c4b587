from datetime import datetime, <PERSON><PERSON><PERSON>
import os

from loguru import logger
import orjson

from app.services import crawler_callback_services


@pytest.mark.asyncio
async def test_split_by_segment():
    json_file = os.path.join(os.path.dirname(__file__), 'search_result.json')
    with open(json_file, 'r') as f:
        json_data = orjson.loads(f.read())
        # result = crawler_callback_services.split_by_segment(json_data=json_data)
        # logger.debug(result)
    split_rs = await crawler_callback_services.split_by_segment(data=json_data)
    logger.debug(split_rs)
    with open(os.path.join(os.path.dirname(__file__), 'split_rs.json'), 'w') as f:
        f.write(orjson.dumps(split_rs, option=orjson.OPT_INDENT_2).decode('utf-8'))

    merge_rs = crawler_callback_services.merge_results(datas=split_rs)
    logger.debug(merge_rs)
    with open(os.path.join(os.path.dirname(__file__), 'merge_rs.json'), 'w') as f:
        f.write(orjson.dumps(merge_rs, option=orjson.OPT_INDENT_2).decode('utf-8'))
    # assert False


# def test_merge_results():
#     json_file = os.path.join(os.path.dirname(__file__), 'cache_data.json')
#     with open(json_file, 'r') as f:
#         json_data = orjson.loads(f.read())
#     rs = crawler_callback_services.merge_results(datas=json_data)
#     logger.debug(rs)
#     assert False
