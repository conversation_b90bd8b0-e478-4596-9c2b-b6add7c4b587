from app.services import public_services


def test_fare_fuse():
    result = [
        {
            "flight_info": {
                "airline_code": "ZE",
                "trip_type": "ow",
                "expire_seconds": 292,
                "flight_no": "ZE886",
                "dep_airport_code": "TPE",
                "arr_airport_code": "CJU",
                "dep_date": "2025-02-03",
                "dep_time": "03:05",
                "arr_date": "2025-02-03",
                "arr_time": "06:05",
                "segments": [
                    {
                        "segment_index": 0,
                        "airline_code": "ZE",
                        "flight_no": "ZE886",
                        "dep_airport_code": "TPE",
                        "arr_airport_code": "CJU",
                        "dep_date": "2025-02-03",
                        "dep_time": "03:05",
                        "arr_date": "2025-02-03",
                        "arr_time": "06:05",
                        "share_code": False,
                        "aircraft_code": "B38M",
                        "stop_times": 0,
                        "stops": [],
                    }
                ],
            },
            "products": [
                {
                    "fare_type": "normal",
                    "fare_key": "VFBFX0NKVV8yMDI1LTAyLTAzX1pFODg2X1lfRV9LUlc=#bm9ybWFsXzEz",
                    "cabin": "Y",
                    "cabin_class": "E",
                    "adult": {
                        "base": 249,
                        "tax": 315,
                        "total": 564,
                        "quantity": 99,
                        "src_cny_base": 249,
                        "src_cny_tax": 315,
                        "src_cny_total": 564,
                        "src_base": 49000,
                        "src_tax": 62100,
                        "src_total": 111100,
                        "src_currency": "KRW",
                    },
                    "includes": {},
                    "add_ons": [],
                    "baggages": [],
                }
            ],
        }
    ]
    new_result = public_services.fare_fuse(result)
    assert len(new_result) == 1

    result[0]['products'][0]['adult']['src_cny_total'] = 180
    new_result = public_services.fare_fuse(result)
    assert len(new_result) == 0

    result[0]['products'][0]['adult']['src_cny_total'] = 564
    result[0]['products'][0]['adult']['total'] = 360
    new_result = public_services.fare_fuse(result)
    assert len(new_result) == 0
