from datetime import datetime
from loguru import logger
import orjson
import pytest
from app import tasks
from app.services import tb_service


@pytest.mark.asyncio
async def test_tb_fetch():
    data = {
        "cookies": {
            "cancelledSubSites": "empty",
            "cna": "kT2IHXu+NwsCAd3Ykksd9LJX",
            "lgc": "",
            "hng": "CN%7Czh-CN%7CCNY%7C156",
            "wk_cookie2": "169a40080d92b9d0d1393c1edf3b3ff7",
            "cookie2": "15fc61594188e8ffa336bbadd11d95bd",
            "t": "13187fe0dbfa3dc57193c78c9e586cf8",
            "_tb_token_": "7baebe857d687",
            "sk": "sai=UUjZelQI88InCh9iuz%2ByuA%3D%3D&n=0vR4jdyhvH7pN6QY&sae=F59coDuupyuGRCI7BEnyejBgF7jG&agf=UtIC5a4Dmqq6XifRpgt7sh2upgyEKXRpj%2FUj%2BHARMAk%3D&ss=Tmc%3D&fg=UUkHJwev1eDoipOHaw%3D%3D&agt=Ug%3D%3D&i=Vvj4Qg%3D%3D&fn=0vR4jdyhvH6fJ1RPs6x1PpcGcRI%3D&ln=0vR4jdyhvH7pN6QY",
            "tal": "Vw%3D%3D",
            "ck": "uid=UUpgTsA0Fo%2B0zK6vyg%3D%3D",
            "_m_h5_c": "4e30ef717e9184195a02077fc65f80ba_1732000667574%3B8fc1207ef8a76e14b2b2b173909ff8c5",
            "sty": "2",
            "sn": "",
            "dnk": "%5Cu5317%5Cu4EAC%5Cu6C47%5Cu6E38%5Cu5546%5Cu65C5",
            "tracknick": "%5Cu5317%5Cu4EAC%5Cu6C47%5Cu6E38%5Cu5546%5Cu65C5",
            "lid": "%E5%8C%97%E4%BA%AC%E6%B1%87%E6%B8%B8%E5%95%86%E6%97%85",
            "wk_unb": "UUpgTsA0Fo%2B0zK6vyg%3D%3D",
            "_l_g_": "Ug%3D%3D",
            "unb": "2218944205835",
            "cookie1": "BYfqW1grPUb0LBKUgPTBdknVqTShVu2FhraGuTqrTvE%3D",
            "login": "true",
            "cookie17": "UUpgTsA0Fo%2B0zK6vyg%3D%3D",
            "_nk_": "%5Cu5317%5Cu4EAC%5Cu6C47%5Cu6E38%5Cu5546%5Cu65C5",
            "sg": "%E6%97%855d",
            "uc1": "cookie15=W5iHLLyFOGW7aA%3D%3D&pas=0&existShop=true&cookie16=W5iHLLyFPlMGbLDwA%2BdvAGZqLg%3D%3D&cookie14=UoYdWtKJZqXZKg%3D%3D&cookie21=WqG3DMC9EmWL&tmb=1",
            "sgcookie": "E100NA0Tw0ZRw%2Fe09MnOND3%2FDKqYCsyoHhQZnDUKp%2BuT6RGJBFVaMxu%2B8paPNPXeF2vq%2BrpvygABCTrPdSvs%2Fpoicn39Z3PYsi40XeKuXjUQypPvrDJ773iBsLDLweNpV4PY",
            "csg": "f9b893ed",
            "xlly_s": "1",
            "tfstk": "gekt0-MJDHIOFKwkIdO3mrkTjbKn6xnwdVo5nr4GG23KS4K4SmqglX3-Rc0iI1cLk2gL5cxwgqhxqmz6bAxZMoMmccXwbqfAY40QofVmciEj-RNqsr4gk-nqyHYkELmZb-yWrUvo6I7OT8rf11_0O6ZbHlGuWHvqb-yX-RW2BwiZPp72WfwXvJZ0fowbfPtLdor0hONfC6_Q8owbh56bdkZgXs1_hOtKAyrbhrgbhUOWXrj_zthRKmz2owLK1DHLX5USvQSc0gqFrPi_JtT7WnVQPK4dhtMLbDmy4qW6eyGiGA2ICe_Td0ln5keJBartO03LZvbhn8GrJxFSPnfax00tH70MdOUqtmMg9-pFYPGrtVMQ3iC_Ibmo37M6Baz3ao3LZfLRB8GrjRcA4zHoeUlNrzEc1HKdgsP_Y1MYpaZTyYK4vzxHts549krLrHLGgsP_YkUkYV1VgWEF.",
            "isg": "BDg4XojoqxT26scl1dDpCNB0CeDKoZwrG2LARnKqv3EcjdR3KLBvuknvQYU93VQD",
        },
        "headers": {
            "authority": "saas2.flight.fliggy.com",
            "accept": "application/json",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "content-type": "application/json;charset=UTF-8",
            "dnt": "1",
            "origin": "https://saas2.flight.fliggy.com",
            "pragma": "no-cache",
            "referer": "https://saas2.flight.fliggy.com/",
            "sec-ch-ua": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
        },
        'airline_code': 'VZ',
        # 'dep_city_code': "BJS",
        # 'arr_city_code': "BKK",
        # 'dep_date': '2025-02-01',
        'dep_city_code': "BKK",
        'arr_city_code': "CNX",
        'dep_date': '2025-02-19',
        'batch_no': datetime.now().strftime('%Y%m%d_%H%M%S'),
    }
    logger.debug('开始测试')
    await tasks.tb_fetch_task(data)
    assert False


def test_tb_format():
    result = None
    with open('./tests/tb_tests/tb_search.json', 'r') as f:
        result = orjson.loads(f.read())
    batch_no = datetime.now().strftime('%Y%m%d_%H%M%S')
    rows = tb_service.format_search_result(batch_no=batch_no, result=result)
    tb_service.print_es_logs(rows)
    assert result
