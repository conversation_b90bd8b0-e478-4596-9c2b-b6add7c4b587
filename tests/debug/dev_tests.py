from app import tasks


def test_send_task():
    params = {
        "callback_url": "http://192.168.1.158:9529/api/v1/flight_fare/crawler/callback/verify/book/result",
        "order_no": "20250213171301327",
        "mock_pnr": "N6BI5Y",
        "airline_code": "VZ",
        "dep_airport_code": "BKK",
        "arr_airport_code": "CEI",
        "dep_date": "2025-02-16",
        "flight_no": "VZ130",
        "adult": 1,
        "child": 0,
        "infant": 0,
        "currency_code": "THB",
        "src_adult_base": 1260.0,
        "src_adult_tax": 450.4,
        "passengers": [
            {
                "name": "yang/yang",
                "last_name": "yang",
                "first_name": "yang",
                "birthday": "2000-01-01",
                "sex": "male",
                "passenger_type": "adult",
                "country": "CN",
                "card_no": "E23230975",
                "card_valid_date": "2028-12-31",
                "card_country": "CN",
            }
        ],
    }
    task_name = f'vz_verify_book_task'.lower()
    celery_task_id = tasks.crawler_celery_tasks[task_name].apply_async(args=(params,))
