import pytest
from unittest.mock import AsyncMock, MagicMock
from commons.consts.api_codes import ApiCodes


@pytest.mark.asyncio
async def test_verify_order_finish():
    """测试verify_order_finish接口"""
    # 准备测试数据
    mock_pnr = "ABCDEF"
    
    # 创建一个模拟的VerifyTmpOrder类
    mock_verify_tmp_order = MagicMock()
    mock_verify_tmp_order.update_by_async = AsyncMock()
    mock_verify_tmp_order.mock_pnr = MagicMock()
    
    # 创建一个模拟的verify_order_finish函数
    async def mock_verify_order_finish(item, client_ip):
        # 验证请求参数
        assert item.mock_pnr == mock_pnr
        
        # 模拟更新数据库
        await mock_verify_tmp_order.update_by_async(
            mock_verify_tmp_order.mock_pnr == item.mock_pnr,
            is_finish=1
        )
        
        # 返回成功响应
        return {
            "code": ApiCodes.SUCCESS.value,
            "data": True
        }
    
    # 模拟请求对象
    mock_request = MagicMock()
    mock_request.mock_pnr = mock_pnr
    
    # 调用模拟的verify_order_finish函数
    response = await mock_verify_order_finish(mock_request, "127.0.0.1")
    
    # 验证结果
    assert response["code"] == ApiCodes.SUCCESS.value
    assert response["data"] is True
    
    # 验证update_by_async被正确调用
    mock_verify_tmp_order.update_by_async.assert_called_once()
    
    # 验证is_finish被设置为1
    args, kwargs = mock_verify_tmp_order.update_by_async.call_args
    assert kwargs["is_finish"] == 1
