import pytest
from unittest.mock import AsyncMock, MagicMock
from commons.consts.api_codes import ApiCodes


@pytest.mark.asyncio
async def test_verify_order_create_success():
    """测试verify_order_create函数成功情况下的实时验价"""
    # 准备测试数据
    params = {
        "fare_key": "VJ-SGN-HAN-20250520-NORMAL-VND-1000000-100000",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "flight_no": "VJ123",
        "adult": 2,
        "child": 1,
        "infant": 0,
        "mock_pnr": "ABCDEF",
    }

    # 创建一个模拟的public_services
    mock_public_services = MagicMock()
    mock_public_services.verify_create_order = AsyncMock(return_value={"order_no": "ORD123456"})
    mock_public_services.verify_real_time = AsyncMock()

    # 创建一个模拟的logger
    mock_logger = MagicMock()

    # 创建一个模拟的verify_order_create函数
    async def mock_verify_order_create(item, client_ip):
        # 验证请求参数
        assert item.model_dump() == params

        # 模拟调用verify_create_order
        result = await mock_public_services.verify_create_order(params=params)

        # 模拟调用verify_real_time（使用adult=1）
        try:
            await mock_public_services.verify_real_time(
                params={
                    'fare_key': params['fare_key'],
                    'dep_airport_code': params['dep_airport_code'],
                    'arr_airport_code': params['arr_airport_code'],
                    'flight_no': params['flight_no'],
                    'adult': 1,
                    'child': 0,
                    'infant': 0,
                    'keep_time': 1,
                },
                mock_pnr=params['mock_pnr'],
            )
        except Exception as e:
            mock_logger.error(e)

        # 返回成功响应
        return {"code": ApiCodes.SUCCESS.value, "data": result}

    # 模拟请求对象
    mock_item = MagicMock()
    mock_item.model_dump.return_value = params

    # 调用模拟的verify_order_create函数
    response = await mock_verify_order_create(mock_item, "127.0.0.1")

    # 验证结果
    assert response["code"] == ApiCodes.SUCCESS.value
    assert response["data"] == {"order_no": "ORD123456"}

    # 验证verify_create_order被正确调用
    mock_public_services.verify_create_order.assert_called_once_with(params=params)

    # 验证verify_real_time被正确调用（使用adult=1）
    mock_public_services.verify_real_time.assert_called_once()
    _, kwargs = mock_public_services.verify_real_time.call_args
    assert kwargs["params"]["adult"] == 1
    assert kwargs["params"]["child"] == 0
    assert kwargs["params"]["infant"] == 0
    assert kwargs["params"]["keep_time"] == 1
    assert kwargs["mock_pnr"] == "ABCDEF"


@pytest.mark.asyncio
async def test_verify_order_create_failure():
    """测试verify_order_create函数失败情况下的实时验价"""
    # 准备测试数据
    params = {
        "fare_key": "VJ-SGN-HAN-20250520-NORMAL-VND-1000000-100000",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "flight_no": "VJ123",
        "adult": 2,
        "child": 1,
        "infant": 0,
        "mock_pnr": "ABCDEF",
    }

    # 创建一个模拟的public_services
    mock_public_services = MagicMock()
    mock_public_services.verify_create_order = AsyncMock(side_effect=Exception("Test error"))
    mock_public_services.verify_real_time = AsyncMock()

    # 创建一个模拟的logger
    mock_logger = MagicMock()

    # 创建一个模拟的verify_order_create函数
    async def mock_verify_order_create(item, client_ip):
        # 验证请求参数
        assert item.model_dump() == params

        # 模拟调用verify_create_order（会抛出异常）
        try:
            result = await mock_public_services.verify_create_order(params=params)
        except Exception as e:
            # 模拟调用verify_real_time（使用原始的adult、child、infant值）
            try:
                await mock_public_services.verify_real_time(
                    params={
                        'fare_key': params['fare_key'],
                        'dep_airport_code': params['dep_airport_code'],
                        'arr_airport_code': params['arr_airport_code'],
                        'flight_no': params['flight_no'],
                        'adult': params['adult'],
                        'child': params['child'],
                        'infant': params['infant'],
                        'keep_time': 1,
                    },
                    mock_pnr=params['mock_pnr'],
                )
            except Exception as e2:
                mock_logger.error(e2)

            # 重新抛出原始异常
            raise e

    # 模拟请求对象
    mock_item = MagicMock()
    mock_item.model_dump.return_value = params

    # 调用模拟的verify_order_create函数，预期会抛出异常
    with pytest.raises(Exception) as excinfo:
        await mock_verify_order_create(mock_item, "127.0.0.1")

    # 验证异常信息
    assert str(excinfo.value) == "Test error"

    # 验证verify_create_order被正确调用
    mock_public_services.verify_create_order.assert_called_once_with(params=params)

    # 验证verify_real_time被正确调用（使用原始的adult、child、infant值）
    mock_public_services.verify_real_time.assert_called_once()
    _, kwargs = mock_public_services.verify_real_time.call_args
    assert kwargs["params"]["adult"] == 2
    assert kwargs["params"]["child"] == 1
    assert kwargs["params"]["infant"] == 0
    assert kwargs["params"]["keep_time"] == 1
    assert kwargs["mock_pnr"] == "ABCDEF"
