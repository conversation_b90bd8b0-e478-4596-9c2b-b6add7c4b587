import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from app.services.mongo_services import FlightFareMongoService


@pytest.mark.asyncio
async def test_delete_cache_with_flight_nos():
    """测试使用flight_nos参数删除缓存"""
    # 准备测试数据
    collection_name = "test_collection"
    site_code = "airline_vj"
    dep_airport_code = "SGN"
    arr_airport_code = "HAN"
    dep_date = "2025-05-20"
    return_date = ""
    flight_nos = ["VJ123", "VJ456"]
    
    # 创建模拟对象
    mock_collection = AsyncMock()
    mock_db = MagicMock()
    mock_db.__getitem__.return_value = mock_collection
    
    # 创建服务实例并替换db属性
    service = FlightFareMongoService("test_db")
    service.db = mock_db
    
    # 调用被测试方法
    await service.delete_cache(
        collection_name=collection_name,
        site_code=site_code,
        dep_airport_code=dep_airport_code,
        arr_airport_code=arr_airport_code,
        dep_date=dep_date,
        return_date=return_date,
        flight_nos=flight_nos
    )
    
    # 验证查询条件
    expected_query = {
        'task_info.site_code': site_code,
        'task_info.dep_airport_code': dep_airport_code,
        'task_info.arr_airport_code': arr_airport_code,
        'task_info.dep_date': dep_date,
        'task_info.return_date': return_date,
        'result.flight_no': {'$nin': flight_nos}
    }
    
    # 验证delete_many被正确调用
    mock_collection.delete_many.assert_called_once_with(expected_query)


@pytest.mark.asyncio
async def test_delete_cache_with_invalid_flight_nos():
    """测试使用invalid_flight_nos参数删除缓存"""
    # 准备测试数据
    collection_name = "test_collection"
    site_code = "airline_vj"
    dep_airport_code = "SGN"
    arr_airport_code = "HAN"
    dep_date = "2025-05-20"
    return_date = ""
    invalid_flight_nos = ["VJ123", "VJ456"]
    
    # 创建模拟对象
    mock_collection = AsyncMock()
    mock_db = MagicMock()
    mock_db.__getitem__.return_value = mock_collection
    
    # 创建服务实例并替换db属性
    service = FlightFareMongoService("test_db")
    service.db = mock_db
    
    # 调用被测试方法
    await service.delete_cache(
        collection_name=collection_name,
        site_code=site_code,
        dep_airport_code=dep_airport_code,
        arr_airport_code=arr_airport_code,
        dep_date=dep_date,
        return_date=return_date,
        invalid_flight_nos=invalid_flight_nos
    )
    
    # 验证查询条件
    expected_query = {
        'task_info.site_code': site_code,
        'task_info.dep_airport_code': dep_airport_code,
        'task_info.arr_airport_code': arr_airport_code,
        'task_info.dep_date': dep_date,
        'task_info.return_date': return_date,
        'result.flight_no': {'$in': invalid_flight_nos}
    }
    
    # 验证delete_many被正确调用
    mock_collection.delete_many.assert_called_once_with(expected_query)


@pytest.mark.asyncio
async def test_delete_cache_with_both_flight_nos_params():
    """测试同时使用flight_nos和invalid_flight_nos参数删除缓存"""
    # 准备测试数据
    collection_name = "test_collection"
    site_code = "airline_vj"
    dep_airport_code = "SGN"
    arr_airport_code = "HAN"
    dep_date = "2025-05-20"
    return_date = ""
    flight_nos = ["VJ123", "VJ456"]
    invalid_flight_nos = ["VJ789"]
    
    # 创建模拟对象
    mock_collection = AsyncMock()
    mock_db = MagicMock()
    mock_db.__getitem__.return_value = mock_collection
    
    # 创建服务实例并替换db属性
    service = FlightFareMongoService("test_db")
    service.db = mock_db
    
    # 调用被测试方法
    await service.delete_cache(
        collection_name=collection_name,
        site_code=site_code,
        dep_airport_code=dep_airport_code,
        arr_airport_code=arr_airport_code,
        dep_date=dep_date,
        return_date=return_date,
        flight_nos=flight_nos,
        invalid_flight_nos=invalid_flight_nos
    )
    
    # 验证查询条件 - 应该使用invalid_flight_nos
    expected_query = {
        'task_info.site_code': site_code,
        'task_info.dep_airport_code': dep_airport_code,
        'task_info.arr_airport_code': arr_airport_code,
        'task_info.dep_date': dep_date,
        'task_info.return_date': return_date,
        'result.flight_no': {'$in': invalid_flight_nos}
    }
    
    # 验证delete_many被正确调用
    mock_collection.delete_many.assert_called_once_with(expected_query)
