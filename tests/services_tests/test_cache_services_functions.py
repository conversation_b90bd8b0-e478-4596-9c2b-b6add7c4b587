import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from app.services.cache_services import (
    update_cache_from_verify_result,
    clear_cache_for_error,
    calculate_adjusted_expire_time,
)
from commons.consts.api_codes import ApiCodes


@pytest.mark.asyncio
@patch('app.services.crawler_callback_services.split_by_segment')
@patch('app.services.crawler_callback_services.check_low_quantity')
@patch('app.services.crawler_callback_services.coumpute_expire_time')
@patch('app.services.crawler_callback_services.apply_shopping_push')
@patch('app.services.crawler_callback_services.cache_fuse')
@patch('app.services.fetch_rule_services.get_rule')
@patch('app.services.es_service.write_price_log')
@patch('app.services.task_services.set_cache_expire')
@patch('app.services.cache_services.FlightFareMongoService')
async def test_update_cache_from_verify_result_success(
    mock_flight_fare_mongo_class,
    mock_set_cache_expire,
    mock_write_price_log,
    mock_get_rule,
    mock_cache_fuse,
    mock_apply_shopping_push,
    mock_coumpute_expire_time,
    mock_check_low_quantity,
    mock_split_by_segment,
):
    """测试成功情况下更新缓存"""
    # 准备测试数据
    search_result = {
        "task_info": {
            "airline_code": "VJ",
            "dep_airport_code": "SGN",
            "arr_airport_code": "HAN",
            "dep_date": "2025-05-20",
            "return_date": "",
            "site_code": "airline_vj",
            "task_key": "VJ-SGN-HAN-2025-05-20",
            "expire_seconds": 600,
        },
        "error": {"code": ApiCodes.SUCCESS.value, "message": "success"},
        "data": {
            "results": [{"trips": [{"segments": [{"flight_no": "VJ123"}]}]}],
            "exchange": {"currency_code": "VND", "rate": 3300},
        },
    }

    # 模拟FlightFareMongoService
    mock_mongo_instance = MagicMock()
    mock_mongo_instance.bulk_update_cache = AsyncMock()
    mock_mongo_instance.delete_cache = AsyncMock()
    mock_flight_fare_mongo_class.return_value = mock_mongo_instance

    # 模拟fetch_rule_services.get_rule
    mock_get_rule.return_value = {"id": 1, "cache_type": 1}

    # 模拟task_services.set_cache_expire
    mock_set_cache_expire.return_value = None

    # 模拟crawler_callback_services.cache_fuse
    mock_cache_fuse.return_value = search_result

    # 模拟crawler_callback_services.split_by_segment
    split_data = {
        "task_info": search_result["task_info"],
        "result": {"flight_no": "VJ123", "fares": {"adult": {"quantity": 10}}},
    }
    mock_split_by_segment.return_value = [split_data]

    # 模拟crawler_callback_services.check_low_quantity
    mock_check_low_quantity.return_value = split_data

    # 模拟crawler_callback_services.coumpute_expire_time
    mock_coumpute_expire_time.return_value = (split_data, 300)

    # 模拟crawler_callback_services.apply_shopping_push
    mock_apply_shopping_push.return_value = None

    # 调用被测试方法
    valid_flight_nos, real_expire_seconds = await update_cache_from_verify_result(
        search_result=search_result, collection_name="airline_fare_cache"
    )

    # 验证结果
    assert valid_flight_nos == ["VJ123"]
    assert real_expire_seconds == 300

    # 验证数据库和外部API的mock调用
    mock_flight_fare_mongo_class.assert_called_once_with('flight_fare')
    mock_get_rule.assert_called_once()
    mock_write_price_log.assert_called_once()
    mock_cache_fuse.assert_called_once()
    mock_split_by_segment.assert_called_once()
    mock_check_low_quantity.assert_called_once()
    mock_coumpute_expire_time.assert_called_once()
    mock_set_cache_expire.assert_called_once()
    mock_mongo_instance.bulk_update_cache.assert_called_once()
    mock_mongo_instance.delete_cache.assert_called_once()
    mock_apply_shopping_push.assert_called_once()


@pytest.mark.asyncio
@patch('app.services.fetch_rule_services.get_rule')
@patch('app.services.task_services.set_cache_expire')
@patch('app.services.cache_services.FlightFareMongoService')
async def test_update_cache_from_verify_result_error(
    mock_flight_fare_mongo_class, mock_set_cache_expire, mock_get_rule
):
    """测试错误情况下更新缓存"""
    # 准备测试数据
    search_result = {
        "task_info": {
            "airline_code": "VJ",
            "dep_airport_code": "SGN",
            "arr_airport_code": "HAN",
            "dep_date": "2025-05-20",
            "return_date": "",
            "site_code": "airline_vj",
            "task_key": "VJ-SGN-HAN-2025-05-20",
            "expire_seconds": 600,
        },
        "error": {"code": ApiCodes.SUCCESS.value, "message": "success"},
        "data": {"results": []},
    }

    # 模拟FlightFareMongoService
    mock_mongo_instance = MagicMock()
    mock_mongo_instance.delete_cache = AsyncMock()
    mock_flight_fare_mongo_class.return_value = mock_mongo_instance

    # 模拟fetch_rule_services.get_rule抛出异常
    mock_get_rule.side_effect = Exception("Test exception")

    # 模拟task_services.set_cache_expire
    mock_set_cache_expire.return_value = None

    # 调用被测试方法
    valid_flight_nos, real_expire_seconds = await update_cache_from_verify_result(
        search_result=search_result, collection_name="airline_fare_cache"
    )

    # 验证结果
    assert valid_flight_nos == []
    assert real_expire_seconds == 600  # 由于异常处理逻辑，返回task_info中的expire_seconds


@pytest.mark.asyncio
@patch('app.services.task_services.del_task_key')
@patch('app.services.cache_services.FlightFareMongoService')
async def test_clear_cache_for_error_book_ticket_not_enough(mock_flight_fare_mongo_class, mock_del_task_key):
    """测试BOOK_TICKET_NOT_ENOUGH错误情况下清除缓存"""
    # 准备测试数据
    task_info = {
        "airline_code": "VJ",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "return_date": "",
        "flight_no": "VJ123",
    }
    error_code = ApiCodes.BOOK_TICKET_NOT_ENOUGH.value

    # 模拟FlightFareMongoService
    mock_mongo_instance = MagicMock()
    mock_mongo_instance.delete_cache = AsyncMock()
    mock_flight_fare_mongo_class.return_value = mock_mongo_instance

    # 模拟task_services.del_task_key
    mock_del_task_key.return_value = None

    # 调用被测试方法
    result = await clear_cache_for_error(task_info=task_info, error_code=error_code)

    # 验证结果
    assert result is True


@pytest.mark.asyncio
@patch('app.services.task_services.del_task_key')
@patch('app.services.cache_services.FlightFareMongoService')
async def test_clear_cache_for_error_other_error(mock_flight_fare_mongo_class, mock_del_task_key):
    """测试其他错误情况下不清除缓存"""
    # 准备测试数据
    task_info = {
        "airline_code": "VJ",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "return_date": "",
        "flight_no": "VJ123",
    }
    error_code = ApiCodes.UNKNOWN.value

    # 调用被测试方法
    result = await clear_cache_for_error(task_info=task_info, error_code=error_code)

    # 验证结果
    assert result is False

    # 验证方法调用
    mock_flight_fare_mongo_class.assert_not_called()
    mock_del_task_key.assert_not_called()


@patch('app.services.cache_services.datetime')
def test_calculate_adjusted_expire_time_less_than_60_minutes(mock_datetime):
    """测试过期时间小于60分钟的情况"""
    # 准备测试数据
    now = datetime(2025, 5, 15, 12, 0, 0)
    mock_datetime.now.return_value = now
    mock_datetime.strptime = datetime.strptime

    original_expire_time = (now + timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")

    # 调用被测试方法
    adjusted_expire_time = calculate_adjusted_expire_time(original_expire_time)

    # 验证结果
    expected_expire_time = (now + timedelta(minutes=15)).strftime("%Y-%m-%d %H:%M:%S")
    assert adjusted_expire_time == expected_expire_time


@patch('app.services.cache_services.datetime')
def test_calculate_adjusted_expire_time_more_than_60_minutes(mock_datetime):
    """测试过期时间大于60分钟的情况"""
    # 准备测试数据
    now = datetime(2025, 5, 15, 12, 0, 0)
    mock_datetime.now.return_value = now
    mock_datetime.strptime = datetime.strptime

    original_expire_time = (now + timedelta(minutes=90)).strftime("%Y-%m-%d %H:%M:%S")

    # 调用被测试方法
    adjusted_expire_time = calculate_adjusted_expire_time(original_expire_time)

    # 验证结果
    expected_expire_time = (now + timedelta(minutes=60)).strftime("%Y-%m-%d %H:%M:%S")
    assert adjusted_expire_time == expected_expire_time
