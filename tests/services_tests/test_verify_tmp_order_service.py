import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
from app.consts.types import FareType
from commons.consts.api_codes import ApiCodes
import json


@pytest.mark.asyncio
async def test_cumpute_order_passenger_num_with_is_finish():
    """测试cumpute_order_passenger_num函数，验证is_finish条件"""
    # 在测试方法内部导入依赖，避免循环引用
    from app.services.verify_tmp_order_service import cumpute_order_passenger_num

    # 准备测试数据
    mock_pnr = "ABCDEF"
    result = [
        {
            "flight_info": {
                "dep_airport_code": "SGN",
                "arr_airport_code": "HAN",
                "dep_date": "2025-05-20",
                "flight_no": "VJ123",
            }
        }
    ]

    # 创建两个临时订单，一个已完成，一个未完成
    tmp_order_finished = {
        "id": 1,
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "flight_no": "VJ123",
        "created": "2025-05-15 11:00:00",
        "mock_pnr": "GHIJKL",
        "code": ApiCodes.SUCCESS.value,
        "is_finish": 1,  # 已完成
        "order_info": '{"fare_key":"VJ-SGN-HAN-20250520-NORMAL-VND-1000000-100000","mock_pnr":"GHIJKL","src_adult_base":1000000,"passengers":[{"name":"Test User 1"},{"name":"Test User 2"}]}',
    }

    tmp_order_unfinished = {
        "id": 2,
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "flight_no": "VJ123",
        "created": "2025-05-15 11:00:00",
        "mock_pnr": "MNOPQR",
        "code": ApiCodes.FARE_VERIFY_STOP_BOOK.value,  # 非成功状态
        "is_finish": 0,  # 未完成
        "order_info": '{"fare_key":"VJ-SGN-HAN-20250520-NORMAL-VND-1000000-100000","mock_pnr":"MNOPQR","src_adult_base":1000000,"passengers":[{"name":"Test User 3"},{"name":"Test User 4"}]}',
    }

    # 使用patch装饰器模拟依赖
    with patch('app.services.verify_tmp_order_service.VerifyTmpOrder') as mock_verify_tmp_order, patch(
        'app.services.verify_tmp_order_service.datetime'
    ) as mock_datetime, patch('app.services.verify_tmp_order_service.public_services') as mock_public_services, patch(
        'app.services.verify_tmp_order_service.orjson'
    ) as mock_orjson:

        # 模拟public_services.decode_fare_key
        mock_public_services.decode_fare_key = MagicMock(
            side_effect=[{"fare_type": FareType.NORMAL.value}, {"fare_type": FareType.NORMAL.value}]
        )

        # 模拟datetime.now()和datetime.strptime()
        mock_now = MagicMock()
        mock_datetime.now.return_value = mock_now
        mock_datetime.strptime.side_effect = lambda date_str, fmt: datetime.strptime(date_str, fmt)

        # 模拟datetime.now().date()
        mock_date = MagicMock()
        mock_date.__str__.return_value = "2025-05-15"
        mock_now.date.return_value = mock_date

        # 模拟datetime.now() - datetime.strptime()的结果
        mock_timedelta = MagicMock()
        mock_timedelta.total_seconds.return_value = 10  # 10秒，小于TB_TEMP_ORDER_LOCK_TIME * 60
        mock_now.__sub__.return_value = mock_timedelta

        # 模拟settings.TB_TEMP_ORDER_LOCK_TIME
        with patch('app.services.verify_tmp_order_service.settings') as mock_settings:
            mock_settings.TB_TEMP_ORDER_LOCK_TIME = 60  # 60分钟

        # 模拟VerifyTmpOrder.get_all_async
        mock_verify_tmp_order.get_all_async = AsyncMock(return_value=[tmp_order_finished, tmp_order_unfinished])

        # 模拟SQL查询条件
        mock_verify_tmp_order.dep_airport_code = MagicMock()
        mock_verify_tmp_order.arr_airport_code = MagicMock()
        mock_verify_tmp_order.dep_date = MagicMock()
        mock_verify_tmp_order.created = MagicMock()
        mock_verify_tmp_order.created.__gt__ = MagicMock()
        mock_verify_tmp_order.mock_pnr = MagicMock()
        mock_verify_tmp_order.mock_pnr.__ne__ = MagicMock()
        mock_verify_tmp_order.is_finish = MagicMock()

        # 模拟orjson.loads
        def mock_loads(s):
            return json.loads(s)

        mock_orjson.loads.side_effect = mock_loads

        # 调用被测试函数
        p_map = await cumpute_order_passenger_num(result, mock_pnr)

        # 验证结果
        # 只有未完成的订单应该被计算
        assert len(p_map) == 1
        key = f"SGN_HAN_2025-05-20_VJ123_1000000_{FareType.NORMAL.value}"
        assert p_map[key] == 2  # 未完成订单有2个乘客

        # 验证VerifyTmpOrder.get_all_async被正确调用
        mock_verify_tmp_order.get_all_async.assert_called_once()

        # 验证is_finish == 0条件被包含在查询中
        mock_verify_tmp_order.is_finish.__eq__.assert_called_with(0)


@pytest.mark.asyncio
async def test_deduct_ticket_by_temp_order():
    """测试deduct_ticket_by_temp_order函数"""
    # 在测试方法内部导入依赖，避免循环引用
    from app.services.verify_tmp_order_service import deduct_ticket_by_temp_order

    # 准备测试数据
    mock_pnr = "ABCDEF"
    result = [
        {
            "flight_info": {
                "dep_airport_code": "SGN",
                "arr_airport_code": "HAN",
                "dep_date": "2025-05-20",
                "flight_no": "VJ123",
            },
            "products": [
                {
                    "adult": {"quantity": 5, "src_base": 1000000},
                    "child": {"quantity": 3},
                    "infant": {"quantity": 2},
                    "fare_type": FareType.NORMAL.value,
                }
            ],
        }
    ]

    # 使用patch装饰器模拟依赖
    with patch('app.services.verify_tmp_order_service.cumpute_order_passenger_num') as mock_cumpute_order_passenger_num:
        # 模拟cumpute_order_passenger_num返回值
        p_key = f"SGN_HAN_2025-05-20_VJ123_1000000_{FareType.NORMAL.value}"
        mock_cumpute_order_passenger_num.return_value = {p_key: 2}

        # 调用被测试函数
        new_result = await deduct_ticket_by_temp_order(result, mock_pnr)

        # 验证结果
        assert len(new_result) == 1
        assert new_result[0]["products"][0]["adult"]["quantity"] == 3  # 5 - 2 = 3
        assert new_result[0]["products"][0]["child"]["quantity"] == 1  # 3 - 2 = 1
        assert new_result[0]["products"][0]["infant"]["quantity"] == 0  # 2 - 2 = 0

        # 验证cumpute_order_passenger_num被正确调用
        mock_cumpute_order_passenger_num.assert_called_once_with(result, mock_pnr=mock_pnr)
