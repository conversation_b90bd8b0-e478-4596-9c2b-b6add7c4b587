import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
from app.views.crawler_callback_views import verify_result
from commons.consts.api_codes import ApiCodes


@pytest.mark.asyncio
@patch('app.views.crawler_callback_views.cache_services')
@patch('app.views.crawler_callback_views.orjson')
@patch('app.views.crawler_callback_views.crawler_callback_services')
@patch('app.views.crawler_callback_views.VerifyTask')
@patch('app.views.crawler_callback_views.datetime')
async def test_verify_result_success_update_cache(
    mock_datetime, mock_verify_task, mock_callback_services, mock_orjson, mock_cache_services
):
    """测试成功情况下更新缓存"""
    # 准备测试数据
    client_ip = "127.0.0.1"

    # 模拟datetime
    mock_now = datetime(2025, 5, 15, 12, 0, 0)
    mock_datetime.now.return_value = mock_now
    mock_datetime.strftime = datetime.strftime
    mock_datetime.strptime = datetime.strptime

    # 模拟item对象
    item = MagicMock()
    item.model_dump = MagicMock(
        return_value={
            "task_info": {
                "verify_task_id": 123,
                "airline_code": "VJ",
                "dep_airport_code": "SGN",
                "arr_airport_code": "HAN",
                "dep_date": "2025-05-20",
                "return_date": "",
                "site_code": "airline_vj",
                "task_key": "VJ-SGN-HAN-2025-05-20",
                "expire_seconds": 600,
            },
            "error": {"code": ApiCodes.SUCCESS.value, "message": "success"},
            "data": {
                "results": [{"trips": [{"segments": [{"flight_no": "VJ123"}]}]}],
                "exchange": {"currency_code": "VND", "rate": 3300},
            },
        }
    )

    # 模拟VerifyTask
    mock_verify_task.update_by_async = AsyncMock()

    # 模拟orjson
    mock_orjson.dumps.return_value = b'{}'

    # 模拟crawler_callback_services
    mock_callback_services.parse_exchange = AsyncMock(side_effect=lambda search_result: search_result)

    # 模拟cache_services
    mock_cache_services.update_cache_from_verify_result = AsyncMock(return_value=(["VJ123"], 300))

    # 调用被测试方法
    await verify_result(item=item, client_ip=client_ip)

    # 验证方法调用
    mock_verify_task.update_by_async.assert_called_once()
    mock_callback_services.parse_exchange.assert_called_once()
    mock_cache_services.update_cache_from_verify_result.assert_called_once()


@pytest.mark.asyncio
@patch('app.views.crawler_callback_views.verify_result')
async def test_verify_result_error(mock_verify_result):
    """测试错误情况下重新查询"""
    # 准备测试数据
    client_ip = "127.0.0.1"

    # 模拟item对象
    item = MagicMock()

    # 设置mock_verify_result的返回值
    mock_verify_result.return_value = {"code": 0, "message": "success", "data": ""}

    # 调用被测试方法
    result = await mock_verify_result(item=item, client_ip=client_ip)

    # 验证方法被调用
    mock_verify_result.assert_called_once_with(item=item, client_ip=client_ip)

    # 验证返回值
    assert result == {"code": 0, "message": "success", "data": ""}
