from datetime import datetime, timedelta
from unittest.mock import patch


def test_expire_time_calculation_less_than_60_minutes():
    """测试过期时间小于60分钟的情况"""
    # 准备测试数据
    now = datetime(2025, 5, 15, 12, 0, 0)
    expire_time = (now + timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")

    # 不再需要模拟item对象，因为我们直接测试calculate_adjusted_expire_time函数

    # 使用app.services.cache_services中的calculate_adjusted_expire_time函数来测试
    with patch('app.services.cache_services.datetime') as mock_datetime:
        mock_datetime.now.return_value = now
        mock_datetime.strptime = datetime.strptime

        from app.services.cache_services import calculate_adjusted_expire_time

        # 调用被测试方法
        adjusted_expire_time = calculate_adjusted_expire_time(expire_time)

        # 验证结果
        expected_expire_time = (now + timedelta(minutes=15)).strftime("%Y-%m-%d %H:%M:%S")
        assert adjusted_expire_time == expected_expire_time


def test_expire_time_calculation_more_than_60_minutes():
    """测试过期时间大于60分钟的情况"""
    # 准备测试数据
    now = datetime(2025, 5, 15, 12, 0, 0)
    expire_time = (now + timedelta(minutes=90)).strftime("%Y-%m-%d %H:%M:%S")

    # 使用app.services.cache_services中的calculate_adjusted_expire_time函数来测试
    with patch('app.services.cache_services.datetime') as mock_datetime:
        mock_datetime.now.return_value = now
        mock_datetime.strptime = datetime.strptime

        from app.services.cache_services import calculate_adjusted_expire_time

        # 调用被测试方法
        adjusted_expire_time = calculate_adjusted_expire_time(expire_time)

        # 验证结果
        expected_expire_time = (now + timedelta(minutes=60)).strftime("%Y-%m-%d %H:%M:%S")
        assert adjusted_expire_time == expected_expire_time
