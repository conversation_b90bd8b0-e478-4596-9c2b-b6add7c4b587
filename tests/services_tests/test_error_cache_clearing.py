import pytest
from unittest.mock import patch, MagicMock
from app.views.crawler_callback_views import verify_book_result
from commons.consts.api_codes import ApiCodes


@pytest.mark.asyncio
@patch('app.services.cache_services.clear_cache_for_error')
@patch('app.models.verify_tmp_order.VerifyTmpOrder.update_by_async')
@patch('app.models.verify_tmp_order.VerifyTmpOrder.get_by_async')
@patch('app.views.crawler_callback_views.orjson.dumps')
async def test_error_cache_clearing_book_ticket_not_enough(
    mock_orjson_dumps, mock_get_by_async, mock_update_by_async, mock_clear_cache_for_error
):
    """测试BOOK_TICKET_NOT_ENOUGH错误情况下的缓存清除"""
    # 准备测试数据
    order_no = "TEST123"
    mock_pnr = "MOCKPNR"
    unique_id = "test-unique-id"

    # 模拟item对象
    item = MagicMock()
    item.error = MagicMock()
    item.error.code = ApiCodes.BOOK_TICKET_NOT_ENOUGH.value
    item.error.message = "Ticket not enough"
    item.data = None
    item.task_info = {
        "airline_code": "VJ",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "return_date": "",
        "flight_no": "VJ123",
        "order_no": order_no,
        "mock_pnr": mock_pnr,
        "unique_id": unique_id,
    }

    # 模拟数据库操作
    mock_get_by_async.return_value = None
    mock_update_by_async.return_value = None

    # 模拟orjson.dumps
    mock_orjson_dumps.return_value = b'{}'

    # 模拟cache_services.clear_cache_for_error
    mock_clear_cache_for_error.return_value = True

    # 调用被测试方法
    await verify_book_result(item=item, client_ip="127.0.0.1")

    # 验证方法调用
    mock_update_by_async.assert_called_once()
    mock_clear_cache_for_error.assert_called_once_with(
        task_info=item.task_info, error_code=ApiCodes.BOOK_TICKET_NOT_ENOUGH.value
    )


@pytest.mark.asyncio
@patch('app.services.cache_services.clear_cache_for_error')
@patch('app.models.verify_tmp_order.VerifyTmpOrder.update_by_async')
@patch('app.models.verify_tmp_order.VerifyTmpOrder.get_by_async')
@patch('app.views.crawler_callback_views.orjson.dumps')
async def test_error_cache_clearing_book_price_error(
    mock_orjson_dumps, mock_get_by_async, mock_update_by_async, mock_clear_cache_for_error
):
    """测试BOOK_PRICE_ERROR错误情况下的缓存清除"""
    # 准备测试数据
    order_no = "TEST123"
    mock_pnr = "MOCKPNR"
    unique_id = "test-unique-id"

    # 模拟item对象
    item = MagicMock()
    item.error = MagicMock()
    item.error.code = ApiCodes.BOOK_PRICE_ERROR.value
    item.error.message = "Price error"
    item.data = None
    item.task_info = {
        "airline_code": "VJ",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "return_date": "",
        "flight_no": "VJ123",
        "order_no": order_no,
        "mock_pnr": mock_pnr,
        "unique_id": unique_id,
    }

    # 模拟数据库操作
    mock_get_by_async.return_value = None
    mock_update_by_async.return_value = None

    # 模拟orjson.dumps
    mock_orjson_dumps.return_value = b'{}'

    # 模拟cache_services.clear_cache_for_error
    mock_clear_cache_for_error.return_value = True

    # 调用被测试方法
    await verify_book_result(item=item, client_ip="127.0.0.1")

    # 验证方法调用
    mock_update_by_async.assert_called_once()
    mock_clear_cache_for_error.assert_called_once_with(
        task_info=item.task_info, error_code=ApiCodes.BOOK_PRICE_ERROR.value
    )


@pytest.mark.asyncio
@patch('app.services.cache_services.clear_cache_for_error')
@patch('app.models.verify_tmp_order.VerifyTmpOrder.update_by_async')
@patch('app.models.verify_tmp_order.VerifyTmpOrder.get_by_async')
@patch('app.views.crawler_callback_views.orjson.dumps')
async def test_error_cache_clearing_other_error(
    mock_orjson_dumps, mock_get_by_async, mock_update_by_async, mock_clear_cache_for_error
):
    """测试其他错误情况下不清除缓存"""
    # 准备测试数据
    order_no = "TEST123"
    mock_pnr = "MOCKPNR"
    unique_id = "test-unique-id"

    # 模拟item对象
    item = MagicMock()
    item.error = MagicMock()
    item.error.code = ApiCodes.UNKNOWN.value  # 其他错误
    item.error.message = "Unknown error"
    item.data = None
    item.task_info = {
        "airline_code": "VJ",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-20",
        "return_date": "",
        "flight_no": "VJ123",
        "order_no": order_no,
        "mock_pnr": mock_pnr,
        "unique_id": unique_id,
    }

    # 模拟数据库操作
    mock_get_by_async.return_value = None
    mock_update_by_async.return_value = None

    # 模拟orjson.dumps
    mock_orjson_dumps.return_value = b'{}'

    # 模拟cache_services.clear_cache_for_error
    mock_clear_cache_for_error.return_value = False

    # 调用被测试方法
    await verify_book_result(item=item, client_ip="127.0.0.1")

    # 验证方法调用
    mock_update_by_async.assert_called_once()
    mock_clear_cache_for_error.assert_called_once_with(task_info=item.task_info, error_code=ApiCodes.UNKNOWN.value)
