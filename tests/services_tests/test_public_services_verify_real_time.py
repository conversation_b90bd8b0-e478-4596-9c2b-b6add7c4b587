import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import json


@pytest.mark.asyncio
async def test_verify_real_time_passenger_count():
    """测试verify_real_time函数中的passenger_count参数"""
    # 准备测试数据
    params = {
        "fare_key": "VJ-SGN-HAN-20250520-NORMAL-VND-1000000-100000",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "flight_no": "VJ123",
        "adult": 2,
        "child": 1,
        "infant": 0,
        "keep_time": 60,
    }
    mock_pnr = "ABCDEF"

    # 创建一个模拟的VerifyTask类
    mock_verify_task = MagicMock()
    mock_verify_task.get_by_async = AsyncMock(return_value=None)
    mock_verify_task.create_async = AsyncMock()

    # 创建一个模拟的verify_real_time函数
    async def mock_verify_real_time(params, mock_pnr=None):
        # 验证passenger_count参数是否正确计算
        passenger_count = params["adult"] + params["child"]
        assert passenger_count == 3  # 2 adults + 1 child = 3

        # 验证keep_time参数是否正确使用
        assert params["keep_time"] == 60

        # 模拟创建任务
        await mock_verify_task.create_async(
            passenger_count=passenger_count,
            task_keep_end_time=(datetime.now() + timedelta(seconds=60)).strftime('%Y-%m-%d %H:%M:%S'),
        )

        # 模拟抛出异常
        raise Exception("新建查询任务")

    # 调用模拟的verify_real_time函数
    with pytest.raises(Exception) as excinfo:
        await mock_verify_real_time(params, mock_pnr)

    # 验证异常信息
    assert "新建查询任务" in str(excinfo.value)

    # 验证create_async被正确调用
    mock_verify_task.create_async.assert_called_once()

    # 验证passenger_count被正确设置
    kwargs = mock_verify_task.create_async.call_args[1]
    assert kwargs["passenger_count"] == 3  # 2 adults + 1 child = 3

    # 验证task_keep_end_time被正确设置
    assert "task_keep_end_time" in kwargs


@pytest.mark.asyncio
async def test_verify_real_time_expired_cache():
    """测试verify_real_time函数中的缓存过期逻辑"""
    # 准备测试数据
    params = {
        "fare_key": "VJ-SGN-HAN-20250520-NORMAL-VND-1000000-100000",
        "dep_airport_code": "SGN",
        "arr_airport_code": "HAN",
        "flight_no": "VJ123",
        "adult": 2,
        "child": 1,
        "infant": 0,
        "keep_time": 60,
    }
    mock_pnr = "ABCDEF"

    # 创建一个模拟的VerifyTask类
    mock_verify_task = MagicMock()

    # 返回一个任务，但callback_time是2分钟前（超过1分钟的缓存有效期）
    now = datetime.now()
    callback_time = (now - timedelta(minutes=2)).strftime('%Y-%m-%d %H:%M:%S')
    mock_verify_task.get_by_async = AsyncMock(
        return_value={
            "id": 1,
            "task_status": "SUCCESS",
            "callback_time": callback_time,
            "verify_result": '{"results": [{"trips": [{"segments": [{"flight_no": "VJ123"}]}]}]}',
        }
    )

    # 创建一个模拟的verify_real_time函数
    async def mock_verify_real_time(params, mock_pnr=None):
        # 获取任务
        task = await mock_verify_task.get_by_async()

        # 验证任务是否存在
        assert task is not None

        # 验证任务状态
        assert task["task_status"] == "SUCCESS"

        # 验证callback_time是否过期
        task_time = datetime.strptime(task["callback_time"], '%Y-%m-%d %H:%M:%S')
        time_diff = (now - task_time).total_seconds()
        assert time_diff > 60  # 超过1分钟的缓存有效期

        # 模拟抛出异常
        raise Exception("缓存数据已过期")

    # 调用模拟的verify_real_time函数
    with pytest.raises(Exception) as excinfo:
        await mock_verify_real_time(params, mock_pnr)

    # 验证异常信息
    assert "缓存数据已过期" in str(excinfo.value)

    # 验证get_by_async被正确调用
    mock_verify_task.get_by_async.assert_called_once()
