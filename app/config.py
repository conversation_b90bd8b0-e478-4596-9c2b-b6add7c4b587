import os
import sys
from typing import Any, Dict, List, Optional
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field

from commons.cores.config_manager import ConfigManager
from commons.extensions.logger_extras import DEFAULT_LOG_FORMAT, es_api_sink, es_sink, log_uid, log_server_name


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    ROOT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

    DEBUG: bool = Field(False, description="是否开启debug模式")
    #
    LOG_PATH: str = Field(os.path.join(ROOT_PATH, 'logs'), description="日志路径")
    LOG_LEVEL: str = Field('INFO', description="日志等级")
    LOG_FORMAT: str = Field(DEFAULT_LOG_FORMAT, description="日志格式")
    LOG_BACKTRACE: bool = Field(False, description="是否开启日志回溯")
    LOG_DIAGNOSE: bool = Field(False, description="是否开启日志诊断")

    # ========= MYSQL ==========
    # 异步操作数据库
    SQLALCHEMY_DATABASE_URI: str = Field(
        'mysql+aiomysql://root:password@localhost/basename', description="异步数据库连接url"
    )
    SQLALCHEMY_ECHO: bool = Field(False, description="是否开启sqlalchemy echo")
    # 每n秒检查一次连接池（重要，可避免链接超时断开）
    SQLALCHEMY_POOL_RECYCLE: int = Field(7200, description="每n秒检查一次连接池（重要，可避免链接超时断开）")
    # 连接池最大连接数
    SQLALCHEMY_POOL_SIZE: int = Field(50, description="连接池最大连接数")
    # 连接池最大等待时间
    SQLALCHEMY_POOL_TIMEOUT: int = Field(30, description="连接池最大等待时间")
    # 连接池超出最大连接数时，最大超出上限
    SQLALCHEMY_MAX_OVERFLOW: int = Field(10, description="连接池超出最大连接数时，最大超出上限")
    # ========= 业务配置 ==========
    MONGO_URL: str = Field(
        'mongodb+srv://hy_admin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0',
        description="mongodb连接url",
    )
    MONGO_KWARGS: Optional[Dict] = Field({"maxPoolSize": 20}, description="mongodb连接参数")
    # ========= REDIS ==========
    REDIS_URL: str = Field(
        'redis://localhost:6379/1?health_check_interval=60&decode_responses=True', description="redis连接url"
    )
    REDIS_MAX_CONNECTIONS: int = Field(50, description="redis最大连接数")
    REDIS_DECODE_RESPONSES: bool = Field(True, description="redis是否解码响应")
    REDIS_SOCKET_TIMEOUT: int = Field(10, description="redis连接超时时间，单位秒，仅同步方式生效")
    REDIS_HEALTH_CHECK_INTERVAL: int = Field(60, description="redis健康检查间隔，单位秒，仅异步方式生效")
    # ========= FastAPI ==========
    FASTAPI_INIT_OPTIONS: Optional[Dict] = Field(
        {'docs_url': '', 'redoc_url': '', 'openapi_url': '', 'swagger_ui_oauth2_redirect_url': ''},
        description="fastapi配置",
    )

    FASTAPI_CORS_ORIGINS: Optional[List[str]] = Field(
        ['http://localhost:9528', 'http://127.0.0.1:9528'], description="跨域白名单"
    )

    API_PERFIX: str = Field('/api/v1/flight_fare', description="api前缀")
    # openssl rand -hex 32
    SECRET_KEY: str = Field(..., description="密钥")  # = env.str('SECRET_KEY')
    ALGORITHM: str = Field('HS256', description="加密算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(60 * 24 * 7, description="token过期时间")  # 7 天

    # ========= Celery ==========
    CELERY_BROKER_URL: str = Field('redis://localhost:6379/1', description="celery任务池地址")

    CELERY_TASK_SERIALIZER: str = Field('json', description="celery任务序列化类型")
    CELERY_RESULT_SERIALIZER: str = Field('json', description="celery结果序列化类型")
    CELERY_ACCEPT_CONTENT: List[str] = Field(['json'], description="celery任务接受内容")
    CELERY_BROKER_CONNECTION_RETRY: bool = Field(
        True, description="celery对链接失败进行重试"
    )  # celery_broker_connection_retry
    CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP: bool = Field(True, description="celery启动时进行链接重试")
    # CELERY_TASK_ROUTES: Dict[str, str] = Field(
    #     {'fare_change_task': 'fare_change_queue'}, description="celery task routes"
    # )
    CELERY_WORKER_HIJACK_ROOT_LOGGER: bool = Field(False, description="是否启用 Celery 接管日志")
    # 爬虫 celery worker
    CELERY_CRAWLER_BROKER_URL: str = Field('redis://localhost:6379/1', description="爬虫任务池地址")
    CELERY_CRAWLER_TASK_ROUTES: Dict[str, str] = Field(
        {
            # vz 泰国越捷航空
            'vz_search_task': 'vz_search_queue',
            'vz_verify_task': 'vz_verify_queue',
            'vz_verify_book_task': 'vz_verify_book_queue',
            'vz_scan_book_task': 'vz_scan_book_queue',
            'vz_book_task': 'vz_book_queue',
            'vz_confirm_pay_task': 'vz_confirm_pay_queue',
            # vj 越南越捷航空
            'vj_search_task': 'vj_search_queue',
            'vj_verify_task': 'vj_verify_queue',
            'vj_verify_book_task': 'vj_verify_book_queue',
            'vj_scan_book_task': 'vj_scan_book_queue',
            'vj_book_task': 'vj_book_queue',
            'vj_confirm_pay_task': 'vj_confirm_pay_queue',
        },
        description="爬虫 task routes",
    )
    # 特殊爬虫 task routes
    SPECIAL_CRAWLER_TASK_ROUTES: Dict[str, str] = Field(
        {'5j_search_task': '5j_search_queue', '5j_verify_task': '5j_verify_queue', '5j_book_task': '5j_book_queue'},
        description="非celery worker执行的爬虫任务",
    )
    # 适配 celery worker
    CELERY_PLATFORM_BROKER_URL: str = Field('redis://localhost:6379/1', description="适配任务池地址")
    CELERY_PLATFORM_TASK_ROUTES: Dict[str, str] = Field(
        {'tb_shopping_push_task': 'tb_shopping_push_queue'}, description="适配 task routes"
    )

    # ========= 业务配置 ==========
    FARE_CHANGE_SET: str = Field('fare_change_set', description="运价变化任务去重")
    FARE_CHANGE_QUEUE: str = Field('fare_change_queue', description="运价变化任务队列")
    HOOD_FARE_SET: str = Field('hood_fare_set', description="压位航线集合")

    FLIGHT_FARE_URL: str = Field('http://*************:8080', description="运价模块API地址")
    FLIGHT_ORDER_URL: str = Field('http://*************:8081', description="订单模块API地址")
    FLIGHT_PRE_ORDER_URL: str = Field('http://*************:8082', description="压位模块API地址")
    PLATFORM_URL: str = Field('http://*************:9529', description="平台模块API地址")
    CRAWLER_URL: str = Field('http://*************:9529', description="压位模块API地址")
    PAY_CENTER_URL: str = Field('http://*************:9529', description="支付中心API地址")
    LOGIN_URL: str = Field(f'/api/v1/flight_fare/admin/login/token', description="登录url,注意前缀配置成运价系统地址")
    FARE_SYNC_INTERVAL: int = Field(3600, description="运价同步间隔，单位秒")
    MIN_CNY_TOTAL_PRICE: int = Field(150, description="最低票价限制：人民币")
    MIN_DISCOUNT_RATE: float = Field(0.8, description="最低折扣率")

    # DRAMATIQ_BROKER_URL: str = Field(..., description="dramatiq broker url")
    # DRAMATIQ_TASK_QUEUES: Dict[str, str] = Field({'tb_fetch_task': 'tb_fetch_queue'}, description="爬虫任务池地址")

    TB_DATA_FETCH_INTERVAL: int = Field(60, description="tb数据抓取间隔，单位分钟")
    MIN_TICKET_QUANTITY: int = Field(1, description="过滤低余票")
    LOW_QUANTITY_CACHE_TIME: int = Field(120, description="低余票缓存时间，单位秒")

    VERIFY_BOOK_CALLBACK_URL: str = Field(..., description="预订回调url")
    SCAN_BOOK_CALLBACK_URL: str = Field(..., description="扫描出票回调url")
    CONFIRM_PAY_CALLBACK_URL: str = Field(..., description="预占座支付回调url")
    DING_TOKEN: str = Field('601f25173ba1db9c4cd3f93bd694b153263a0a06b2da34dfd4d894b1f82b762a', description="钉钉token")
    DING_TOKENS: Dict[str, str] = Field(default_factory=dict, description="钉钉token，格式：{'环境': 'token'}")
    DING_ENV: str = Field('dev', description="钉钉环境")
    ALLOW_CREATE_ORDER_AIRLINES: List[str] = Field(['VZ', 'VJ'], description="允许创建订单的航司")
    CAN_NOT_PAY_LATER: Dict[str, int] = Field(
        {'VZ': 24, 'VJ': 24, '5J': 24 * 30 * 12, 'DG': 24 * 30 * 12},
        description="停止预订,格式：{'航司代码': 起飞时间距离当前时间n小时，则停止预订}; 随时可订的航司，则不设置",
    )
    # 当 CAN_NOT_PAY_LATER 和 OPEN_FUSE 同时存在时，只有航班日期小于等于 STOP_VERIFY_BOOK 的日期，且余票小于等于 OPEN_FUSE 的余票时，才会开启熔断
    AIRLINE_MIN_QUANTITY: Dict[str, int] = Field(
        {'VZ': 1, 'VJ': 1, '5J': 1, 'DG': 1},
        description="是否开启熔断,格式：{'航司代码': 余票数量，小于等于则开启熔断}",
    )

    SCAN_BOOK_EXPIRM_TIME: int = Field(30, description="扫描出票距离PNR失效时间任务终止：分钟")
    HOOD_DATA_EXPIRM_TIME: int = Field(15, description="压位数据缓存时间：分钟")

    TB_TEMP_ORDER_LOCK_TIME: int = Field(40, description="临时订单锁定时间,单位分钟")


settings = Settings()

log_server_name.set('flight_fare')
# ========== 日志配置 ==========
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    backtrace=settings.LOG_BACKTRACE,
    diagnose=settings.LOG_DIAGNOSE,
    filter=lambda record: record["extra"].get("write_tag") is None,
)

logger.add(
    es_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'elasticsearch', serialize=True
)

logger.add(
    es_api_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'api_es_log', serialize=True
)
logger.opt(exception=True)
logger.configure(extra={"unique_id": log_uid})

logger.debug('config loaded')

# 修复 config manager使用错误，单例对象，不能重复初始化
new_settings = ConfigManager(
    settings_files=[
        os.path.join(settings.ROOT_PATH, 'configs', 'airline_code_maps.toml'),
        os.path.join(settings.ROOT_PATH, 'configs', 'fix_contact.toml'),
    ]
)

SYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "socket_timeout": settings.REDIS_SOCKET_TIMEOUT,
}

ASYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "health_check_interval": settings.REDIS_HEALTH_CHECK_INTERVAL,
}
