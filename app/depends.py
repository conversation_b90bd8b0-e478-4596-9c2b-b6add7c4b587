'''
这里存放fastapi的depends
'''

from fastapi.security import OAuth2<PERSON><PERSON><PERSON>Bearer
from commons.extensions.redis_extras import RedisPool
from commons.depends import get_current_user_id
from fastapi import Depends
from typing_extensions import Annotated

# from app.extensions.db_extras import AsyncDatabase
from app.config import settings

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=settings.LOGIN_URL)

#

redis_pool = RedisPool(redis_url=settings.REDIS_URL)


async def get_current_admin_id(token: Annotated[str, Depends(oauth2_scheme)]):
    return await get_current_user_id(token=token, secret_key=settings.SECRET_KEY, algorithm=settings.ALGORITHM)
