from datetime import datetime
import os
import random
from loguru import logger
import or<PERSON><PERSON>
from app import tasks
from app.consts.status import ScanT<PERSON><PERSON>tatus, TaskStatus
from app.consts.types import FareType
from app.models.verify_tmp_order import VerifyTmpOrder
from app.services import public_services
from commons import sdks, utils
from commons.consts.api_codes import ApiCodes
from app.config import settings, new_settings


async def cumpute_order_passenger_num(result: list, mock_pnr: str = None):
    ftd_keys = []
    p_map = {}
    for rs in result:
        dep_airport_code = rs['flight_info']['dep_airport_code']
        arr_airport_code = rs['flight_info']['arr_airport_code']
        dep_date = rs['flight_info']['dep_date']
        ftd_key = f'{dep_airport_code}_{arr_airport_code}_{dep_date}'
        if ftd_key in ftd_keys:
            continue

        ftd_keys.append(ftd_key)
        tmp_order_rows = await VerifyTmpOrder.get_all_async(
            VerifyTmpOrder.dep_airport_code == dep_airport_code,
            VerifyTmpOrder.arr_airport_code == arr_airport_code,
            VerifyTmpOrder.dep_date == dep_date,
            VerifyTmpOrder.created > str(datetime.now().date()),
            VerifyTmpOrder.mock_pnr != mock_pnr,
            VerifyTmpOrder.is_finish == 0,
        )
        for tmp_order_row in tmp_order_rows:
            order_info = orjson.loads(tmp_order_row['order_info'])
            fare_key_info = public_services.decode_fare_key(order_info['fare_key'])
            fare_type = fare_key_info['fare_type']
            p_key = f'{tmp_order_row["dep_airport_code"]}_{tmp_order_row["arr_airport_code"]}_{tmp_order_row["dep_date"]}_{tmp_order_row["flight_no"]}_{order_info["src_adult_base"]}_{fare_type}'
            # 普通运价只扣减占座失败的订单
            if fare_type == FareType.NORMAL.value:
                if tmp_order_row['code'] == ApiCodes.SUCCESS.value or tmp_order_row.get('book_result'):
                    logger.info(f'标准运价，临时订单已成功，不扣减：{order_info["mock_pnr"]}')
                    continue

            if (
                datetime.now() - datetime.strptime(tmp_order_row['created'], '%Y-%m-%d %H:%M:%S')
            ).total_seconds() > settings.TB_TEMP_ORDER_LOCK_TIME * 60:
                logger.info(f'临时订单已过期，不扣减：{order_info["mock_pnr"]} {tmp_order_row["created"]}')
                continue
            passenger_num = len(order_info['passengers'])
            logger.debug(f'临时订单 {order_info["mock_pnr"]} 乘客数量： {passenger_num}')

            if p_key not in p_map:
                p_map[p_key] = 0

            p_map[p_key] += passenger_num
    logger.debug(f'临时订单乘客数量扣减：{p_map}')
    return p_map


# 通过临时订单对余票进行扣减
async def deduct_ticket_by_temp_order(result: list, mock_pnr: str = None):
    new_result = []
    p_map = await cumpute_order_passenger_num(result, mock_pnr=mock_pnr)
    for rs in result:
        tmp_rs = {'flight_info': rs['flight_info'], 'products': []}
        dep_airport_code = rs['flight_info']['dep_airport_code']
        arr_airport_code = rs['flight_info']['arr_airport_code']
        dep_date = rs['flight_info']['dep_date']
        flight_no = rs['flight_info']['flight_no']
        for product in rs['products']:
            p_key = f'{dep_airport_code}_{arr_airport_code}_{dep_date}_{flight_no}_{product["adult"]["src_base"]}_{product["fare_type"]}'
            if p_key in p_map:
                logger.info(f'临时订单乘客数量扣减：{p_key} {product["adult"]["quantity"]} - {p_map[p_key]}')
                product['adult']['quantity'] -= p_map[p_key]
                if 'child' in product:
                    if product['adult']['quantity'] == 1:
                        product['child']['quantity'] = 0
                    else:
                        product['child']['quantity'] -= p_map[p_key]
                if 'infant' in product:
                    if product['adult']['quantity'] == 1:
                        product['infant']['quantity'] = 0
                    else:
                        product['infant']['quantity'] -= p_map[p_key]

            if product['adult']['quantity'] > 0:
                tmp_rs['products'].append(product)

        if tmp_rs['products']:
            new_result.append(tmp_rs)
    return new_result


def parse_passenger_auxes(
    dep_airport_code: str, arr_airport_code: str, flight_no: str, dep_date: str, passengers: list, passenger_auxes: list
):
    for aux in passenger_auxes:
        if (
            aux['dep_airport_code'] == dep_airport_code
            and aux['arr_airport_code'] == arr_airport_code
            and aux['flight_no'] == flight_no
            and aux['dep_date'] == dep_date
        ):
            for passenger in passengers:
                if passenger['name'] == aux['name']:
                    if 'baggages' not in passenger:
                        passenger['baggages'] = []
                    passenger['baggages'].append(aux)
    return passengers


def get_random_contact(airline_code: str):
    # 随机生成一个手机号
    # 网段符合中国移动，联通，电信三大运营商
    # 排除虚拟号段
    fix_contact = new_settings.get(f'fix_contact.{airline_code}')
    if fix_contact:
        return fix_contact

    tmp_contact = {
        "mobile": utils.generate_chinese_phone_number(),
        # "email": "<EMAIL>",
        "email": f"{utils.generate_random_str(random.randint(5, 12))}@iduhang.com",
        "country": "CN",
        "area_code": "+86",
        "city": "Beijing",
        "post_code": "100000",
        "address": "Room 208, Building 3, Jinyuchi Community, Dongcheng District, Beijing",
    }
    return tmp_contact


def parse_contact(airline_code: str, passengers: list, contact: dict = None):
    if contact:
        return contact
    else:
        contact = get_random_contact(airline_code=airline_code)
        if not contact.get('name'):
            for passenger in passengers:
                if passenger['passenger_type'] == 'adult':
                    contact['name'] = passenger['name']
                    contact['last_name'] = passenger['last_name']
                    contact['first_name'] = passenger['first_name']
                    break
        return contact


async def direct_pay(order_no: str, base_float: int = 0, request_id: str = None):
    tmp_order_row = await VerifyTmpOrder.get_by_async(VerifyTmpOrder.order_no == order_no)
    if not tmp_order_row:
        raise Exception(f'订单不存在：{order_no}')
    if tmp_order_row['task_status'] in [TaskStatus.SUCCESS.value, TaskStatus.RUNNING.value]:
        raise Exception(f'订单状态[{TaskStatus(tmp_order_row["task_status"]).label}]不正确：{order_no}')
    order_info = orjson.loads(tmp_order_row['order_info'])
    fare_key_info = public_services.decode_fare_key(order_info['fare_key'])
    dep_date = fare_key_info['dep_date']
    passengers = order_info['passengers']
    passengers = parse_passenger_auxes(
        dep_airport_code=order_info['dep_airport_code'],
        arr_airport_code=order_info['arr_airport_code'],
        flight_no=order_info['flight_no'],
        dep_date=dep_date,
        passengers=passengers,
        passenger_auxes=order_info.get('passenger_auxes', []),
    )
    contact = parse_contact(airline_code=order_info['flight_no'][:2], passengers=passengers)

    req = sdks.crawler.create_order.CreateOrderRequest(
        callback_url=settings.VERIFY_BOOK_CALLBACK_URL,
        order_no=order_info['order_no'],
        mock_pnr=order_info['mock_pnr'],
        airline_code=order_info['flight_no'][:2],
        dep_airport_code=order_info['dep_airport_code'],
        arr_airport_code=order_info['arr_airport_code'],
        dep_date=dep_date,
        flight_no=order_info['flight_no'],
        adult=order_info['adult'],
        child=order_info['child'],
        infant=order_info['infant'],
        currency_code=fare_key_info['src_currency'],
        src_adult_base=order_info['src_adult_base'] + 1,  # 加1的意思是只要票价相等即可出
        src_adult_tax=order_info['src_adult_tax'],
        passengers=order_info['passengers'],
        contact=contact,
        base_float=base_float,
    )
    order_info = orjson.loads(tmp_order_row['order_info'])
    logger.info(f"[ScanBookTask]send ok, order_no:{tmp_order_row['order_no']}")

    airline_code = order_info['flight_no'][:2]
    task_name = f'{airline_code}_book_task'.lower()
    celery_task_params = req.model_dump()
    celery_task_params['unique_id'] = request_id
    logger.debug(f'celery_task_params: {celery_task_params}')
    if task_name in tasks.crawler_celery_tasks:
        celery_task_id = tasks.crawler_celery_tasks[task_name].apply_async(args=(celery_task_params,))
    elif task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
        tasks.simple_task_push(task_name, celery_task_params)
        celery_task_id = celery_task_params['unique_id']
    else:
        raise Exception(f'任务[{task_name}]未找到')

    if celery_task_id:
        # 推送完成更新状态
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.id == tmp_order_row['id'],
            VerifyTmpOrder.task_status.not_in([TaskStatus.SUCCESS.value, TaskStatus.RUNNING.value]),
            celery_task_id=celery_task_id,
            task_status=TaskStatus.RUNNING.value,
            latest_run_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            base_float=base_float,
        )
    else:
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.id == tmp_order_row['id'],
            celery_task_id='',
            scan_status=TaskStatus.FAILED.value,
            message='celery 任务发送失败',
        )


async def confirm_pay(order_no: str):
    tmp_order_row = await VerifyTmpOrder.get_by_async(VerifyTmpOrder.order_no == order_no)
    if not tmp_order_row:
        raise Exception(f'订单不存在：{order_no}')
    if '已支付' in tmp_order_row['message']:
        raise Exception(f'订单已支付：{order_no}')
    if tmp_order_row['real_pnr'] == '':
        raise Exception(f'订单未占座：{order_no}')
    order_info = orjson.loads(tmp_order_row['order_info'])
    real_pnr = tmp_order_row['real_pnr']
    book_result = orjson.loads(tmp_order_row['book_result'])
    if tmp_order_row.get('scan_pnr') and tmp_order_row.get('scan_book_result'):
        real_pnr = tmp_order_row['scan_pnr']
        book_result = orjson.loads(tmp_order_row['scan_book_result'])

    if 'account_name' not in book_result:
        raise Exception(f'订单未获取到账户信息：{order_no}，请手动处理')
    logger.debug(f'预占座支付：{book_result}')
    airline_code = order_info['flight_no'][:2]
    celery_task_params = {
        "callback_url": settings.CONFIRM_PAY_CALLBACK_URL,
        "airline_code": airline_code,
        "order_no": order_info['order_no'],
        "real_pnr": real_pnr,
        "currency_code": book_result['book']['fare']['currency'],
        "total_price": book_result['book']['fare']['total_price'],
        "account_name": book_result['account_name'],
    }
    task_name = f'{airline_code.lower()}_confirm_pay_task'.lower()
    if task_name in tasks.crawler_celery_tasks:
        celery_task_id = tasks.crawler_celery_tasks[task_name].apply_async(args=(celery_task_params,))
    elif task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
        tasks.simple_task_push(task_name, celery_task_params)
        celery_task_id = celery_task_params['unique_id']
    else:
        raise Exception(f'任务[{task_name}]未找到')
    await VerifyTmpOrder.update_by_async(
        VerifyTmpOrder.id == tmp_order_row['id'],
        celery_task_id=celery_task_id,
        task_status=TaskStatus.RUNNING.value,
        latest_run_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    )
