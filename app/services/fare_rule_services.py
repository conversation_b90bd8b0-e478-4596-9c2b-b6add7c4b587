'''
@Author: your name
@Date: 2020-02-06 10:26:15

航线表相关业务逻辑
'''

from datetime import datetime
from loguru import logger

import or<PERSON><PERSON>


from app import tasks, utils
from app.consts.types import FareFlightDateType
from app.config import ASYNC_REDIS_CFG
from app.models.fare_rule import FareRule
from app.models.fare_rule_add_on import FareRuleAddOn
from app.models.fare_rule_baggage import FareRuleBaggage
from app.models.operate_log import OperateLog
from app.services import schedule_services, task_services
from commons.consts.common_status import EnableStatus, SuccessStatus
from commons.extensions.redis_extras import AsyncRedisPool
from app.config import settings


def batch_upper(row, columns):
    for column in columns:
        if column in row:
            row[column] = row[column].upper()
    return row


def request_pre_process(new_row):

    new_row = batch_upper(
        new_row, ['dep_airport_code', 'arr_airport_code', 'airline_code', 'cabin_codes', 'flight_nos']
    )
    return new_row


async def add_rule(new_row):
    logger.debug(new_row)
    baggages = new_row.pop('baggages', None)

    add_ons = new_row.pop('add_ons', None)

    fare_row = await FareRule.create_at_async(**new_row)
    if baggages:
        for baggage in baggages:
            baggage['fare_rule_id'] = fare_row['id']
        logger.debug(baggages)
        await FareRuleBaggage.bulk_insert_or_update(
            records=baggages, update_keys=['fare_rule_id', 'baggage_type', 'baggage_weight']
        )
        fare_row['baggages'] = await FareRuleBaggage.get_all_async(FareRuleBaggage.fare_rule_id == fare_row['id'])
        new_row['baggages'] = baggages
    else:
        fare_row['baggages'] = []

    if add_ons:
        for add_on in add_ons:
            add_on['fare_rule_id'] = fare_row['id']
        logger.debug(add_ons)
        await FareRuleAddOn.bulk_insert_or_update(records=add_ons, update_keys=['fare_rule_id', 'add_on_desc'])
        fare_row['add_ons'] = await FareRuleAddOn.get_all_async(FareRuleAddOn.fare_rule_id == fare_row['id'])
        new_row['add_ons'] = add_ons
    else:
        fare_row['add_ons'] = []
    logger.info(f'本次新增需要同步报价 {new_row}')
    await apply_shopping_push(fare_row)
    return fare_row


async def apply_shopping_push(fare_row):
    try:
        if fare_row['flight_date_type'] == FareFlightDateType.DAYS.value:
            line_map = await schedule_services.split_lines(
                airline_code=fare_row['airline_code'],
                dep_airport_code=fare_row['dep_airport_code'],
                arr_airport_code=fare_row['arr_airport_code'],
                start_days=fare_row['start_days'],
                end_days=fare_row['end_days'],
                use_city=True,
            )
        else:
            line_map = await schedule_services.split_lines(
                airline_code=fare_row['airline_code'],
                dep_airport_code=fare_row['dep_airport_code'],
                arr_airport_code=fare_row['arr_airport_code'],
                flight_start_date=fare_row['flight_start_date'],
                flight_end_date=fare_row['flight_end_date'],
                use_city=True,
            )
        async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
            for k, v in line_map.items():
                line = k.split('-')
                airline_code = line[0]
                dep_city_code = line[1]
                arr_city_code = line[2]
                dep_date = f'{line[3]}-{line[4]}-{line[5]}'
                t = f'{dep_city_code}_{arr_city_code}_{dep_date}'
                if not await redis.sismember(settings.FARE_CHANGE_SET, t):
                    await redis.sadd(settings.FARE_CHANGE_SET, t)
                    await redis.lpush(settings.FARE_CHANGE_QUEUE, t)
                    logger.info(f'推送 {t} 到 {settings.FARE_CHANGE_QUEUE}')
                else:
                    logger.info(f'{t} 同步任务已存在 {settings.FARE_CHANGE_QUEUE}')
    except Exception as e:
        logger.error(e)


async def update_rule(new_row):
    baggages = new_row.pop('baggages', None)
    add_ons = new_row.pop('add_ons', None)
    # 修改主记录
    if new_row:
        await FareRule.update_by_async(FareRule.id == new_row['id'], **new_row)
    fare_rule_row = await FareRule.get_by_async(FareRule.id == new_row['id'])

    # 修改行李信息
    if baggages:
        # 删除不在本次修改的行李信息
        await FareRuleBaggage.delete_all_async(
            FareRuleBaggage.fare_rule_id == fare_rule_row['id'],
            FareRuleBaggage.id.not_in([baggage['id'] for baggage in baggages if 'id' in baggage]),
        )
        # 更新已存在的行李信息
        up_baggages = [baggage for baggage in baggages if 'id' in baggage]
        logger.debug(up_baggages)
        if up_baggages:
            await FareRuleBaggage.bulk_insert_or_update(records=up_baggages, update_keys=['id'])
        logger.debug(baggages)
        # 插入本次修改新增的行李信息
        new_baggages = [baggage for baggage in baggages if 'id' not in baggage]
        if new_baggages:
            # 为新记录添加fare_rule_id
            for baggage in new_baggages:
                baggage['fare_rule_id'] = fare_rule_row['id']
            await FareRuleBaggage.bulk_insert_or_update(
                records=new_baggages, update_keys=['fare_rule_id', 'baggage_type', 'baggage_weight']
            )
        new_row['baggages'] = baggages
    else:
        await FareRuleBaggage.delete_all_async(FareRuleBaggage.fare_rule_id == fare_rule_row['id'])

    fare_rule_row['baggages'] = await FareRuleBaggage.get_all_async(FareRuleBaggage.fare_rule_id == fare_rule_row['id'])
    # 修改附加项
    if add_ons:
        # 删除不在本次修改的附加项
        await FareRuleAddOn.delete_all_async(
            FareRuleAddOn.fare_rule_id == fare_rule_row['id'],
            FareRuleAddOn.id.not_in([add_on['id'] for add_on in add_ons if 'id' in add_on]),
        )
        # 更新已存在的附加项
        up_add_ons = [add_on for add_on in add_ons if 'id' in add_on]
        logger.debug(up_add_ons)
        if up_add_ons:
            await FareRuleAddOn.bulk_insert_or_update(records=up_add_ons, update_keys=['id'])
        # 插入本次修改新增的附加项
        new_add_ons = [add_on for add_on in add_ons if 'id' not in add_on]
        logger.debug(new_add_ons)
        if new_add_ons:
            for add_on in new_add_ons:
                add_on['fare_rule_id'] = fare_rule_row['id']
            await FareRuleAddOn.bulk_insert_or_update(records=new_add_ons, update_keys=['fare_rule_id', 'add_on_desc'])
        new_row['add_ons'] = add_ons
    else:
        await FareRuleAddOn.delete_all_async(FareRuleAddOn.fare_rule_id == fare_rule_row['id'])

    fare_rule_row['add_ons'] = await FareRuleAddOn.get_all_async(FareRuleAddOn.fare_rule_id == fare_rule_row['id'])
    # 只要不是禁用就同步报价
    if new_row.get('status', '') != EnableStatus.DISABLED.value:
        logger.info(f'本次修改需要同步报价 {new_row}')
        await apply_shopping_push(fare_rule_row)
    return fare_rule_row


