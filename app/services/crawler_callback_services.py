import copy
from datetime import datetime, timedelta
import math
import os
import random
import time
from typing import Dict, List

from loguru import logger
import orjson

from app.consts.types import FetchCacheType
from app.config import ASYNC_REDIS_CFG
from app.models.airline_rate import AirlineRate
from app.models.hood_data import HoodData
from app.services import base_data_service, task_services
from app.config import settings, new_settings
from app.services.mongo_services import CommonMongoService
from commons import sdks
from commons.consts.api_codes import ApiCodes
from commons.extensions.redis_extras import AsyncRedisPool
from commons.sdks.base import SdkClient
from commons.sdks.pay_center.exchange_rate import GetExchangeRateRequest


async def split_by_segment(data: Dict) -> List[Dict]:
    results = data['data']['results']
    task_key = data['task_info']['task_key']
    split_data = []
    for i in range(len(results)):
        result = results[i]
        trip_type = result['trip_type']
        task_airline_code = data['task_info']['airline_code']
        data_flight_no = result['trips'][0]['segments'][0]['flight_no']

        data_airline_code = data_flight_no[:2]
        if data_airline_code not in new_settings.get(f'airline_code_maps.{task_airline_code}', []):
            logger.warning(
                f'{task_key}: {data_flight_no} 航班与 {new_settings.get(f"airline_code_maps.{task_airline_code}", [])} 航司代码不匹配'
            )
            continue
        for trip in result['trips']:
            trip_index = trip['trip_index']
            segments = trip['segments']
            flight_nos = ','.join(sorted(trip['flight_nos']))
            batch_key = f'{task_key}-{i}-{trip_type}-{trip_index}-{flight_nos}-{int(time.time()*1000000)}'

            for segment in segments:

                segment_data = {
                    "task_info": copy.deepcopy(data["task_info"]),
                    "result": {
                        "batch_key": batch_key,
                        "trip_type": trip_type,
                        "trip_index": trip_index,
                        "segment_index": segment['segment_index'],
                        "flight_number": segment['flight_number'],
                        "flight_no": segment['flight_no'],
                        "dep_airport_code": segment['dep_airport_code'],
                        "arr_airport_code": segment['arr_airport_code'],
                        "dep_city_code": await base_data_service.get_city_code(
                            airport_code=segment['dep_airport_code']
                        ),
                        "arr_city_code": await base_data_service.get_city_code(
                            airport_code=segment['arr_airport_code']
                        ),
                        "dep_date": segment['dep_date'],
                        "dep_time": segment['dep_time'],
                        "arr_date": segment['arr_date'],
                        "arr_time": segment['arr_time'],
                        "stop_times": segment['stop_times'],
                        "cabin": segment['cabin'],
                        "stops": segment['stops'],
                        "fares": trip['fares'],
                        "includes": trip['includes'],
                        "add_ons": trip.get('add_ons', {}),
                        "exchange": data['data']['exchange'],
                        "aircraft_code": segment['aircraft_code'],
                        "share_code": segment['share_code'],
                    },
                    "error": data["error"],
                }
                split_data.append(segment_data)

    return split_data


from collections import defaultdict


def merge_trips(trips):
    for trip in trips:
        airline_codes = []
        cabin_codes = []
        flight_nos = []

        for segment in trip["segments"]:
            airline_codes.append(segment["airline_code"])
            cabin_codes.append(segment["cabin_code"])
            flight_nos.append(segment["flight_no"])

        # 将拼接后的数组更新到 trip 对象中
        trip["airline_codes"] = airline_codes
        trip["cabin_codes"] = cabin_codes
        trip["flight_nos"] = flight_nos

    return trips


def merge_results(datas):
    # 对datas按 batch_key,trip_index,segment_index 三个字段的值升序排序
    datas = sorted(
        datas, key=lambda x: (x['result']['batch_key'], x['result']['trip_index'], x['result']['segment_index'])
    )

    merged_results = {
        'task_info': datas[0]["task_info"],
        'data': {'results': [], 'exchange': datas[0]["result"]["exchange"]},
        'error': datas[0]["error"],
    }

    result_map = {}

    # 循环列表
    for data in datas:
        result = data["result"]
        logger.debug(result)
        # 先用 batch_key 定位数组组
        batch_key = result["batch_key"]
        if batch_key not in result_map:
            result_map[batch_key] = {
                "trip_type": result["trip_type"],
                "origin_trip_type": result.get("origin_trip_type", result["trip_type"]),
                "expire_time": data['task_info']['expire_time'],
                "trips": [],
            }

        trip_index = result["trip_index"]

        tmp_segment = copy.deepcopy(result)
        del tmp_segment['fares']
        del tmp_segment['includes']
        del tmp_segment['add_ons']
        del tmp_segment['exchange']

        # if trip_index > len(result_map[batch_key]["trips"]):
        if trip_index + 1 > len(result_map[batch_key]["trips"]):

            result_map[batch_key]["trips"].append(
                {
                    "airline_codes": [result['flight_no'][:2]],
                    "cabin_codes": [result['cabin']['code']],
                    "trip_index": trip_index,
                    "flight_nos": [result['flight_no']],
                    "dep_airport_code": result["dep_airport_code"],
                    "arr_airport_code": result["arr_airport_code"],
                    "dep_date": result["dep_date"],
                    "dep_time": result["dep_time"],
                    "arr_date": result["arr_date"],
                    "arr_time": result["arr_time"],
                    "stop_times": result["stop_times"],
                    "across_days": 0,
                    "segments": [tmp_segment],
                    "fares": result["fares"],
                    "includes": result["includes"],
                    "add_ons": result["add_ons"],
                    "exchange": result['exchange'],
                }
            )
        else:
            result_map[batch_key]["trips"][trip_index]["airline_codes"].append(result["flight_no"][:2])
            result_map[batch_key]["trips"][trip_index]["cabin_codes"].append(result["cabin"]["code"])
            result_map[batch_key]["trips"][trip_index]["flight_nos"].append(result["flight_no"])
            result_map[batch_key]["trips"][trip_index]["arr_airport_code"] = result["arr_airport_code"]
            result_map[batch_key]["trips"][trip_index]["arr_date"] = result["arr_date"]
            result_map[batch_key]["trips"][trip_index]["arr_time"] = result["arr_time"]
            result_map[batch_key]["trips"][trip_index]["stop_times"] += result["stop_times"]
            result_map[batch_key]["trips"][trip_index]["segments"].append(tmp_segment)
        arr_date_time = (
            result_map[batch_key]["trips"][trip_index]["arr_date"]
            + " "
            + result_map[batch_key]["trips"][trip_index]["arr_time"]
        )
        if len(arr_date_time) == 16:

            arr_date_time = datetime.strptime(arr_date_time, "%Y-%m-%d %H:%M")
        elif len(arr_date_time) > 16:
            arr_date_time = datetime.strptime(arr_date_time, "%Y-%m-%d %H:%M:%S")

        dep_date_time = (
            result_map[batch_key]["trips"][trip_index]["dep_date"]
            + " "
            + result_map[batch_key]["trips"][trip_index]["dep_time"]
        )

        if len(dep_date_time) == 16:
            dep_date_time = datetime.strptime(dep_date_time, "%Y-%m-%d %H:%M")
        elif len(dep_date_time) > 16:
            dep_date_time = datetime.strptime(dep_date_time, "%Y-%m-%d %H:%M:%S")

        result_map[batch_key]["trips"][trip_index]["across_days"] = (arr_date_time - dep_date_time).days

        # segment_index = result["segment_index"]
        # if segment_index not in result_map[batch_key]["trips"][trip_index]["segments"]:
        #     result_map[batch_key]["trips"][trip_index]["segments"][segment_index] = result
        # result_map[batch_key]["trips"].append(result)

    merged_results["data"]["results"] = list(result_map.values())

    return merged_results


async def parse_exchange(search_result: dict):

    currency_code = search_result["data"]["exchange"]['currency_code']
    airline_code = search_result["task_info"]["airline_code"]
    fix_rate_row = await AirlineRate.get_by_async(
        AirlineRate.airline_code == airline_code, AirlineRate.currency_code == currency_code
    )
    # 减免手续费
    discount_fee = 0
    if fix_rate_row:
        # 使用固定汇率
        rate = fix_rate_row['fixed_rate']
        discount_fee = fix_rate_row['discount_fee']
    else:
        # 使用实时汇率
        pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)
        resp = await pay_center_sdk.send_async(
            request=GetExchangeRateRequest(from_currency=currency_code, to_currency="CNY")
        )
        rate = resp['data']['rate']

    search_result["data"]["exchange"]['rate'] = {'from_code': currency_code, 'to_code': "CNY", 'rate': rate}

    for datas in search_result['data']['results']:
        for trip in datas['trips']:

            for fk, fv in trip["fares"].items():
                # 对航司原价进行四舍五入
                fv['src_base'] = round(float(fv['base']), 2)
                fv['src_tax'] = round(float(fv['tax']), 2)
                fv['src_total'] = round(float(fv['total']), 2)
                fv['src_currency'] = currency_code
                fv['src_discount_fee'] = discount_fee
                fv['base'] = math.ceil(fv['base'] * rate)
                # 在税费基础上减免手续费
                fv['tax'] = math.ceil((fv['tax'] - discount_fee) * rate)
                # 这里不需要乘以汇率，避免浮点数精度导致总价计算错误
                fv['total'] = fv['base'] + fv['tax']
            if 'add_ons' in trip:
                for ak, av in trip["add_ons"].items():
                    if not av:
                        continue
                    if ak == 'baggage':

                        for bk, bv in av.items():
                            for b in bv:
                                b['src_price'] = b['price']
                                b['price'] = math.ceil(b['price'] * rate)
                    else:
                        for a in av:
                            a['src_price'] = a['price']
                            a['price'] = math.ceil(a['price'] * rate)

    return search_result


def cache_fuse(search_result: dict):
    new_results = []
    for datas in search_result['data']['results']:
        new_trips = []
        for trip in datas['trips']:
            if trip['fares']['adult']['total'] < settings.MIN_CNY_TOTAL_PRICE:
                logger.warning(
                    f'{search_result["task_info"]["task_key"]} {trip["flight_nos"]} {trip["cabin_codes"]} 成本票价 {trip["fares"]["adult"]["total"]} 低于 {settings.MIN_CNY_TOTAL_PRICE}，跳过'
                )
                continue
            new_trips.append(trip)
        if new_trips:
            datas['trips'] = new_trips
            new_results.append(datas)
    search_result['data']['results'] = new_results
    if not new_results:
        ApiCodes.UNKNOWN.raise_error(ext_msg='汇率转换或低价过滤异常')
    return search_result


async def apply_shopping_push(search_result: dict):
    airline_code = search_result["task_info"]["airline_code"]
    dep_airport_code = search_result["task_info"]["dep_airport_code"]
    arr_airport_code = search_result["task_info"]["arr_airport_code"]
    dep_date = search_result["task_info"]["dep_date"]

    cache_key = f'cb_cache_{airline_code}{dep_airport_code}{arr_airport_code}{dep_date}'
    flights = []
    for datas in search_result['data']['results']:
        for trip in datas['trips']:
            flights.append(
                ''.join(
                    [''.join(trip['flight_nos']), ''.join(trip['cabin_codes']), str(trip['fares']['adult']['total'])]
                )
            )
    # 对接个进行排序，保证数据不变时value不变
    cache_val = ''.join(sorted(flights))
    is_changed = False

    async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
        old_cache = await redis.get(cache_key)
        logger.debug(f'type(old_cache): {type(old_cache)}')
        logger.debug(f'type(cache_val): {type(cache_val)}')
        if (not old_cache and cache_val) or old_cache != cache_val:
            logger.info(
                f'{airline_code} {dep_airport_code} {arr_airport_code} {dep_date} 有变化 new: {cache_val} old: {old_cache}'
            )
            # 有变化才覆盖，否则过期时间会一直顺延
            await redis.set(cache_key, cache_val, ex=settings.FARE_SYNC_INTERVAL)
            is_changed = True

    if is_changed:
        dep_city_code = await base_data_service.get_city_code(airport_code=dep_airport_code)
        arr_city_code = await base_data_service.get_city_code(airport_code=arr_airport_code)
        try:
            async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
                t = f'{dep_city_code}_{arr_city_code}_{dep_date}'
                if not await redis.sismember(settings.FARE_CHANGE_SET, t):
                    await redis.sadd(settings.FARE_CHANGE_SET, t)
                    await redis.lpush(settings.FARE_CHANGE_QUEUE, t)
                    logger.info(f'推送 {t} 到 {settings.FARE_CHANGE_QUEUE}')
                else:
                    logger.info(f'{t} 同步任务已存在 {settings.FARE_CHANGE_QUEUE}')
        except Exception as e:
            logger.error(e)


async def check_low_quantity(splited_data: dict):
    logger.debug(splited_data)
    if splited_data['result']:

        logger.debug(f'检查 {splited_data["task_info"]["task_key"]} 低票量')

        airline_code = splited_data["task_info"]["airline_code"]
        dep_airport_code = splited_data["task_info"]["dep_airport_code"]
        arr_airport_code = splited_data["task_info"]["arr_airport_code"]
        currency_code = splited_data['result']["exchange"]['currency_code']
        dep_date = splited_data["task_info"]["dep_date"]
        flight_no = splited_data['result']['flight_no']
        min_quantity = settings.AIRLINE_MIN_QUANTITY.get(airline_code, 0)
        if not min_quantity:
            return splited_data

        not_pay_later_hours = settings.CAN_NOT_PAY_LATER.get(airline_code)
        not_pay_later_time = None
        full_dep_time = datetime.strptime(f'{dep_date} {splited_data["result"]["dep_time"]}', '%Y-%m-%d %H:%M:%S')
        if not_pay_later_hours is not None:
            not_pay_later_time = datetime.now() + timedelta(hours=not_pay_later_hours)

        sdk_client = SdkClient(host=settings.FLIGHT_PRE_ORDER_URL)

        segment = splited_data['result']
        logger.debug(
            f'检查 {segment["flight_no"]} {segment["cabin"]["code"]} 余票数 {segment["fares"]["adult"]["quantity"]} (min {min_quantity})'
        )
        if segment['fares']['adult']['quantity'] <= min_quantity and (
            not not_pay_later_time or full_dep_time <= not_pay_later_time
        ):
            # 外部会减掉60，这里要加上，避免过期

            flight_no = segment['flight_no']
            hy_req = sdks.flight_pre_order.create_once_rule.CreateOnceRuleRequest(
                airline_code=airline_code,
                batch_tags=f'A{str(int(time.time()))[-4:]}{random.randint(1000, 9999)}',
                dep_airport_code=dep_airport_code,
                arr_airport_code=arr_airport_code,
                flight_no=flight_no,
                currency_code=currency_code,
                flight_date=dep_date,
                hood_limit=segment['fares']['adult']['quantity'],
            )
            logger.warning(
                f'{splited_data["task_info"]["task_key"]} {segment["flight_no"]} {segment["cabin"]["code"]} 余票数 {segment["fares"]["adult"]["quantity"]} 低于 {min_quantity}'
            )
            try:
                resp = await sdk_client.send_async(hy_req)
                logger.debug(f'推送单次压位结果: {resp}')
            except Exception as e:
                logger.exception(e)

    return splited_data


def coumpute_expire_time(split_data: dict, fetch_rule_row: dict):
    # 默认使用任务过期时间
    expire_seconds = split_data['task_info']['expire_seconds']

    # 如果是低余票，使用低余票过期时间
    quantity = split_data['result']['fares']['adult']['quantity']
    if fetch_rule_row:
        # 如果是浮动，使用浮动过期时间
        if fetch_rule_row['cache_type'] == FetchCacheType.FLOAT_TICKET.value and fetch_rule_row.get('float_seconds'):
            float_seconds = orjson.loads(fetch_rule_row['float_seconds'])
            for item in float_seconds:
                if quantity >= item['min'] and quantity <= item['max']:
                    expire_seconds = item['seconds']
                    logger.info(
                        f'{split_data["task_info"]["task_key"]} {split_data["result"]["flight_no"]} 余票在 [{item["min"]} - {item["max"]}] 之间, 设置浮动过期时间 {expire_seconds} 秒'
                    )
                    break
    else:
        logger.info(
            f'{split_data["task_info"]["task_key"]} {split_data["result"]["flight_no"]} 使用默认过期时间 {expire_seconds} 秒 余票 {quantity}'
        )

    end_time = datetime.strptime(split_data['task_info']['end_time'], '%Y-%m-%dT%H:%M:%S')
    expire_time = end_time + timedelta(seconds=expire_seconds)
    real_expire_seconds = (expire_time - datetime.now()).seconds

    split_data['task_info']['expire_time'] = expire_time.strftime('%Y-%m-%d %H:%M:%S')
    split_data['task_info']['auto_fetch_rule_id'] = split_data['task_info'].get('fetch_rule_id', 0)
    split_data['task_info']['real_expire_seconds'] = real_expire_seconds
    if fetch_rule_row:
        split_data['task_info']['auto_fetch_rule_id'] = fetch_rule_row['id']

    if real_expire_seconds > 60:
        real_expire_seconds -= 60
    elif real_expire_seconds > 30:
        real_expire_seconds -= 30

    return split_data, real_expire_seconds


async def save_hood_result(hood_result: dict):
    src_adult_base = hood_result['flight_info']['trips'][0]['fares']['adult']['base']
    src_adult_tax = hood_result['flight_info']['trips'][0]['fares']['adult']['tax']

    currency_code = hood_result['currency_code']
    airline_code = hood_result['flight_no'][:2]
    fix_rate_row = await AirlineRate.get_by_async(
        AirlineRate.airline_code == airline_code, AirlineRate.currency_code == currency_code
    )
    discount_fee = 0
    if fix_rate_row:
        rate = fix_rate_row['fixed_rate']
        discount_fee = fix_rate_row['discount_fee']
    else:
        pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)
        resp = await pay_center_sdk.send_async(
            request=GetExchangeRateRequest(from_currency=currency_code, to_currency="CNY")
        )
        rate = resp['data']['rate']

    new_row = {
        'airline_code': hood_result['flight_no'][:2],
        'dep_airport_code': hood_result['dep_airport_code'],
        'arr_airport_code': hood_result['arr_airport_code'],
        'dep_date': hood_result['dep_date'],
        'flight_no': hood_result['flight_no'],
        # 'cabin_code': hood_result['cabin_code'],
        # 'cabin_level': hood_result['cabin_level'],
        'quantity': hood_result['seat_num'],
        'adult_base': math.ceil(src_adult_base * rate),
        'adult_tax': math.ceil((src_adult_tax - discount_fee) * rate),
        'src_adult_base': src_adult_base,
        "src_adult_tax": src_adult_tax,
        'src_currency_code': currency_code,
        'expired_time': hood_result['expired_time'],
        'flight_info': orjson.dumps(hood_result['flight_info']).decode('utf-8'),
        'extra_info': orjson.dumps(hood_result['extra']).decode('utf-8'),
        'ex_rate': rate,
        'discount_fee': discount_fee,
    }
    row = await HoodData.create_at_async(**new_row)

    return row


async def del_hood_from_normal_cache(hood_data_row: dict):
    # 删除所有符合条件的缓存
    query = {
        'task_info.airline_code': hood_data_row['airline_code'],
        'task_info.dep_airport_code': hood_data_row['dep_airport_code'],
        'task_info.arr_airport_code': hood_data_row['arr_airport_code'],
        'task_info.dep_date': hood_data_row['dep_date'],
        # 删除时要判断过期时间，否则会发生数据衔接问题，导致验价失败
        'task_info.expire_time': {'$gte': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
        'result.flight_no': hood_data_row['flight_no'],
        'result.fares.adult.base': {'$lte': hood_data_row['src_adult_base']},
    }
    mongo_service = CommonMongoService(db_name='flight_fare', collection_name='airline_fare_cache')
    total = await mongo_service.count(query)
    if total > 0:
        # 删除所有符合条件的缓存
        await mongo_service.delete_by_query(query)
        logger.info(f'删除 {total} 条价格低于 {hood_data_row["src_adult_base"]} 的缓存')
        # 删除任务锁
        task_key = f'{hood_data_row["airline_code"]}-{hood_data_row["dep_airport_code"]}-{hood_data_row["arr_airport_code"]}-{hood_data_row["dep_date"]}'
        await task_services.del_task_key(task_key=task_key)
        logger.info(f'删除任务锁 {task_key}')
