import copy
from datetime import datetime
from hashlib import md5
import hashlib
import uuid

from loguru import logger
from app.clients.tb_client import TBClient
from app.consts.types import FareChannelType


async def is_alive(cookies: dict, headers: dict):
    logger.debug('检查tb是否存活')
    tb_client = TBClient(cookies=cookies, headers=headers)
    result = await tb_client.async_request('get', 'https://saas2.flight.fliggy.com/api/userAuth?lesseeId=432')
    # {'error': '401 Unauthorized'}
    is_alive = result and result.get('data', {}).get('nick') == '北京汇游商旅'
    logger.info(f'检查tb是否存活: {result} {is_alive}')
    return is_alive, tb_client.session.cookies


async def search(
    cookies: dict,
    headers: dict,
    airline_code: str,
    dep_city_code: str,
    arr_city_code: str,
    dep_date: str,
    page: int = 1,
    page_size: int = 100,
):
    logger.debug('执行tb_fetch_task任务')
    tb_client = TBClient(cookies=cookies, headers=headers)
    search_date = int(datetime.strptime(dep_date + ' 05:52:47', "%Y-%m-%d %H:%M:%S").timestamp() * 1000 + 290)
    json_data = {
        'startIndex': page,
        'pageSize': page_size,
        'query': {
            'agentId': 5667,
            'bizType': 2,
            'tripType': 0,
            'directTransferType': 0,
            'supportCodeShare': None,
            'cabinClass': None,
            'productTypes': None,
            'saleModeCodes': None,
            'brandGrade': 1,
            'policyDeployStatus': None,
            'supplyProductCode': None,
            'useProductModel': None,
            'baggageType': None,
            'session': {
                'tairlastUpdatetime': '**********',
                'loginId': '北京汇游商旅',
                'accountRiskGrade': 'normal',
                'signature': '旅5d',
                'loginType': '9',
                'sk_token': 'c54170d2b8791bee',
                'promoted_type': '260',
                'umidToken': 'T2gANjq3xNIwDboHXDQPC4UFFEX9RwGc4qx8yfgDIGg4XGg8papui9QY-3_DmNIyihg=',
                'ua': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'login': 'true',
                'loginFrom': '5',
                'userID': 'f773f8e2bf71695c10d29e0fa1f24a3d',
                'platform': 'PC',
                'xinmaijia': 'false',
                'behaviorTraceId': '1f961d15b7614908195c2032e554c88f',
                'commonSign': '8711d854',
                'sellerId': '1121313',
                'loginIp': '**************',
                'ssllogin': '1',
                'taoMainUser': 'true',
                '_tb_token_': '5e5e73ee8e533',
                'email': '<EMAIL>',
                'm_id': 'b2b-*************04beb',
                'sharedRegionUnits': '[{"region":"CN","unit":"UNSH"},{"region":"CN","unit":"CENTER"}]',
                'LoginUmid': 'wV10z6744478b73c4985f0016901a12e7',
                'mobile': '***********',
                'gmtCreate': '**********',
                '_cc_': '0',
                'displayNick': '北京汇游商旅',
                '_nk_': '北京汇游商旅',
                'loginBizFrom': 'tb',
                'userIDNum': '*************',
                'sgcookie': 'E100mOqfxGW2bv53RGkdxYHlOrmDIvnHGfaTpA5FKtsYWKvZJ++NHJHwJGnUFQ5KTmJxX7nO4nHpaxztFzvwi8WgHso2Ub567yqGlvHsXu2tRgke6RwXSw3izOUW9XPBi+sD',
                'accountId': '*************',
                'site': '0',
                'service_type': '0',
                'taobaoBizLoginFrom': 'fliggy',
                'pl': '74',
                'alipayId': '20889417413873650156',
            },
            'class': 'com.alitrip.agent.business.flight.saas.page.client.policy.model.compareprice.query.ComparePriceQueryDTO',
            'odInfos': [
                {
                    'index': 0,
                    'airline': airline_code,
                    'depAirport': None,
                    'arrAirport': None,
                    'depCity': dep_city_code,
                    'arrCity': arr_city_code,
                    'transitCity': None,
                    'flightDate': {'start': search_date, 'end': search_date},
                    'flightNo': None,
                    'cabinCode': None,
                }
            ],
        },
        'class': 'com.alibaba.fa.framework.ddd.repository.model.PageQuery',
    }
    logger.debug(json_data)
    flight_search_url = 'https://saas2.flight.fliggy.com/api/biz/ComparePriceQueryAppService/queryPage'
    result = await tb_client.async_request(method='POST', url=flight_search_url, json=json_data)
    return result


def format_search_result(batch_no: str, result: dict):
    if '本次搜索无结果' in str(result.get('data', {}).get('msg')):
        logger.info(f'本次搜索无结果: {result}')
        return []

    datas = result.get('data', {}).get('module', {}).get('data', [])
    rows = []
    for d in datas:
        dep_city_code = d['odInfos'][0]['arrDepCity'].split('-')[0]
        arr_city_code = d['odInfos'][0]['arrDepCity'].split('-')[1]
        cabin_code = d['odInfos'][0]['cabin']
        cabin_level = d['odInfos'][0]['cabinClass']
        flight_no = d['odInfos'][0]['flightNos']
        flight_date = datetime.fromtimestamp((d['odInfos'][0]['flightDates'][0]['start'] - 290) / 1000).strftime(
            '%Y-%m-%d'
        )
        dep_airport_code = d['odInfos'][0]['arrDep'].split('-')[0]
        arr_airport_code = d['odInfos'][0]['arrDep'].split('-')[1]
        adult_base = float(d.get('lowestSalePrice', 0) / 100)
        adult_tax = float(d.get('compareTaxPrice', 0) / 100)
        adult_total = adult_base + adult_tax
        self_base = d.get('selfSalePrice', 0)
        sale_mode = d['saleModeCode']

        if sale_mode == 0:
            channel_code = FareChannelType.TB_NORMAL.value
        elif sale_mode == 7:
            channel_code = FareChannelType.TB_GOLD.value
        elif sale_mode == 10:
            channel_code = FareChannelType.TB_DELAY.value
        elif sale_mode == 11:
            channel_code = FareChannelType.TB_DELAY2.value
        elif sale_mode == 12:
            channel_code = FareChannelType.TB_DELAY3.value
        else:
            logger.warning(f'未知销售模式:{sale_mode} {d}')
            channel_code = ''
        baggage_type = d['baggageType']
        baggage_min_weight = 0
        baggage_max_weight = 0
        if baggage_type == 2:
            # >=15,<30
            baggage_min_weight = 15
            baggage_max_weight = 30
        elif baggage_type == 3:
            baggage_min_weight = 30

        if self_base:
            self_base = float(self_base / 100)
        else:
            self_base = 0
        self_tax = d.get('selfTaxPrice', 0)
        if self_tax:
            self_tax = float(self_tax / 100)
        else:
            self_tax = 0
        self_total = self_base + self_tax
        row = {
            'channel_code': channel_code,
            'batch_no': batch_no,
            'airline_code': flight_no[:2],
            'flight_date': flight_date,
            'dep_city_code': dep_city_code,
            'arr_city_code': arr_city_code,
            'cabin_code': cabin_code,
            'cabin_level': cabin_level,
            'flight_no': flight_no,
            'dep_airport_code': dep_airport_code,
            'arr_airport_code': arr_airport_code,
            'adult_base': adult_base,
            'adult_tax': adult_tax,
            'adult_total': adult_total,
            'self_base': self_base,
            'self_tax': self_tax,
            'self_total': self_total,
            'baggage_min_weight': baggage_min_weight,
            'baggage_max_weight': baggage_max_weight,
        }

        rows.append(row)
    logger.debug(f'rows: {rows}')
    return rows


def print_es_logs(rows: list):
    new_map = {}
    # 按 adult_total 从小到大排序
    rows.sort(key=lambda x: x['adult_total'])

    for row in rows:
        copy_row = copy.deepcopy(row)
        key = '_'.join(
            [
                copy_row['channel_code'],
                copy_row['airline_code'],
                copy_row['flight_date'],
                copy_row['dep_city_code'],
                copy_row['arr_city_code'],
                copy_row['flight_no'],
                str(copy_row['baggage_min_weight']),
                str(copy_row['baggage_max_weight']),
            ]
        )
        if key in new_map:
            # 发现新低价
            if copy_row['adult_total'] < new_map[key]['adult_total']:
                # 将最低价替换为次低价
                # if not new_map[key].get('second_total') or new_map[key]['adult_total'] < new_map[key]['second_total']:
                #     new_map[key]['second_total'] = new_map[key]['adult_total']
                #     new_map[key]['second_base'] = new_map[key]['adult_base']
                #     new_map[key]['second_tax'] = new_map[key]['adult_tax']
                # 替换最低价
                new_map[key]['adult_total'] = copy_row['adult_total']
                new_map[key]['adult_base'] = copy_row['adult_base']
                new_map[key]['adult_tax'] = copy_row['adult_tax']
            # else:
            #     # 新价格不是最低价，但是比次低价低
            #     if not new_map[key].get('second_total') or row['adult_total'] < new_map[key]['second_total']:
            #         # 替换次低价
            #         new_map[key]['second_total'] = row['adult_total']
            #         new_map[key]['second_base'] = row['adult_base']
            #         new_map[key]['second_tax'] = row['adult_tax']
            # 同类中发现自身最低价
            if copy_row['self_total'] and not new_map[key]['self_total']:
                new_map[key]['self_total'] = copy_row['self_total']
                new_map[key]['self_base'] = copy_row['self_base']
                new_map[key]['self_tax'] = copy_row['self_tax']
        else:
            new_map[key] = copy_row
            # new_map[key]['second_total'] = 0
            # new_map[key]['second_base'] = 0
            # new_map[key]['second_tax'] = 0

    new_rows = list(new_map.values())
    try:
        from commons.extensions.logger_extras import log_uid

        for row in new_rows:
            # 通用型es日志，字段通过extra传入
            # 注意相同server_name的日志，字段和取值类型要保持一致
            log_uid.set(hashlib.md5(f'{row["batch_no"]}{uuid.uuid4()}'.encode('utf-8')).hexdigest())
            logger.bind(write_tag="elasticsearch").info(
                '', server_name="tb_data", is_lowest=row['adult_total'] == row['self_total'], **row
            )

    except Exception as e:
        logger.exception(e)
    return new_rows
