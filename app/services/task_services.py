import asyncio
import copy
from datetime import datetime, timedelta
import hashlib
import math
import os
import random
import time
from uuid import uuid4
from app.models.verify_tmp_order import VerifyT<PERSON><PERSON>rder

from loguru import logger
import orjson
from sqlalchemy import and_, or_
from app import tasks as celery_tasks
from app import utils
from app.clients.tb_client import TBClient
from app.consts.status import ScanTaskStatus, TaskStatus
from app.consts.types import FareChannelType, FetchType
from app.models.airline import Airline
from app.models.fare_rule import FareRule
from app.models.fetch_rule import FetchRule
from app.models.ota_platform import OTAPlatform
from app.models.schedule import Schedule
from app.models.tb_fare_data import TBFareData
from app.models.verify_task import VerifyTask
from app.services import base_data_service, fare_rule_services, msg_service, tb_service, verify_tmp_order_service
from app.services import public_services
from app.services.mongo_services import CommonMongoService
from app.services.public_services import decode_fare_key
from commons import sdks
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus
from app.config import settings, ASYNC_REDIS_CFG
from app.depends import redis_pool
from commons.consts.flight.resource_site import FlightSiteType
from commons.extensions.redis_extras import AsyncRedisPool
from app.services import tb_fetch_rule_service
from commons.sdks.core import get_sub_sdk


def get_valid_fetch_rule(today: str, dep_airport_code: str, arr_airport_code: str, dep_date: str, fetch_rule_rows):
    result = None
    for fetch_rule_row in fetch_rule_rows:
        diff_days = (datetime.strptime(dep_date, '%Y-%m-%d') - datetime.strptime(today, '%Y-%m-%d')).days

        dep_is_match = (
            fetch_rule_row['dep_airport_code'] == dep_airport_code or fetch_rule_row['dep_airport_code'] == '*'
        )
        arr_is_match = (
            fetch_rule_row['arr_airport_code'] == arr_airport_code or fetch_rule_row['arr_airport_code'] == '*'
        )

        date_is_match = fetch_rule_row['start_days'] <= diff_days <= fetch_rule_row['end_days']
        if fetch_rule_row.get('flight_start_date') and fetch_rule_row.get('flight_end_date'):
            date_is_match = date_is_match or (
                datetime.strptime(fetch_rule_row['flight_start_date'], '%Y-%m-%d').date()
                <= datetime.strptime(dep_date, '%Y-%m-%d').date()
                <= datetime.strptime(fetch_rule_row['flight_end_date'], '%Y-%m-%d').date()
            )
        if dep_is_match and arr_is_match and date_is_match:
            result = fetch_rule_row
            break

    return result


async def set_cache_expire(task_key: str, value: int, expire: int):
    logger.debug(f'设置缓存 {task_key} {value} {expire}')
    async with redis_pool as redis:
        if await redis.exists(task_key):
            await redis.expire(task_key, expire)
        else:
            # 这里需要用expire_seconds作为缓存value，避免影响推送脚本中的优先级判断
            await redis.set(task_key, value=value, ex=expire)


async def del_task_key(task_key: str):
    async with redis_pool as redis:
        return await redis.delete(task_key)


CELERY_ROUTE_MAP = {}


async def send_task(task_params: dict):
    logger.debug(f'添加任务 {task_params}')

    task_key = task_params['task_key']
    # task_queue = f'{site_code}_search_queue'
    airline_code = task_params['airline_code']
    # routes = CELERY_ROUTE_MAP.get(airline_code.upper())
    # if not routes:
    #     routes = await get_celery_routes(airline_code=airline_code)
    #     if not routes:
    #         logger.error(f'获取celery路由失败，航司：{airline_code}')
    #         return False
    #     CELERY_ROUTE_MAP[airline_code.upper()] = routes
    # task_name = f'{airline_code.lower()}_search_task'

    result = False
    async with redis_pool as redis:
        expire_seconds = (
            task_params['expire_seconds'] - 60 if task_params['expire_seconds'] > 60 else task_params['expire_seconds']
        )
        # 这里会造成数据过期，执行效率还是需要从爬虫测保持
        # 或者推送后记录 celeryid，下次推送前要检查 celery 状态（未测试，可能引发其他效率问题）
        # expire_seconds = 60 * 15 # 15分钟
        locked = await redis.set(task_key, task_params['expire_seconds'], nx=True, ex=expire_seconds)
        # 如果锁定失败，检查过期时间
        if not locked:
            expire_seconds = await redis.get(task_key)
            # 如果过期时间比当前任务长，则强制锁定
            if int(expire_seconds) > task_params['expire_seconds']:
                locked = True
                # 强制锁定时注意替换redis信息，避免重复执行
                expire_seconds = (
                    task_params['expire_seconds'] - 60
                    if task_params['expire_seconds'] > 60
                    else task_params['expire_seconds']
                )
                await redis.set(task_key, task_params['expire_seconds'], ex=expire_seconds)
                logger.debug(f'任务过期，强制锁定 {task_key} {expire_seconds} > {task_params["expire_seconds"]}')

        if locked:

            crawler_search_task_name = f'{airline_code.lower()}_search_task'
            if crawler_search_task_name in celery_tasks.crawler_celery_tasks:
                crawler_search_task = celery_tasks.crawler_celery_tasks[crawler_search_task_name]
                celery_task_id = crawler_search_task.apply_async(args=(task_params,))

            elif crawler_search_task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
                celery_tasks.simple_task_push(crawler_search_task_name, task_params)
                celery_task_id = task_params['unique_id']
            else:
                logger.error(f'任务{crawler_search_task_name}未找到对应的队列')
                return
            cont_time = time.time()
            logger.bind(write_tag="api_es_log").info(
                '',
                unique_id=task_params['unique_id'],
                api_type="celery_send",
                api_path=crawler_search_task_name,
                request=orjson.dumps(task_params, default=str).decode("utf-8"),
                response=orjson.dumps(result, default=str).decode("utf-8"),
                status="success",
                code=0,
                message=f"成功：celery id: {celery_task_id}",
                cost_time=time.time() - cont_time,
            )

        else:
            logger.debug(f'任务被锁定 {task_key} {task_params["expire_seconds"]} < {expire_seconds}')
    return result


async def cache_is_valid(task_key):
    async with redis_pool as redis:
        return await redis.exists(task_key)


async def get_celery_routes(airline_code: str):
    sdk_client = sdks.base.SdkClient(host=settings.CRAWLER_URL)
    get_celery_routes_request = sdks.crawler.get_celery_routes.GetCeleryRoutesRequest(crawler_code=airline_code.lower())
    resp = await sdk_client.send_async(get_celery_routes_request)
    if resp and resp['code'] == ApiCodes.SUCCESS.value and resp.get('data'):
        return resp['data']
    return None


# async def batch_send_tasks(task_batch: list, batch_size: int = 100):
#     """批量处理任务发送

#     Args:
#         task_batch: 待处理的任务列表
#         batch_size: 单次pipeline处理的任务数量
#     """
#     results = []
#     # 将任务分成更小的批次处理，避免单个pipeline太大
#     for mini_batch in [task_batch[i : i + batch_size] for i in range(0, len(task_batch), batch_size)]:
#         try:
#             async with redis_pool as redis:
#                 pipe = redis.pipeline()

#                 # 1. 批量检查任务锁
#                 for task in mini_batch:
#                     task_key = task['task_key']
#                     expire_seconds = (
#                         task['expire_seconds'] - 60 if task['expire_seconds'] > 60 else task['expire_seconds']
#                     )
#                     pipe.set(task_key, task['expire_seconds'], nx=True, ex=expire_seconds)

#                 # 2. 执行pipeline获取锁定结果
#                 lock_results = await pipe.execute()

#                 # 3. 处理未能锁定的任务
#                 pipe = redis.pipeline()
#                 update_tasks = []
#                 for i, locked in enumerate(lock_results):
#                     if not locked:
#                         task = mini_batch[i]
#                         task_key = task['task_key']
#                         pipe.get(task_key)
#                         update_tasks.append((i, task))

#                 if update_tasks:
#                     expire_times = await pipe.execute()

#                     # 4. 更新需要强制锁定的任务
#                     pipe = redis.pipeline()
#                     for (i, task), current_expire in zip(update_tasks, expire_times):
#                         if int(current_expire) > task['expire_seconds']:
#                             task_key = task['task_key']
#                             expire_seconds = (
#                                 task['expire_seconds'] - 60 if task['expire_seconds'] > 60 else task['expire_seconds']
#                             )
#                             pipe.set(task_key, task['expire_seconds'], ex=expire_seconds)
#                             lock_results[i] = True

#                     await pipe.execute()

#                 # 5. 创建Celery任务
#                 for i, task in enumerate(mini_batch):
#                     if lock_results[i]:
#                         try:
#                             crawler_search_task_name = f'{task["airline_code"].lower()}_search_task'
#                             if crawler_search_task_name in celery_tasks.crawler_celery_tasks:
#                                 celery_task_id = celery_tasks.crawler_celery_tasks[
#                                     crawler_search_task_name
#                                 ].apply_async(args=(task,))
#                                 results.append((True, task, celery_task_id))
#                             elif crawler_search_task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
#                                 celery_tasks.simple_task_push(crawler_search_task_name, task)
#                                 results.append((True, task, task['unique_id']))
#                             else:
#                                 logger.error(f'任务{crawler_search_task_name}未找到对应的队列')
#                                 results.append((False, task, None))
#                         except Exception as e:
#                             logger.exception(f"任务创建失败: {task['task_key']}")
#                             results.append((False, task, None))
#                     else:
#                         results.append((False, task, None))

#         except Exception as e:
#             logger.exception(f"批处理任务发送失败: {len(mini_batch)}个任务")
#             # 对于批处理失败的情况，将所有任务标记为失败
#             results.extend([(False, task, None) for task in mini_batch])

#     return results


async def create_search_tasks(interval: int, batch_size: int):
    """创建并推送搜索任务

    进一步优化方案:
    1. 缓存常用数据减少数据库查询
    2. 预先批量检查任务有效性
    3. 并行处理规则和时刻表
    4. 流水线并发处理
    """
    while True:
        try:
            global_start_time = time.time()
            start_time = global_start_time
            today = str(datetime.now().date())

            # 1. 缓存常用数据
            # 创建航司缓存,减少重复查询
            airline_cache = {}

            fetch_rule_rows = await FetchRule.get_all_async(
                FetchRule.status == EnableStatus.ENABLED.value, order_by=FetchRule.priority_level.desc()
            )
            end_time = time.time()
            logger.info(f'search_task 读取规则列表: {len(fetch_rule_rows)} 条规则, 耗时: {end_time - start_time:.2f}秒')
            start_time = end_time
            # 预先获取所有涉及的航司信息
            unique_airline_codes = {row['airline_code'] for row in fetch_rule_rows}
            airline_rows = await Airline.get_all_async(Airline.airline_code.in_(unique_airline_codes))
            for airline_row in airline_rows:
                airline_cache[airline_row['airline_code']] = airline_row

            end_time = time.time()
            logger.info(f'search_task 读取航司信息: {len(airline_rows)} 条航司, 耗时: {end_time - start_time:.2f}秒')
            start_time = end_time

            # 预先获取所有有效的时刻表
            schedule_cache = {}
            for airline_code in unique_airline_codes:
                schedule_rows = await Schedule.get_all_async(
                    Schedule.airline_code == airline_code,
                    Schedule.end_date >= today,
                    Schedule.status == EnableStatus.ENABLED.value,
                )
                schedule_cache[airline_code] = schedule_rows

            end_time = time.time()
            logger.info(f'search_task 读取时刻表: {len(schedule_cache)} 条时刻表, 耗时: {end_time - start_time:.2f}秒')
            start_time = end_time

            uuid = str(uuid4())
            task_map = {}  # 用于存储生成的任务
            logger.info(f'search_task 开始处理: {len(fetch_rule_rows)} 条规则')

            # 2. 并行处理规则
            async def process_rule(fetch_rule_row):
                try:
                    airline_code = fetch_rule_row['airline_code']
                    airline_row = airline_cache.get(airline_code)
                    if not airline_row:
                        logger.warning(f'航司信息未找到: {airline_code}')
                        return []

                    schedule_rows = schedule_cache.get(airline_code, [])
                    rule_tasks = []

                    for schedule_row in schedule_rows:
                        # 机场匹配检查
                        if (
                            fetch_rule_row['dep_airport_code'] != '*'
                            and schedule_row['dep_airport_code'] != fetch_rule_row['dep_airport_code']
                        ):
                            continue
                        if (
                            fetch_rule_row['arr_airport_code'] != '*'
                            and schedule_row['arr_airport_code'] != fetch_rule_row['arr_airport_code']
                        ):
                            continue

                        # 计算航班日期
                        if fetch_rule_row['fetch_type'] == FetchType.DAYS.value:
                            dep_dates = utils.calculate_dates(
                                start_days=fetch_rule_row['start_days'],
                                end_days=fetch_rule_row['end_days'],
                                schedule=schedule_row['schedules'],
                            )
                        else:
                            dep_dates = utils.calculate_dates_by_data_range(
                                flight_start_date=fetch_rule_row['flight_start_date'],
                                flight_end_date=fetch_rule_row['flight_end_date'],
                                schedule=schedule_row['schedules'],
                            )

                        # 生成任务
                        for dep_date in dep_dates:
                            # 检查日期是否在时刻表范围内
                            if (
                                datetime.strptime(dep_date, '%Y-%m-%d').date()
                                < datetime.strptime(schedule_row['start_date'], '%Y-%m-%d').date()
                                or datetime.strptime(dep_date, '%Y-%m-%d').date()
                                > datetime.strptime(schedule_row['end_date'], '%Y-%m-%d').date()
                            ):
                                continue

                            dep_airport_code = schedule_row['dep_airport_code']
                            arr_airport_code = schedule_row['arr_airport_code']
                            task_key = f'{airline_code}-{dep_airport_code}-{arr_airport_code}-{dep_date}'

                            unique_id = hashlib.md5(f'{task_key}{uuid}'.encode('utf-8')).hexdigest()
                            task_info = {
                                "callback_url": "",
                                "unique_id": unique_id,
                                "site_code": airline_row['site_code'],
                                "site_type": airline_row['site_type'],
                                "dep_airport_code": dep_airport_code,
                                "arr_airport_code": arr_airport_code,
                                "dep_date": dep_date,
                                "return_date": "",
                                "trip_type": "ow",
                                'status': TaskStatus.PENDING.value,
                                'airline_code': schedule_row['airline_code'],
                                'schedule_id': schedule_row['id'],
                                'fetch_rule_id': fetch_rule_row['id'],
                                'expire_seconds': fetch_rule_row['expire_seconds'],
                                'task_key': task_key,
                                'currency_code': fetch_rule_row['currency_code'],
                                'create_time': datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
                            }
                            rule_tasks.append(task_info)

                    return rule_tasks
                except Exception as e:
                    logger.exception(f"处理规则异常: {fetch_rule_row['id']}")
                    return []

            # 并行处理所有规则
            tasks_per_batch = batch_size  # 每批处理的规则数
            all_tasks = []

            for i in range(0, len(fetch_rule_rows), tasks_per_batch):
                batch = fetch_rule_rows[i : i + tasks_per_batch]
                batch_results = await asyncio.gather(*[process_rule(rule) for rule in batch])
                for tasks in batch_results:
                    all_tasks.extend(tasks)

            end_time = time.time()
            logger.info(f'search_task 生成任务: {len(all_tasks)} 条任务, 耗时: {end_time - start_time:.2f}秒')
            start_time = end_time

            # 3. 批量检查任务是否存在于Redis
            # 将任务按批次分组,每批500个任务
            batches = [all_tasks[i : i + batch_size] for i in range(0, len(all_tasks), batch_size)]
            valid_tasks = []

            async def check_batch_exists(batch):
                batch_valid_tasks = []
                async with redis_pool as redis:
                    pipe = redis.pipeline()
                    for task in batch:
                        pipe.exists(task['task_key'])
                    exists_results = await pipe.execute()

                    for task, exists in zip(batch, exists_results):
                        if not exists:
                            batch_valid_tasks.append(task)
                return batch_valid_tasks

            # 并行检查所有批次
            batch_results = await asyncio.gather(*[check_batch_exists(batch) for batch in batches])
            for tasks in batch_results:
                valid_tasks.extend(tasks)

            end_time = time.time()
            logger.info(f'search_task 检查任务: {len(valid_tasks)} 条任务, 耗时: {end_time - start_time:.2f}秒')
            start_time = end_time

            if len(valid_tasks) == 0:
                logger.info(f'search_task 无新任务，跳过推送')
                continue

            # 4. 优化的批量推送逻辑
            async def batch_send_tasks(task_batch):
                async with redis_pool as redis:
                    pipe = redis.pipeline()

                    # 批量锁定任务
                    for task in task_batch:
                        task_key = task['task_key']
                        expire_seconds = (
                            task['expire_seconds'] - 60 if task['expire_seconds'] > 60 else task['expire_seconds']
                        )
                        pipe.set(task_key, task['expire_seconds'], nx=True, ex=expire_seconds)

                    # 执行锁定操作
                    lock_results = await pipe.execute()

                    # 处理需要更新的任务
                    update_tasks = []
                    for i, locked in enumerate(lock_results):
                        if not locked:
                            task = task_batch[i]
                            pipe.get(task['task_key'])
                            update_tasks.append((i, task))

                    # 如果有需要更新的任务
                    if update_tasks:
                        expire_times = await pipe.execute()
                        pipe = redis.pipeline()

                        for (i, task), current_expire in zip(update_tasks, expire_times):
                            if int(current_expire) > task['expire_seconds']:
                                expire_seconds = (
                                    task['expire_seconds'] - 60
                                    if task['expire_seconds'] > 60
                                    else task['expire_seconds']
                                )
                                pipe.set(task['task_key'], task['expire_seconds'], ex=expire_seconds)
                                lock_results[i] = True

                        await pipe.execute()

                    # 过滤出可以推送的任务
                    tasks_to_push = [task for task, locked in zip(task_batch, lock_results) if locked]

                    return tasks_to_push

            # 5. 并发创建Celery任务
            async def create_celery_tasks(task_list):
                semaphore = asyncio.Semaphore(20)  # 控制并发数

                async def process_single_task(task):
                    async with semaphore:
                        try:
                            task_key = task['task_key']
                            unique_id = task['unique_id']
                            task['create_time'] = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
                            crawler_search_task_name = f'{task["airline_code"].lower()}_search_task'
                            if crawler_search_task_name in celery_tasks.crawler_celery_tasks:
                                celery_task_id = celery_tasks.crawler_celery_tasks[
                                    crawler_search_task_name
                                ].apply_async(args=(task,))
                                return True, task, celery_task_id
                            elif crawler_search_task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
                                celery_tasks.simple_task_push(crawler_search_task_name, task)
                                return True, task, task['unique_id']
                            else:
                                logger.error(f'任务{crawler_search_task_name}未找到对应的队列 {task_key} {unique_id}')
                                return False, task, None
                        except Exception as e:
                            logger.exception(f"任务创建失败: {task['task_key']}")
                            return False, task, None

                return await asyncio.gather(*[process_single_task(task) for task in task_list])

            # 6. 按批次处理所有任务
            pushed_count = 0

            for i in range(0, len(valid_tasks), batch_size):
                batch = valid_tasks[i : i + batch_size]

                # 批量锁定任务
                locked_tasks = await batch_send_tasks(batch)
                end_time = time.time()
                logger.info(
                    f'search_task 批量锁定任务 {batch_size * (i+1)}/{len(valid_tasks)} 条任务: {len(locked_tasks)} 条任务, 耗时: {end_time - start_time:.2f}秒'
                )
                start_time = end_time

                # 创建Celery任务
                results = await create_celery_tasks(locked_tasks)
                end_time = time.time()
                logger.info(
                    f'search_task 创建Celery任务 {batch_size * (i+1)}/{len(valid_tasks)} 条任务: {len(results)} 条任务, 耗时: {end_time - start_time:.2f}秒'
                )
                start_time = end_time

                # 统计成功数量
                for success, task, task_id in results:
                    if success:
                        pushed_count += 1
                        logger.info(
                            f"任务创建成功 tk: {task['task_key']} uid: {task['unique_id']}) tid: {task_id} ct: {task['create_time']}"
                        )
                    else:
                        logger.error(
                            f"任务创建失败 tk: {task['task_key']} uid: {task['unique_id']} ct: {task['create_time']}"
                        )

            end_time = time.time()
            logger.info(
                f'search_task 推送完成: {len(valid_tasks)} 条任务, 成功: {pushed_count}, 耗时: {end_time - global_start_time:.2f}秒'
            )

        except Exception as e:
            if settings.LOG_LEVEL == 'DEBUG':
                logger.exception(e)
            else:
                logger.error(e)
        finally:
            await asyncio.sleep(interval)


async def shopping_push(interval: int, concurrency: int):
    from commons.extensions.logger_extras import log_uid

    async def do_push():
        # 取外部变量interval
        while True:
            try:
                task = None
                async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
                    task = await redis.rpop(settings.FARE_CHANGE_QUEUE)
                    logger.debug(task)
                    if task:
                        await redis.srem(settings.FARE_CHANGE_SET, task)
                        logger.info(f'从 {settings.FARE_CHANGE_QUEUE} 中移除 {task}')

                if not task:
                    logger.debug('not task')
                    await asyncio.sleep(interval)
                    continue

                # log_uid.set(task)
                params = task.split('_')
                if len(params) > 3 and params[3] == '1':
                    await fare_change_task(
                        dep_city_code=params[0], arr_city_code=params[1], dep_date=params[2], update_actual_float=True
                    )
                else:
                    await fare_change_task(dep_city_code=params[0], arr_city_code=params[1], dep_date=params[2])
            except Exception as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(0.2)

    runners = [asyncio.create_task(do_push()) for _ in range(concurrency)]
    await asyncio.gather(*runners)


async def fare_change_task(dep_city_code, arr_city_code, dep_date, update_actual_float=False):
    from commons.extensions.logger_extras import log_uid

    log_uid.set(f'{dep_city_code}-{arr_city_code}-{dep_date}-{update_actual_float}')
    logger.info('开始报价同步任务')

    try:
        # 在进程或线程中创建loop环境
        # asyncio.set_event_loop(loop)
        # logger.debug(f'开始同步 {list(FareChannelType.mappings().keys())}')
        for channel_code in list(FareChannelType.mappings().keys()):
            conditions = {
                'channel_code': channel_code,
                'dep_city_code': dep_city_code,
                'arr_city_code': arr_city_code,
                'dep_date': dep_date,
            }
            result = []
            try:
                normal_result = await public_services.search_simple(
                    update_actual_float=update_actual_float, **conditions
                )
                logger.debug(f'标准运价计算结果（{channel_code}）：{normal_result}')

                hood_result = await public_services.search_hood_price(**conditions)
                if hood_result:
                    logger.debug(f'压位运价计算结果：{hood_result}')
                    result = public_services.merge_result(normal_result, hood_result)
                else:
                    result = normal_result
                # 扣减临时订单
                result = await verify_tmp_order_service.deduct_ticket_by_temp_order(result)
                # 追加基础数据
                # result = await public_services.append_base_data(result=result)
                logger.debug(f'合并结果：{result}')
            except Exception as e:
                logger.exception(e)
            finally:
                result = public_services.fare_fuse(result=result)

            if not result:
                logger.info(f'没有数据，无需同步 {channel_code}')
                continue
            # min_expire_seconds = 0
            # for item in result:
            #     if not min_expire_seconds or item['flight_info']['expire_seconds'] < min_expire_seconds:
            #         min_expire_seconds = item['flight_info']['expire_seconds']

            # logger.debug(f'最小过期时间：{min_expire_seconds}')

            # min_expire_time = datetime.now() + timedelta(seconds=min_expire_seconds)
            min_expire_time = datetime.now() + timedelta(seconds=settings.FARE_SYNC_INTERVAL)

            task_data = {
                'channel_code': channel_code,
                'dep_city_code': dep_city_code,
                'arr_city_code': arr_city_code,
                'dep_date': dep_date,
                'min_expire_time': min_expire_time.strftime('%Y-%m-%d %H:%M:%S'),
                'result': ApiCodes.SUCCESS.generate_api_result(data=result),
            }
            logger.debug(f'准备推送到适配')
            celery_id = celery_tasks.platform_celery_workers['tb_shopping_push_task'].apply_async(args=(task_data,))
            logger.info(f'适配任务已推送，任务ID： {celery_id} {task_data}')
    except Exception as e:
        logger.exception(e)


async def run_tb_data_fetch(storagestate_file: str, concurrency: int):
    """执行淘宝比价数据抓取"""
    if not os.path.exists(storagestate_file):
        err_msg = f'{storagestate_file} 不存在，无法执行淘宝数据抓取'
        await msg_service.tb_fetch_alert(err_msg)
        logger.warning(err_msg)
        return
    with open(storagestate_file, 'r') as f:
        storagestate = orjson.loads(f.read())
    if not storagestate:
        err_msg = f'{storagestate_file} 内容为空，无法执行淘宝数据抓取'
        await msg_service.tb_fetch_alert(err_msg)
        logger.warning(err_msg)
        return
    if not storagestate.get('cookies'):
        err_msg = f'{storagestate_file} 内 cookies 为空，无法执行淘宝数据抓取'
        await msg_service.tb_fetch_alert(err_msg)
        logger.warning(err_msg)
        return
    if not storagestate.get('headers'):
        err_msg = f'{storagestate_file} 内 headers 为空，无法执行淘宝数据抓取'
        await msg_service.tb_fetch_alert(err_msg)
        logger.warning(err_msg)
        return

    is_alive, new_cookies = await tb_service.is_alive(cookies=storagestate['cookies'], headers=storagestate['headers'])
    if is_alive:
        with open(storagestate_file, 'w') as f:
            f.write(orjson.dumps({'cookies': new_cookies, 'headers': storagestate['headers']}).decode('utf-8'))
        logger.info(f'淘宝cookie验证成功，更新 cookies 和 headers')
    else:
        err_msg = f'{storagestate_file} 内 cookies 失效，无法执行淘宝数据抓取'
        await msg_service.tb_fetch_alert(err_msg)
        logger.warning(err_msg)
        return

    fetch_conditions = await tb_fetch_rule_service.get_auto_float_conditions()
    batch_no = datetime.now().strftime('%Y%m%d_%H%M%S')
    task_list = []
    semaphore = asyncio.Semaphore(concurrency)
    if fetch_conditions:
        logger.info('开始执行淘宝数据抓取')
    else:
        err_msg = '无自动调价投放政策，无法执行淘宝数据抓取'
        # await msg_service.tb_fetch_alert(err_msg)
        logger.warning(err_msg)
        return

    for c in fetch_conditions:
        tmp = c.split('-')
        airline_code = tmp[0]
        dep_city_code = tmp[1]
        arr_city_code = tmp[2]
        dep_date = f'{tmp[3]}-{tmp[4]}-{tmp[5]}'
        data = {
            'batch_no': batch_no,
            'cookies': storagestate['cookies'],
            'headers': storagestate['headers'],
            'airline_code': airline_code,
            'dep_city_code': dep_city_code,
            'arr_city_code': arr_city_code,
            'dep_date': dep_date,
        }
        task_list.append(tb_data_fetch(data=data, semaphore=semaphore))
    await asyncio.gather(*task_list)


async def apply_shopping_push(dep_city_code, arr_city_code, dep_date, update_actual_float=''):
    async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
        t = f'{dep_city_code}_{arr_city_code}_{dep_date}_{update_actual_float}'
        if not await redis.sismember(settings.FARE_CHANGE_SET, t):
            await redis.sadd(settings.FARE_CHANGE_SET, t)
            await redis.lpush(settings.FARE_CHANGE_QUEUE, t)
            logger.info(f'触发同步任务：{t}')


async def tb_data_fetch(data: dict, semaphore: asyncio.Semaphore = None):
    if not semaphore:
        semaphore = asyncio.Semaphore(1)
    async with semaphore:
        from commons.extensions.logger_extras import log_uid

        log_uid.set(f'{data["airline_code"]}-{data["dep_city_code"]}-{data["arr_city_code"]}-{data["dep_date"]}')
        try:
            result = await tb_service.search(
                cookies=data['cookies'],
                headers=data['headers'],
                airline_code=data['airline_code'],
                dep_city_code=data['dep_city_code'],
                arr_city_code=data['arr_city_code'],
                dep_date=data['dep_date'],
            )
            rows = tb_service.format_search_result(data['batch_no'], result)
            if not rows:
                return
            # await TBFareData.bulk_insert_or_update(records=rows)

            total = result.get('data', {}).get('module', {}).get('recordsFiltered', 0)
            total_page = int(math.ceil(total / 100))
            for page in range(2, total_page + 1):
                result = await tb_service.search(
                    cookies=data['cookies'],
                    headers=data['headers'],
                    airline_code=data['airline_code'],
                    dep_city_code=data['dep_city_code'],
                    arr_city_code=data['arr_city_code'],
                    dep_date=data['dep_date'],
                    page=page,
                )
                rows += tb_service.format_search_result(data['batch_no'], result)
            # 批量写入数据
            await TBFareData.bulk_insert_or_update(records=rows)
            tb_service.print_es_logs(rows)
            # 停顿，等待数据库完成写入
            await asyncio.sleep(0.2)
            # 推送平台数据同步任务
            await apply_shopping_push(
                dep_city_code=data['dep_city_code'],
                arr_city_code=data['arr_city_code'],
                dep_date=data['dep_date'],
                update_actual_float='1',
            )
        except Exception as e:
            logger.exception(e)


async def run_tb_price_compare_fetch(concurrency: int = 10, max_requests_per_minute: int = 175):
    """
    执行淘宝比价数据抓取 新（api模式）- 批次并发执行（180QPM限制）
    平台最大180，留出5个避免极限
    """
    result = await tb_fetch_rule_service.get_auto_float_conditions()
    fetch_conditions, rule_condition_mapping = result
    batch_no = datetime.now().strftime('%Y%m%d_%H%M%S')

    if not fetch_conditions:
        err_msg = '无自动调价投放政策，无法执行淘宝数据抓取'
        logger.warning(err_msg)
        return

    logger.info(f'开始执行淘宝数据抓取，共 {len(fetch_conditions)} 个条件，涉及 {len(rule_condition_mapping)} 个规则')

    # 平台限制：每分钟180次请求（已改成入参）

    # 根据并发数动态计算时间窗口
    # 时间窗口 = (并发数 / 180) * 60秒，确保不超过180QPM
    window_seconds = (concurrency / max_requests_per_minute) * 60
    # 精确到小数点后2位，向上取值
    import math

    window_seconds = math.ceil(window_seconds * 100) / 100

    # 每个时间窗口处理的请求数等于并发数
    requests_per_window = concurrency

    logger.info(f'并发数: {concurrency}, 动态时间窗口: {window_seconds} 秒, 每窗口请求数: {requests_per_window}')

    # 按并发数为一批进行分组
    batches = []
    for i in range(0, len(fetch_conditions), requests_per_window):
        batch = fetch_conditions[i : i + requests_per_window]
        batches.append(batch)

    logger.info(f'分为 {len(batches)} 个批次，每批次最多 {requests_per_window} 个条件')

    total_success_count = 0
    total_error_count = 0
    overall_start_time = time.time()

    # 逐批次处理
    for batch_index, batch_conditions in enumerate(batches):
        batch_start_time = time.time()
        logger.info(f'开始处理第 {batch_index + 1}/{len(batches)} 批次，包含 {len(batch_conditions)} 个条件')

        # 准备当前批次的所有任务
        batch_tasks = []
        semaphore = asyncio.Semaphore(concurrency)  # 控制并发数

        for c in batch_conditions:
            tmp = c.split('-')
            airline_code = tmp[0]
            dep_city_code = tmp[1]
            arr_city_code = tmp[2]
            dep_date = f'{tmp[3]}-{tmp[4]}-{tmp[5]}'
            data = {
                'batch_no': batch_no,
                'airline_code': airline_code,
                'dep_city_code': dep_city_code,
                'arr_city_code': arr_city_code,
                'dep_date': dep_date,
                'condition': c,  # 添加原始条件，用于更新last_fetch_time
            }
            batch_tasks.append(
                tb_price_compare_fetch_task(
                    data=data, semaphore=semaphore, rule_condition_mapping=rule_condition_mapping
                )
            )

        # 并发执行当前批次的所有任务
        try:
            results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 统计当前批次结果
            batch_success_count = 0
            batch_error_count = 0
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    batch_error_count += 1
                    logger.error(f"批次 {batch_index + 1} 条件 {batch_conditions[i]} 执行异常: {result}")
                else:
                    batch_success_count += 1

            total_success_count += batch_success_count
            total_error_count += batch_error_count

        except Exception as e:
            logger.exception(f"批次 {batch_index + 1} 执行异常: {e}")
            total_error_count += len(batch_conditions)

        # 计算当前批次耗时
        batch_end_time = time.time()
        batch_duration = batch_end_time - batch_start_time

        logger.info(
            f'批次 {batch_index + 1} 完成: 成功 {batch_success_count} 个，失败 {batch_error_count} 个，耗时 {batch_duration:.2f} 秒'
        )

        # 如果不是最后一个批次，需要控制批次间隔
        if batch_index < len(batches) - 1:
            if batch_duration < window_seconds:
                wait_time = window_seconds - batch_duration
                logger.info(
                    f'批次耗时 {batch_duration:.2f} 秒 < {window_seconds} 秒，等待 {wait_time:.2f} 秒后开始下一批次'
                )
                await asyncio.sleep(wait_time)
            else:
                logger.info(f'批次耗时 {batch_duration:.2f} 秒 >= {window_seconds} 秒，直接开始下一批次')

    # 总体统计信息
    overall_end_time = time.time()
    overall_duration = overall_end_time - overall_start_time
    logger.info(
        f'淘宝数据抓取全部完成: 成功 {total_success_count} 个，失败 {total_error_count} 个，总耗时 {overall_duration:.2f} 秒'
    )


async def tb_price_compare_fetch_task(
    data: dict, semaphore: asyncio.Semaphore = None, rule_condition_mapping: dict = None
):
    """淘宝比价数据抓取任务

    Args:
        data: 任务数据
        semaphore: 信号量，用于并发控制。如果为None则不使用信号量（串行执行）
        rule_condition_mapping: 规则条件映射关系，用于更新last_fetch_time
    """

    async def _execute_task():
        """实际的任务执行逻辑"""
        from commons.extensions.logger_extras import log_uid
        import re

        log_uid.set(f'{data["airline_code"]}-{data["dep_city_code"]}-{data["arr_city_code"]}-{data["dep_date"]}')

        # 在实际开始执行时更新规则的last_fetch_time
        if rule_condition_mapping and 'condition' in data:
            await tb_fetch_rule_service.update_rule_last_fetch_time_on_first_execution(
                data['condition'], rule_condition_mapping
            )

        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                platform_sdk = get_sub_sdk(module_name='platform', host=settings.PLATFORM_URL)
                resp = await platform_sdk.platform_public_price_compare_async(
                    ota_code='taobao',
                    airline_code=data['airline_code'],
                    dep_city_code=data['dep_city_code'],
                    arr_city_code=data['arr_city_code'],
                    dep_date=data['dep_date'],
                )

                # 检查是否是限流错误
                if resp.get('code') == 9999 and 'App Call Limited' in resp.get('message', ''):
                    # 解析等待时间
                    message = resp.get('message', '')
                    wait_time_match = re.search(r'This ban will last for (\d+) more seconds', message)
                    if wait_time_match:
                        wait_time = int(wait_time_match.group(1))
                        logger.warning(f'API限流，需要等待 {wait_time} 秒，重试次数: {retry_count + 1}/{max_retries}')
                        await asyncio.sleep(wait_time + 1)  # 多等1秒确保限制解除
                        retry_count += 1
                        continue
                    else:
                        logger.warning(
                            f'API限流但无法解析等待时间，等待3秒后重试，重试次数: {retry_count + 1}/{max_retries}'
                        )
                        await asyncio.sleep(3)  # 减少到3秒
                        retry_count += 1
                        continue

                # 成功或其他错误，跳出重试循环
                if resp['code'] == ApiCodes.SUCCESS.value and resp.get('data'):
                    logger.info(f'比价数据抓取成功 {len(resp["data"])}')
                    rows = resp['data']
                    for r in rows:
                        r['batch_no'] = data['batch_no']
                    # 批量写入数据
                    await TBFareData.bulk_insert_or_update(records=rows)
                    tb_service.print_es_logs(rows)
                    # 数据库操作已经是异步等待完成的，不需要额外停顿
                    # 推送平台数据同步任务
                    await apply_shopping_push(
                        dep_city_code=data['dep_city_code'],
                        arr_city_code=data['arr_city_code'],
                        dep_date=data['dep_date'],
                        update_actual_float='1',
                    )
                else:
                    logger.warning(f'比价数据抓取失败: code={resp.get("code")}, message={resp.get("message")}')
                break  # 成功或非限流错误，跳出重试循环

            except Exception as e:
                logger.exception(f'比价数据抓取异常，重试次数: {retry_count + 1}/{max_retries}')
                retry_count += 1
                if retry_count < max_retries:
                    # 检查异常信息中是否包含限流信息
                    error_message = str(e)
                    wait_time = None

                    # 尝试从异常信息中解析限流等待时间
                    if 'App Call Limited' in error_message:
                        wait_time_match = re.search(r'This ban will last for (\d+) more seconds', error_message)
                        if wait_time_match:
                            wait_time = int(wait_time_match.group(1)) + 1  # 多等1秒确保限制解除
                            logger.warning(
                                f'异常中检测到API限流，需要等待 {wait_time} 秒，重试次数: {retry_count}/{max_retries}'
                            )
                        else:
                            wait_time = 3  # 无法解析等待时间时等待3秒
                            logger.warning(
                                f'异常中检测到API限流但无法解析等待时间，等待 {wait_time} 秒，重试次数: {retry_count}/{max_retries}'
                            )

                    # 如果不是限流错误，使用指数退避策略
                    if wait_time is None:
                        wait_time = min(2**retry_count, 10)  # 最大等待10秒
                        logger.debug(f'非限流异常，使用指数退避策略等待 {wait_time} 秒')

                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f'比价数据抓取最终失败，已重试 {max_retries} 次')
                    break

        # 移除最终停顿，因为已经有请求间隔控制了
        # await asyncio.sleep(random.randint(80, 120) / 1000)

    # 根据是否有semaphore来决定执行方式
    if semaphore:
        async with semaphore:
            await _execute_task()
    else:
        await _execute_task()


def send_verify_tasks(verify_task_row: dict):
    task_key = f'{verify_task_row["airline_code"]}-{verify_task_row["dep_airport_code"]}-{verify_task_row["arr_airport_code"]}-{verify_task_row["dep_date"]}'
    celery_id = None
    src_params = verify_task_row['verify_params']
    logger.debug(f'发送验证任务 {src_params}')
    if src_params:
        src_params = orjson.loads(src_params)
    else:
        src_params = {}
    task_data = {
        "callback_url": "",
        "verify_task_id": verify_task_row['id'],
        "unique_id": verify_task_row['unique_id'],
        "site_code": f'airline_{verify_task_row["airline_code"].lower()}',
        "site_type": FlightSiteType.AIRLINE.value,
        "dep_airport_code": verify_task_row['dep_airport_code'],
        "arr_airport_code": verify_task_row['arr_airport_code'],
        "dep_date": verify_task_row['dep_date'],
        "return_date": "",
        "trip_type": "ow",
        'status': TaskStatus.PENDING.value,
        'airline_code': verify_task_row['airline_code'],
        'schedule_id': 0,
        'fetch_rule_id': verify_task_row['fare_id'],
        'expire_seconds': 600,
        'task_key': task_key,
        'currency_code': verify_task_row['currency_code'],
        'create_time': datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
        'adult': src_params.get('adult', 1),
        'child': src_params.get('child', 0),
        'infant': src_params.get('infant', 0),
    }
    task_name = f'{verify_task_row["airline_code"]}_verify_task'.lower()
    if task_name in celery_tasks.crawler_celery_tasks:
        celery_id = celery_tasks.crawler_celery_tasks[task_name].apply_async(args=(task_data,))
    elif task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
        celery_tasks.simple_task_push(task_name, task_data)
        celery_id = task_data['unique_id']
    else:
        logger.error(f'任务{task_name}未找到对应的队列')
    return celery_id


async def create_verify_tasks(interval: int):
    while True:
        try:
            verify_task_rows = await VerifyTask.get_all_async(
                or_(
                    # 筛选出待执行的任务
                    VerifyTask.task_status == TaskStatus.PENDING.value,
                    # 或者是任务未过期，但缓存已失效的任务
                    and_(
                        VerifyTask.task_status.in_([TaskStatus.SUCCESS.value, TaskStatus.FAILED.value]),
                        VerifyTask.task_keep_end_time > datetime.now(),
                        VerifyTask.callback_time < datetime.now() - timedelta(seconds=60),
                    ),
                ),
                order_by=VerifyTask.created.desc(),
            )
            for verify_task_row in verify_task_rows:
                # task_key = f'{verify_task_row["airline_code"]}-{verify_task_row["dep_airport_code"]}-{verify_task_row["arr_airport_code"]}-{verify_task_row["dep_date"]}'
                # task_data = {
                #     "callback_url": "",
                #     "verify_task_id": verify_task_row['id'],
                #     "unique_id": verify_task_row['unique_id'],
                #     "site_code": verify_task_row['airline_code'],
                #     "site_type": FlightSiteType.AIRLINE.value,
                #     "dep_airport_code": verify_task_row['dep_airport_code'],
                #     "arr_airport_code": verify_task_row['arr_airport_code'],
                #     "dep_date": verify_task_row['dep_date'],
                #     "return_date": "",
                #     "trip_type": "ow",
                #     'status': TaskStatus.PENDING.value,
                #     'airline_code': verify_task_row['airline_code'],
                #     'schedule_id': 0,
                #     'fetch_rule_id': verify_task_row['fare_id'],
                #     'expire_seconds': 600,
                #     'task_key': task_key,
                #     'currency_code': verify_task_row['currency_code'],
                #     'create_time': datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
                # }
                # task_name = f'{verify_task_row["airline_code"]}_verify_task'.lower()
                # if task_name in celery_tasks.crawler_celery_tasks:
                #     celery_id = celery_tasks.crawler_celery_tasks[task_name].apply_async(args=(task_data,))
                # elif task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
                #     celery_tasks.simple_task_push(task_name, task_data)
                #     celery_id = task_data['unique_id']
                # else:
                #     logger.error(f'任务{task_name}未找到对应的队列')
                #     continue
                celery_id = send_verify_tasks(verify_task_row)
                if not celery_id:
                    continue
                await VerifyTask.update_by_async(
                    VerifyTask.id == verify_task_row['id'],
                    VerifyTask.task_status == verify_task_row['task_status'],
                    task_status=TaskStatus.RUNNING.value,
                )
                logger.info(f"Created celery task for verify task {verify_task_row['id']}: {celery_id}")

        except Exception as e:
            logger.exception(e)
        finally:
            await asyncio.sleep(interval)


async def send_scan_book_tasks(interval: int):
    # 发送扫描出票的任务

    while True:
        try:

            today = str(datetime.now().date())
            fetch_rows = await VerifyTmpOrder.get_all_async(
                VerifyTmpOrder.scan_status == ScanTaskStatus.PREPARE.value, order_by=VerifyTmpOrder.created.desc()
            )

            for fetch_row in fetch_rows:
                affect = await VerifyTmpOrder.update_by_async(
                    VerifyTmpOrder.id == fetch_row['id'],
                    VerifyTmpOrder.scan_status == ScanTaskStatus.PREPARE.value,
                    # VerifyTmpOrder.real_pnr != '',  # 有真PNR才可以开始
                    scan_status=ScanTaskStatus.LOCKED.value,
                )
                if not affect:
                    logger.warning(f"[ScanBookTask]lock task error, order_no:{fetch_row['order_no']}")
                    continue
                current_time = datetime.now()

                if fetch_row.get('real_pnr') and fetch_row.get('book_result'):
                    expire_time_str = fetch_row['expire_time']
                    expire_time = datetime.strptime(expire_time_str, '%Y-%m-%d %H:%M:%S')
                else:
                    expire_time = datetime.now() + timedelta(minutes=settings.SCAN_BOOK_EXPIRM_TIME)

                time_difference = expire_time - current_time
                minutes_difference = time_difference.total_seconds() / 60

                if minutes_difference < settings.SCAN_BOOK_EXPIRM_TIME:
                    await VerifyTmpOrder.update_by_async(
                        VerifyTmpOrder.id == fetch_row['id'],
                        VerifyTmpOrder.scan_status == ScanTaskStatus.LOCKED.value,
                        scan_status=ScanTaskStatus.OUTTIME.value,
                    )
                    logger.info(
                        f"[ScanBookTask]expire time error, order_no:{fetch_row['order_no']}, expire_time:{expire_time}"
                    )
                    msg_service.scan_timeout(fetch_row['mock_pnr'])
                    continue

                order_info = orjson.loads(fetch_row['order_info'])
                if not fetch_row['book_result'] or fetch_row['book_result'] == 'null':
                    # await VerifyTmpOrder.update_by_async(
                    #     VerifyTmpOrder.id == fetch_row['id'],
                    #     VerifyTmpOrder.scan_status == ScanTaskStatus.LOCKED.value,
                    #     code=ApiCodes.FARE_SCAN_NO_BOOK_RESULT.value,
                    #     message=ApiCodes.FARE_SCAN_NO_BOOK_RESULT.label,
                    #     scan_status=ScanTaskStatus.FAILED.value,
                    # )
                    # logger.warning(f"[ScanBookTask]book_result is None, order_no:{fetch_row['order_no']}")
                    fare_key_info = decode_fare_key(order_info['fare_key'])
                    dep_date = fare_key_info['dep_date']
                    passengers = order_info['passengers']
                    passengers = verify_tmp_order_service.parse_passenger_auxes(
                        dep_airport_code=order_info['dep_airport_code'],
                        arr_airport_code=order_info['arr_airport_code'],
                        flight_no=order_info['flight_no'],
                        dep_date=dep_date,
                        passengers=passengers,
                        passenger_auxes=order_info.get('passenger_auxes', []),
                    )
                    contact = verify_tmp_order_service.parse_contact(
                        airline_code=order_info['flight_no'][:2], passengers=passengers
                    )

                    req = sdks.crawler.create_order.CreateOrderRequest(
                        callback_url=settings.SCAN_BOOK_CALLBACK_URL,
                        order_no=order_info['order_no'],
                        mock_pnr=order_info['mock_pnr'],
                        airline_code=order_info['flight_no'][:2],
                        dep_airport_code=order_info['dep_airport_code'],
                        arr_airport_code=order_info['arr_airport_code'],
                        dep_date=dep_date,
                        flight_no=order_info['flight_no'],
                        adult=order_info['adult'],
                        child=order_info['child'],
                        infant=order_info['infant'],
                        currency_code=fare_key_info['src_currency'],
                        src_adult_base=order_info['src_adult_base'] + 1,  # 加1的意思是只要票价相等即可出
                        src_adult_tax=order_info['src_adult_tax'],
                        passengers=order_info['passengers'],
                        contact=contact,
                    )

                else:

                    # 推送celery任务给爬虫队列,进行轮询占座

                    book_result = orjson.loads(fetch_row['book_result'])
                    adult_price = book_result['flight']['trips'][0]['fares']['adult']
                    airline_code = order_info['flight_no'][:2]
                    fare_key_info = decode_fare_key(order_info['fare_key'])
                    dep_date = fare_key_info['dep_date']
                    req = sdks.crawler.create_order.CreateOrderRequest(
                        callback_url=settings.SCAN_BOOK_CALLBACK_URL,
                        order_no=order_info['order_no'],
                        mock_pnr=order_info['mock_pnr'],
                        airline_code=airline_code,
                        dep_airport_code=order_info['dep_airport_code'],
                        arr_airport_code=order_info['arr_airport_code'],
                        dep_date=dep_date,
                        flight_no=order_info['flight_no'],
                        adult=order_info['adult'],
                        child=order_info['child'],
                        infant=order_info['infant'],
                        currency_code=fare_key_info['src_currency'],
                        src_adult_base=adult_price['base'],
                        src_adult_tax=adult_price['tax'],
                        passengers=order_info['passengers'],
                    )
                logger.info(f"[ScanBookTask]send ok, order_no:{fetch_row['order_no']}")

                airline_code = order_info['flight_no'][:2]
                task_name = f'{airline_code}_scan_book_task'.lower()
                celery_task_params = req.model_dump()
                celery_task_params['unique_id'] = req._headers['X-Request-ID']
                celery_task_id = celery_tasks.crawler_celery_tasks[task_name].apply_async(args=(celery_task_params,))

                if celery_task_id:
                    # 推送完成更新状态
                    await VerifyTmpOrder.update_by_async(
                        VerifyTmpOrder.id == fetch_row['id'],
                        VerifyTmpOrder.scan_status == ScanTaskStatus.LOCKED.value,
                        celery_task_id=celery_task_id,
                        scan_status=ScanTaskStatus.RUNNING.value,
                        latest_run_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    ),
                else:
                    await VerifyTmpOrder.update_by_async(
                        VerifyTmpOrder.id == fetch_row['id'],
                        celery_task_id='',
                        scan_status=TaskStatus.FAILED.value,
                        message='celery 任务发送失败',
                    )

        except Exception as e:
            if settings.LOG_LEVEL == 'DEBUG':
                logger.exception(e)
            else:
                logger.error(e)
        finally:
            await asyncio.sleep(interval)
