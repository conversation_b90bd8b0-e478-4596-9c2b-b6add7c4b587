import asyncio
import copy
from datetime import datetime
import hashlib
import os
from uuid import uuid4

import aiofiles
from loguru import logger
import orjson
import pandas as pd
from app.config import settings
from app.services.mongo_services import CommonMongoService


async def update_city_and_airports(csv_file: str):
    df = pd.read_csv(csv_file)
    logger.debug(df.head())
    result = {}
    for row in df.values.tolist():
        # logger.debug(row)
        try:
            airport_code = row[0]
            # airport_en= row[2]
            city_code = row[8]
            country_code = row[9]
            city_en = row[10]

            if city_code not in result:
                result[city_code] = {
                    'country_code': country_code,
                    'city_code': city_code,
                    'city_en': city_en,
                    'airport_codes': [airport_code],
                }
            else:
                if airport_code not in result[city_code]['airport_codes']:
                    result[city_code]['airport_codes'].append(airport_code)
        except Exception as e:
            logger.exception(e)
    # logger.debug(result)
    map_file = os.path.join(settings.ROOT_PATH, 'static', 'city_airport_map.json')
    async with aiofiles.open(map_file, 'w') as f:
        await f.write(orjson.dumps(result).decode('utf-8'))
    mongo_service = CommonMongoService(db_name='base_data', collection_name='city_airport_map')
    await mongo_service.bulk_insert_or_update(datas=list(result.values()), unique_keys=['city_code'])


async def get_airport_codes(city_code: str):
    # city_airport_map_file = os.path.join(settings.ROOT_PATH, 'static', 'city_airport_map.json')
    # async with aiofiles.open(city_airport_map_file, 'r') as f:
    #     city_airport_map = orjson.loads(await f.read())
    # airport_codes = city_airport_map.get(city_code)
    # if airport_codes is None:
    #     airport_codes = [city_code]
    # else:
    #     airport_codes = airport_codes['airport_codes']
    mongo_service = CommonMongoService(db_name='base_data', collection_name='city_airport_map')
    airport_codes = await mongo_service.find_one({'city_code': city_code})
    if airport_codes:
        airport_codes = airport_codes['airport_codes']
    else:
        airport_codes = [city_code]
    return airport_codes


async def get_city_code(airport_code: str):
    mongo_service = CommonMongoService(db_name='base_data', collection_name='city_airport_map')
    city_code = await mongo_service.find_one({'airport_codes': airport_code})
    if city_code:
        city_code = city_code['city_code']
    logger.debug(city_code)
    return city_code


async def append_airport_code(airport_code: str, city_code: str, city_en: str, country_code: str):
    """
    调整机场代码

    Args:
        airport_code (str): _description_
        city_code (str): _description_
    """
    """
    example:
    db.city_airport_map.find({"city_code":"CTU"})
    db.city_airport_map.find({"city_code":"TFU"})

    db.city_airport_map.updateOne(
    { _id: ObjectId("674807eed2ee1fb1a35da247") },
    { $set: { airport_codes: ["CTU","TFU"] }  }
    );

    db.city_airport_map.remove({"city_code":"TFU"})
    """
    mongo_service = CommonMongoService(db_name='base_data', collection_name='city_airport_map')
    # 查询城市码
    data_row = await mongo_service.find_one({'city_code': city_code})
    airport_code = airport_code.upper()
    city_code = city_code.upper()
    country_code = country_code.upper()
    if data_row:
        data_row['airport_codes'].append(airport_code)
        await mongo_service.update_by_id(data_row['_id'], {'airport_codes': data_row['airport_codes']})
    else:
        await mongo_service.bulk_insert_or_update(
            [
                {
                    'city_code': city_code,
                    'airport_codes': [airport_code],
                    'city_en': city_en,
                    'country_code': country_code,
                }
            ],
            unique_keys=['city_code'],
        )
    data_row = await mongo_service.find_one({'city_code': city_code})
    logger.info(f'调整机场代码成功 {data_row}')

    # 删除旧的机场代码
    await mongo_service.delete_by_query({'city_code': airport_code})
    logger.info(f'删除旧的机场代码成功 {airport_code}')
