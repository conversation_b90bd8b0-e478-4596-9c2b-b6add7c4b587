import asyncio
from collections import defaultdict
from datetime import datetime
import os
import random
import re
import time
from typing import Union

from loguru import logger
import orjson


from app.consts.types import (
    AddOnType,
    BaggageType,
    FareFlightDateType,
    FareRuleMarkupType,
    FareType,
    FareValidType,
    FetchType,
    UpdateModeCode,
)
from app.models.operate_log import OperateLog
from commons.consts.common_status import EnableStatus


#


class OperateLogBaseServices:
    """
    操作日志
    """

    def __init__(self, admin_id: int, username: str, username_desc: str = '') -> None:
        self.admin_id = admin_id
        self.username = username
        self.username_desc = username_desc

    async def save_log(self, op_table: str, row_id: int, op_type: str, desc_tpl: str, op_data: dict):
        op_log_row = None
        try:
            op_time = datetime.now()
            # 通过defaultdict创建一个默认值为空字符串的字典
            op_desc = desc_tpl.format_map(defaultdict(lambda: '未修改', op_data))

            op_log_row = await OperateLog.create_at_async(
                op_table=op_table,
                row_id=row_id,
                admin_id=self.admin_id,
                username=f'{self.username}({self.username_desc})' if self.username_desc else self.username,
                op_type=op_type,
                op_time=op_time,
                op_desc=op_desc,
                op_data=orjson.dumps(op_data).decode('utf-8'),
            )
        except Exception as e:
            logger.exception(f"操作日志写入失败: {e}")

        return op_log_row

    async def get_list(self, id: int, limit: int = 20):
        log_rows = await OperateLog.get_all_async(
            OperateLog.op_table == self.table,
            OperateLog.row_id == id,
            limit=limit,
            order_by=[OperateLog.op_time.desc(), OperateLog.id.desc()],
        )

        return log_rows


class FetchRuleOpLogServices(OperateLogBaseServices):
    FIELDS_TPL = {
        "airline_code": "航司: {params[airline_code]}",
        "dep_airport_code": "出发机场三字码：{params[dep_airport_code]}",
        "arr_airport_code": "到达机场三字码：{params[arr_airport_code]}",
        "start_days": "从第 {params[start_days]} 天（含）开始抓取",
        "end_days": "到第 {params[end_days]} 天（含）结束",
        "flight_start_date": "开始抓取日期：{params[flight_start_date]}",
        "flight_end_date": "结束抓取日期：{params[flight_end_date]}",
        "ota_codes": "需要同时抓取的ota平台：{params[ota_codes]}",
        "expire_seconds": "缓存过期时间：{params[expire_seconds]} 秒",
        "priority_level": "配置冲突时的优先级：{params[priority_level]}",
        "currency_code": "抓取币种：{params[currency_code]}",
        # "fixed_rate": "固定汇率：{params[fixed_rate]}",
        # "discount_fee": "减免手续费：{params[discount_fee]} (抓取币种)",
    }

    def __init__(self, admin_id: int, username: str, username_desc: str = '') -> None:
        super().__init__(admin_id, username, username_desc)
        self.table = 'fetch_rules'

    async def do_create(self, id: int, op_data: dict):
        tpl = [self.FIELDS_TPL[pm] for pm in op_data['params'].keys() if pm in self.FIELDS_TPL]
        if 'fetch_type' in op_data['params']:
            tpl.append(f'抓取类型：{FetchType(op_data["params"]["fetch_type"]).label}')
        if 'status' in op_data['params']:
            tpl.append(f'状态：{EnableStatus(op_data["params"]["status"]).label}')

        await self.save_log(
            op_table=self.table, row_id=id, op_type='创建抓取策略', desc_tpl='，'.join(tpl), op_data=op_data
        )

    async def do_update(self, id: int, op_data: dict):
        tpl = [self.FIELDS_TPL[pm] for pm in op_data['params'].keys() if pm in self.FIELDS_TPL]
        if 'fetch_type' in op_data['params']:
            tpl.append(f'抓取类型：{FetchType(op_data["params"]["fetch_type"]).label}')
        if 'status' in op_data['params']:
            tpl.append(f'状态：{EnableStatus(op_data["params"]["status"]).label}')

        tpl.append("{message}")
        await self.save_log(
            op_table=self.table, row_id=id, op_type='修改抓取策略', desc_tpl='，'.join(tpl), op_data=op_data
        )

    async def do_batch_enable(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='批量启用', desc_tpl="{message}", op_data=op_data)

    async def do_batch_disable(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='批量禁用', desc_tpl="{message}", op_data=op_data)


class FareRuleOpLogServices(OperateLogBaseServices):

    FIELDS_TPL = {
        'airline_code': '航司：{params[airline_code]}',
        'channel_code': '投放渠道：{params[channel_code]}',
        'fare_start_date': '运价开始日期：{params[fare_start_date]}',
        'fare_end_date': '运价结束日期：{params[fare_end_date]}',
        'flight_start_date': '航班开始日期：{params[flight_start_date]}',
        'flight_end_date': '航班结束日期：{params[flight_end_date]}',
        'dep_airport_code': '出发机场三字码：{params[dep_airport_code]}',
        'arr_airport_code': '到达机场三字码：{params[arr_airport_code]}',
        'flight_nos': '指定航班号：{params[flight_nos]}',
        'exclude_flight_nos': '排除航班号：{params[exclude_flight_nos]}',
        'cabin_codes': '舱位代码：{params[cabin_codes]}',
        'priority_level': '配置冲突时的优先级：{params[priority_level]}',
        # 'status': '政策状态：{params[status]}',
        # 'adult_markup_type': '成人加价类型：{params[adult_markup_type]}',
        # 'child_markup_type': '儿童加价类型：{params[child_markup_type]}',
        # 'infant_markup_type': '婴儿加价类型：{params[infant_markup_type]}',
        'adult_tag_base': '成人票投放价：{params[adult_tag_base]}',
        'child_tag_base': '儿童票投放价：{params[child_tag_base]}',
        'infant_tag_base': '婴儿票投放价：{params[infant_tag_base]}',
        'adult_tag_tax': '成人票税费：{params[adult_tag_tax]}',
        'child_tag_tax': '儿童票税费：{params[child_tag_tax]}',
        'infant_tag_tax': '婴儿票税费：{params[infant_tag_tax]}',
        'adult_cabin_code': '成人票舱位：{params[adult_cabin_code]}',
        'child_cabin_code': '儿童票舱位：{params[child_cabin_code]}',
        'infant_cabin_code': '婴儿票舱位：{params[infant_cabin_code]}',
        "start_days": "从第 {params[start_days]} 天（含）开始投放",
        "end_days": "到第 {params[end_days]} 天（含）结束投放",
        'auto_min_float': '自动调价最低浮动值：{params[auto_min_float]}',
        'auto_max_float': '自动调价最高浮动值：{params[auto_max_float]}',
    }

    def __init__(self, admin_id: int, username: str, username_desc: str = '') -> None:
        super().__init__(admin_id, username, username_desc)
        self.table = 'fare_rules'

    def append_tpl(self, tpl: list, op_data: dict):
        if 'status' in op_data['params']:
            tpl.append(f'状态：{EnableStatus(op_data["params"]["status"]).label}')
        if 'adult_markup_type' in op_data['params']:
            tpl.append(f'成人加价类型：{FareRuleMarkupType(op_data["params"]["adult_markup_type"]).label}')
        if 'child_markup_type' in op_data['params']:
            tpl.append(f'儿童加价类型：{FareRuleMarkupType(op_data["params"]["child_markup_type"]).label}')
        if 'infant_markup_type' in op_data['params']:
            tpl.append(f'婴儿加价类型：{FareRuleMarkupType(op_data["params"]["infant_markup_type"]).label}')
        if 'fare_valid_type' in op_data['params']:
            tpl.append(f'投放生效类型：{FareValidType(op_data["params"]["fare_valid_type"]).label}')
        if 'flight_date_type' in op_data['params']:
            tpl.append(f'航班日期类型：{FareFlightDateType(op_data["params"]["flight_date_type"]).label}')
        if 'auto_float' in op_data['params']:
            tpl.append(f'是否启用自动调价：{EnableStatus(op_data["params"]["auto_float"]).label}')
        if 'update_model' in op_data['params']:
            tpl.append(f'批量更新模式：{UpdateModeCode(op_data["params"]["update_model"]).label}')
        if 'fare_type' in op_data['params']:
            tpl.append(f'报价类型：{FareType(op_data["params"]["fare_type"]).label}')

        if 'add_ons' in op_data['params']:
            for ao in op_data['params']['add_ons']:
                tpl.append(
                    f'辅营：{AddOnType(ao["add_on_type"]).label} {FareRuleMarkupType(ao["markup_type"]).label} {ao["add_on_tag_price"]}'
                )

        else:
            tpl.append(f'辅营：无')
        if 'baggages' in op_data['params']:
            for bg in op_data['params']['baggages']:
                tpl.append(
                    f'{BaggageType(bg["baggage_type"]).label} {bg["baggage_weight"]} Kg. {FareRuleMarkupType(bg["markup_type"]).label} {bg["baggage_tag_price"]}'
                )
        else:
            tpl.append(f'行李：无')
        return tpl

    async def do_create(self, id: int, op_data: dict):
        tpl = [self.FIELDS_TPL[pm] for pm in op_data['params'].keys() if pm in self.FIELDS_TPL]
        tpl = self.append_tpl(tpl=tpl, op_data=op_data)
        tpl.append("{message}")
        await self.save_log(
            op_table=self.table, row_id=id, op_type='创建投放策略', desc_tpl="，".join(tpl), op_data=op_data
        )

    async def do_update(self, id: int, op_data: dict):
        tpl = [self.FIELDS_TPL[pm] for pm in op_data['params'].keys() if pm in self.FIELDS_TPL]
        tpl = self.append_tpl(tpl=tpl, op_data=op_data)

        tpl.append("{message}")
        await self.save_log(
            op_table=self.table, row_id=id, op_type='修改投放策略', desc_tpl='，'.join(tpl), op_data=op_data
        )

    async def do_batch_enable(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='批量启用', desc_tpl="{message}", op_data=op_data)

    async def do_batch_disable(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='批量禁用', desc_tpl="{message}", op_data=op_data)
