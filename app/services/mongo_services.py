import copy
from datetime import datetime
import hashlib
from typing import Any, List
from uuid import uuid4

from bson import ObjectId
from loguru import logger
import orjson
from pymongo import UpdateOne

from functools import reduce
from app.extensions.db_extras import mongo_client


class FlightFareMongoService:
    def __init__(self, db_name):
        self.db = mongo_client[db_name]

    async def delete_cache(
        self,
        collection_name: str,
        site_code: str,
        dep_airport_code: str,
        arr_airport_code: str,
        dep_date: str,
        return_date: str,
        flight_nos: List[str] = None,
        invalid_flight_nos: List[str] = None,
    ):
        collection = self.db[collection_name]
        query = {
            'task_info.site_code': site_code,
            'task_info.dep_airport_code': dep_airport_code,
            'task_info.arr_airport_code': arr_airport_code,
            'task_info.dep_date': dep_date,
            'task_info.return_date': return_date,
            # 删除时要判断过期时间，否则会发生数据衔接问题，导致验价失败
            # 'task_info.expire_time': {'$lte': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
        }
        if flight_nos:
            # 删除航班号不匹配的记录
            query['result.flight_no'] = {'$nin': flight_nos}
        if invalid_flight_nos:
            # 删除航班号匹配的记录
            query['result.flight_no'] = {'$in': invalid_flight_nos}

        logger.info(f'删除缓存: {query}')
        await collection.delete_many(query)

    async def update_cache(self, datas: list, collection_name: str):

        await self.bulk_update_cache(datas=datas, collection_name=collection_name)

    async def bulk_update_cache(self, datas: list, collection_name: str):
        collection = self.db[collection_name]
        await self.check_index(
            collection_name=collection_name,
            index_name='fare_update_index',
            index_fields=[
                ('task_info.task_key', 1),
                ('result.trip_type', 1),
                ('result.trip_index', 1),
                ('result.segment_index', 1),
                ('result.dep_airport_code', 1),
                ('result.arr_airport_code', 1),
                ('result.dep_date', 1),
                ('task_info.site_code', 1),
                ('task_info.return_date', 1),
                ('result.flight_no', 1),
            ],
        )

        operations = [
            UpdateOne(
                {
                    "task_info.task_key": doc["task_info"]["task_key"],
                    "result.trip_type": doc["result"]["trip_type"],
                    "result.trip_index": doc["result"]["trip_index"],
                    "result.segment_index": doc["result"]["segment_index"],
                    "result.dep_airport_code": doc["result"]["dep_airport_code"],
                    "result.arr_airport_code": doc["result"]["arr_airport_code"],
                    "result.dep_date": doc["task_info"]["dep_date"],
                    "task_info.site_code": doc["task_info"]["site_code"],
                    "task_info.return_date": doc["task_info"]["return_date"],
                    "result.flight_no": doc["result"]["flight_no"],
                },
                {"$set": doc},
                upsert=True,
            )  # 查找条件  # 更新内容  # 如果记录不存在则插入
            for doc in datas
        ]
        return await collection.bulk_write(operations)

    async def get_ow_datas(
        self,
        collection_name: str,
        airline_code: str,
        dep_city_code: str,
        arr_city_code: str,
        dep_date: str,
        return_date: str = None,
        trip_type: str = 'ow',
        limit: int = 10000,
    ):
        """获取单程数据（或联程中某段）"""
        collection = self.db[collection_name]
        query = {
            'task_info.airline_code': airline_code,
            '$or': [
                {'result.dep_city_code': dep_city_code, 'result.arr_city_code': arr_city_code},
                {'result.dep_airport_code': dep_city_code, 'result.arr_airport_code': arr_city_code},
            ],
            'result.dep_date': dep_date,
            'result.trip_type': trip_type,
            'task_info.expire_time': {'$gte': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
        }
        if return_date:
            query['result.return_date'] = return_date
        logger.debug(query)
        return await collection.find(query).to_list(length=limit)

    async def check_index(self, collection_name: str, index_name: str, index_fields: dict, force: bool = False):
        collection = self.db[collection_name]
        # 检查并创建更新操作的复合索引
        existing_indexes = await collection.index_information()
        if index_name in existing_indexes and not force:
            logger.debug(f'索引 {index_name} 已存在，跳过创建')
            return
        await collection.create_index(index_fields, name=index_name)
        logger.debug(f'索引 {index_name} 创建成功')

    async def search(
        self,
        collection_name: str,
        airline_code: str = None,
        dep_airport_code: str = None,
        arr_airport_code: str = None,
        data_range: List[str] = None,
        max_quantity: int = None,
        max_price: float = None,
        flight_no: str = None,
        is_expired: Any = None,
        offset: int = 0,
        limit: int = 10000,
    ):
        collection = self.db[collection_name]
        query = {}
        if airline_code:
            query['task_info.airline_code'] = airline_code.upper()
        if dep_airport_code:
            query['task_info.dep_airport_code'] = dep_airport_code.upper()
        if arr_airport_code:
            query['task_info.arr_airport_code'] = arr_airport_code.upper()
        logger.debug(is_expired)
        if data_range:
            query['task_info.dep_date'] = {'$gte': data_range[0], '$lte': data_range[1]}
        if max_quantity:
            query['result.fares.adult.quantity'] = {'$lte': max_quantity}
        if max_price:
            query['result.fares.adult.base'] = {'$lte': max_price}

        if is_expired is False:
            query['task_info.expire_time'] = {'$gte': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        elif is_expired is True:
            query['task_info.expire_time'] = {'$lte': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            # 查询过期时先删除已起飞的数据
            await collection.delete_many({'task_info.dep_date': {'$lt': datetime.now().strftime('%Y-%m-%d')}})
            logger.info('删除已起飞的数据')

        # if return_date:
        #     query['task_info.return_date'] = return_date
        if flight_no:
            query['result.flight_no'] = flight_no.upper()
        if is_expired:
            query['task_info.expire_time'] = {'$lte': datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            # query['task_info.expire_time'] = 1
        # logger.debug(query)
        # logger.debug(offset)
        # logger.debug(limit)
        total = 0
        if query:
            total = await collection.count_documents(query)
            if total <= limit:
                offset = 0
            rows = await collection.find(query).skip(offset).limit(limit).to_list(length=limit)
        else:
            total = await collection.count_documents(query)
            if total <= limit:
                offset = 0
            rows = await collection.find().skip(offset).limit(limit).to_list(length=limit)
        return total, rows

    # 暂不实现联程
    # 联程运价修改，20240830讨论
    # 方案：默认指定/例外航班如果都是空时，表示单程、联程通用
    # 需要指定时TR101表示单程，TR101/*表示前段匹配，*/TR102表示后段匹配，TR101/TR102表示精确匹配

    # async def get_ct_datas(
    #     self,
    #     collection_name: str,
    #     airline_code: str,
    #     dep_airport_code: str,
    #     arr_airport_code: str,
    #     dep_date: str,
    #     return_date: str = None,
    #     trip_type: str = 'ct',
    #     limit: int = 10000,
    # ):
    #     """获取联程数据"""
    #     collection = self.db[collection_name]
    #     query = {
    #         'result.trip_type': trip_type,
    #         'task_info.airline_code': airline_code,
    #         "$or": [{'result.dep_airport_code': dep_airport_code}, {'result.arr_airport_code': arr_airport_code}],
    #         'result.dep_date': dep_date,
    #     }
    #     if return_date:
    #         query['result.return_date'] = return_date
    #     return await collection.find(query).to_list(length=limit)

    async def insert_one(self, collection_name: str, data: dict):
        collection = self.db[collection_name]
        await collection.insert_one(data)


# 工具函数：根据嵌套路径获取字典中的值
def get_nested_value(doc: dict, keys: list):
    try:
        return reduce(lambda d, key: d.get(key) if d else None, keys, doc)
    except KeyError:
        return None


class CommonMongoService:
    def __init__(self, db_name, collection_name):
        self.collection_name = collection_name
        self.db = mongo_client[db_name]
        self.collection = self.db[self.collection_name]

    # async def check_index(self, index_name: str, index_fields: dict, force: bool = False):
    #     # 检查并创建更新操作的复合索引
    #     existing_indexes = await self.collection.index_information()
    #     if index_name in existing_indexes and not force:
    #         logger.debug(f'索引 {index_name} 已存在，跳过创建')
    #         return
    #     await self.collection.create_index(index_fields, name=index_name)
    #     logger.debug(f'索引 {index_name} 创建成功')

    async def check_index(self, index_name: str, index_fields: list, force: bool = False):
        """
        检查并创建复合索引，如果索引存在且字段不一致，删除并重新创建。

        :param index_name: 索引名称
        :param index_fields: 索引字段，格式为[(field1, order), (field2, order), ...]
        :param force: 是否强制重建索引，默认为False
        """
        # 获取现有的索引信息
        existing_indexes = await self.collection.index_information()

        # 检查索引是否存在
        if index_name in existing_indexes:
            existing_index_fields = existing_indexes[index_name]['key']

            # 如果字段一致且force为False，跳过创建
            if existing_index_fields == index_fields and not force:
                logger.debug(f'索引 {index_name} 已存在且字段一致，跳过创建')
                return
            else:
                # 如果字段不一致或force为True，删除现有索引
                logger.debug(f'索引 {index_name} 字段不一致或强制重建，删除旧索引')
                await self.collection.drop_index(index_name)

        # 创建新索引
        await self.collection.create_index(index_fields, name=index_name)
        logger.debug(f'索引 {index_name} 创建成功')

    async def bulk_insert_or_update(self, datas: list, unique_keys: list):

        # 自动生成索引字段
        index_fields = [(key, 1) for key in unique_keys]

        # 检查并创建唯一键复合索引
        await self.check_index(index_name=f"{self.collection_name}_unique_index", index_fields=index_fields)

        # 动态生成查询条件和更新操作
        operations = []
        for doc in datas:
            # 根据唯一键列表生成查询条件
            query = {key: get_nested_value(doc, key.split(".")) for key in unique_keys}

            # 创建 UpdateOne 操作
            operations.append(UpdateOne(query, {"$set": doc}, upsert=True))  # 查找条件  # 更新内容  # 如果不存在则插入

        # 批量更新或插入操作
        return await self.collection.bulk_write(operations)

    async def search(self, query: dict = {}, offset: int = 0, limit: int = 10000):
        logger.debug(f"search: {query}")
        total = 0
        if query:
            total = await self.collection.count_documents(query)
            if total <= limit:
                offset = 0
            rows = await self.collection.find(query).skip(offset).limit(limit).to_list(length=limit)
        else:
            total = await self.collection.count_documents(query)
            if total <= limit:
                offset = 0
            rows = await self.collection.find().skip(offset).limit(limit).to_list(length=limit)
        return total, rows

    async def delete_by_ids(self, ids: list):
        # await self.collection.delete_many({'_id': {'$in': ids}})
        await self.delete_by_query({'_id': {'$in': [ObjectId(id) for id in ids]}})

    async def delete_by_query(self, query: dict):
        logger.debug(f"delete_by_query: {query}")
        await self.collection.delete_many(query)

    async def update_by_id(self, id: str, update: dict):
        logger.debug(f"update_by_id: {id} {update}")
        await self.collection.update_one({'_id': ObjectId(id)}, {'$set': update})

    async def update_by_query(self, query: dict, update: dict):
        logger.debug(f"update_by_query: {query} {update}")
        await self.collection.update_many(query, update)

    async def find_one(self, query: dict):
        logger.debug(f"find_one: {query}")
        return await self.collection.find_one(query)

    async def count(self, query: dict):
        logger.debug(f"count: {query}")
        return await self.collection.count_documents(query)
