import base64
import copy
from datetime import datetime, timedelta
import hashlib
import math
import time
from typing import List, Union
from uuid import uuid4

from fastapi import Request
from app.config import ASYNC_REDIS_CFG, settings
from loguru import logger
import or<PERSON><PERSON>
from sqlalchemy import and_, or_
from app import tasks, utils
from app.consts.status import TaskStatus
from app.consts.types import FareChannelType, FareFlightDateType, FareRuleMarkupType, FareType, FareValidType
from app.models.airline import Airline
from app.models.airline_rate import AirlineRate
from app.models.fare_actual_float import FareActualFloat
from app.models.fare_rule import FareRule
from app.models.fare_rule_baggage import FareRuleBaggage

from app.models.tb_fare_data import TBFareData
from app.models.verify_task import VerifyTask
from app.models.verify_tmp_order import VerifyTmpOrder
from app.sdks.flight_pre_order_sdk import FlightPreOrderSdk
from app.services import (
    base_data_service,
    crawler_callback_services,
    hood_service,
    task_services,
    verify_tmp_order_service,
)
from app.services.mongo_services import CommonMongoService, FlightFareMongoService
from commons import sdks
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus

from app.depends import redis_pool
from commons.extensions.redis_extras import AsyncRedisPool
from commons.sdks.base import SdkClient
from commons.sdks.pay_center.exchange_rate import GetExchangeRateRequest


async def get_fare_rule_map(
    today, channel_code, dep_city_code, arr_city_code, dep_date, fare_type: str = FareType.NORMAL.value
):
    # flight_days = (datetime.strptime(dep_date, '%Y-%m-%d') - datetime.now()).days
    flight_days = (datetime.strptime(dep_date, '%Y-%m-%d').date() - datetime.now().date()).days

    dep_airport_codes = await base_data_service.get_airport_codes(city_code=dep_city_code) + ['*']
    arr_airport_codes = await base_data_service.get_airport_codes(city_code=arr_city_code) + ['*']

    fare_rule_rows = await FareRule.get_all_async(
        FareRule.channel_code == channel_code,
        FareRule.status == EnableStatus.ENABLED.value,
        FareRule.fare_type == fare_type,
        FareRule.dep_airport_code.in_(dep_airport_codes),
        FareRule.arr_airport_code.in_(arr_airport_codes),
        and_(
            # or_(
            #     and_(
            #         or_(FareRule.dep_city_code == dep_city_code, FareRule.dep_city_code == '*'),
            #         or_(FareRule.arr_city_code == arr_city_code, FareRule.arr_city_code == '*'),
            #     ),
            #     and_(
            #         or_(FareRule.dep_airport_code == dep_city_code, FareRule.dep_airport_code == '*'),
            #         or_(FareRule.arr_airport_code == arr_city_code, FareRule.arr_airport_code == '*'),
            #     ),
            # ),
            or_(
                FareRule.fare_valid_type == FareValidType.ALWAYS.value,
                and_(
                    FareRule.fare_valid_type == FareValidType.DATE.value,
                    FareRule.fare_start_date <= today,
                    FareRule.fare_end_date >= today,
                ),
            ),
            or_(
                and_(
                    FareRule.flight_date_type == FareFlightDateType.DATE.value,
                    FareRule.flight_start_date <= dep_date,
                    FareRule.flight_end_date >= dep_date,
                ),
                and_(
                    FareRule.flight_date_type == FareFlightDateType.DAYS.value,
                    FareRule.start_days <= flight_days,
                    FareRule.end_days >= flight_days,
                ),
            ),
        ),
        # FareRule.flight_start_date <= dep_date,
        # FareRule.flight_end_date >= dep_date,
        order_by=[FareRule.priority_level.desc()],
    )
    logger.info(f'运价数量：{len(fare_rule_rows)}')
    fare_rule_map = {}
    for fare_rule_row in fare_rule_rows:
        if fare_rule_row['airline_code'] not in fare_rule_map:
            fare_rule_map[fare_rule_row['airline_code']] = []
        fare_rule_map[fare_rule_row['airline_code']].append(fare_rule_row)

    return fare_rule_map


async def coumpute_markup(orgin_price, markup_type, markup):
    markup_price = orgin_price
    if markup_type == FareRuleMarkupType.PERCENT.value:
        markup_price = math.ceil(markup / 100 * orgin_price)
    elif markup_type == FareRuleMarkupType.AMOUNT.value:
        markup_price = math.ceil(orgin_price + markup)
    elif markup_type == FareRuleMarkupType.FIX_AMOUNT.value:
        markup_price = math.ceil(markup)

    return markup_price


async def get_actual_float(
    channel_code: str,
    dep_city_code: str,
    arr_city_code: str,
    dep_date: str,
    flight_no: str,
    checked_baggage_weight: float,
    total_price: float,
    min_float: float,
    max_float: float,
    log_suffix: str = '',
):
    logger.debug('计算实际浮动')
    find_channel_code = channel_code
    # 对比类型
    # 普通，无托运 和 普通，所有对比
    # 普通，有托运 和 普通，有托运对比
    # 打包，有托运 和 普通，有托运对比（同时注意区分打包档次）
    # 金牌，无托运 和 金牌，所有对比
    # 金牌，有托运 和 金牌，有托运对比

    if channel_code == FareChannelType.TB_BAGGAGE.value:
        find_channel_code = FareChannelType.TB_NORMAL.value

    up_time = (datetime.now() - timedelta(minutes=settings.TB_DATA_FETCH_INTERVAL)).strftime('%Y-%m-%d %H:%M:%S')
    newest_row = await TBFareData.get_by_async(
        TBFareData.dep_city_code == dep_city_code,
        TBFareData.arr_city_code == arr_city_code,
        TBFareData.flight_date == dep_date,
        TBFareData.flight_no == flight_no,
        TBFareData.channel_code == find_channel_code,
        TBFareData.created >= up_time,
        columns=[TBFareData.batch_no],
        order_by=[TBFareData.created.desc()],
    )

    if not newest_row:
        logger.info(f'查无比对数据，停止自动调价 {log_suffix}')
        return 0

    tb_datas = await TBFareData.get_all_async(
        TBFareData.dep_city_code == dep_city_code,
        TBFareData.arr_city_code == arr_city_code,
        TBFareData.flight_date == dep_date,
        TBFareData.flight_no == flight_no,
        TBFareData.batch_no == newest_row['batch_no'],
        TBFareData.channel_code == find_channel_code,
        TBFareData.created >= up_time,
        order_by=[TBFareData.adult_total],
    )
    logger.debug(f'tb_datas: {tb_datas}')
    if not tb_datas:
        logger.info(f'查无比对数据，停止自动调价 {log_suffix}')
        return 0

    valid_batch = tb_datas[0]['batch_no']
    logger.info(f'基于 {valid_batch} 批次进行对比 {log_suffix}')
    tb_datas = [x for x in tb_datas if x['batch_no'] == valid_batch]

    # 根据行李限制，过滤数据
    if checked_baggage_weight:
        new_list = [
            x
            for x in tb_datas
            if x['baggage_min_weight'] > 0
            and checked_baggage_weight >= x['baggage_min_weight']
            and (x['baggage_max_weight'] == 0 or checked_baggage_weight < x['baggage_max_weight'])
        ]
        logger.info(f'只对比相同行李范围内的数据，共 {len(new_list)} 条 {log_suffix}')
    else:
        # 不含行李时，只和无行李对比
        new_list = [x for x in tb_datas if x['baggage_min_weight'] == 0 and x['baggage_max_weight'] == 0]
        # new_list = copy.deepcopy(tb_datas)

    if not new_list:
        logger.info(f'判断行李限制后，无数据，停止自动调价 {log_suffix}')
        return 0

    logger.info(f'对比数据 {len(new_list)} 条 {log_suffix}')

    # 本身为最低价，则与次低价对比
    if new_list[0]['adult_total'] == new_list[0]['self_total']:
        actual_float = max_float
        if len(new_list) > 1:
            second_diff = new_list[1]['adult_total'] - 1 - total_price
            if second_diff > min_float and second_diff < max_float:
                actual_float = second_diff
            elif second_diff < min_float:
                actual_float = min_float
            elif second_diff > max_float:
                actual_float = max_float

        if actual_float == max_float:
            logger.info(
                f'hy为最低价 {log_suffix}，向上浮动到最大值: {total_price+max_float}({total_price}+{max_float})'
            )
        elif actual_float == min_float:
            logger.info(
                f'hy为最低价 {log_suffix}，航司可能已升舱，向下浮动到最小值: {total_price+min_float}({total_price}+{min_float})'
            )
        else:
            logger.info(
                f'hy为最低价 {log_suffix}，向上浮动到次低价: {total_price+actual_float}({total_price}+{actual_float})'
            )
        return actual_float

    # 剔除自己后，算出所有差额
    diff_price_list = [x['adult_total'] - 1 - total_price for x in new_list if x['adult_total'] != x['self_total']]

    if not diff_price_list:
        logger.info(f'扣除自身投放后，无差额，停止自动调价 {log_suffix}')
        return 0

    # 计算实际浮动
    actual_float = 0
    if diff_price_list[0] < min_float:
        # 如果差额小于最小浮动，则使用最小浮动
        actual_float = min_float
    else:
        # 如果差额不超过最大浮动，按次差值计算
        # 否则按最大浮动计算
        actual_float = diff_price_list[0] if diff_price_list[0] < max_float else max_float

    logger.debug(f'diff_price_list: {diff_price_list}')
    logger.info(f'{log_suffix} 与最低价相差：{diff_price_list[0]}')
    return actual_float


async def apply_rule(trip_info, fare_rule_row, update_actual_float: bool = False):
    trip_info['fare_rule_snapshot'] = fare_rule_row

    new_fares = {}
    checked_baggage_weight = 0

    # 将includes中的行李节点转成list，方便追加
    if trip_info.get('includes', {}).get('baggage', {}).get('checked_baggage'):
        if isinstance(trip_info['includes']['baggage']['checked_baggage'], dict):
            trip_info['includes']['baggage']['checked_baggage'] = [trip_info['includes']['baggage']['checked_baggage']]
    if trip_info.get('includes', {}).get('baggage', {}).get('cabin_baggage'):
        if isinstance(trip_info['includes']['baggage']['cabin_baggage'], dict):
            trip_info['includes']['baggage']['cabin_baggage'] = [trip_info['includes']['baggage']['cabin_baggage']]
    if trip_info.get('includes', {}).get('baggage', {}).get('hand_baggage'):
        if isinstance(trip_info['includes']['baggage']['hand_baggage'], dict):
            trip_info['includes']['baggage']['hand_baggage'] = [trip_info['includes']['baggage']['hand_baggage']]

    baggages = None
    if fare_rule_row['channel_code'] in [FareChannelType.TB_BAGGAGE.value]:
        baggages = await FareRuleBaggage.get_all_async(FareRuleBaggage.fare_rule_id == fare_rule_row['id'])
    logger.debug(f'行李打包 {baggages} {trip_info}')
    if baggages:
        if not trip_info.get('includes'):
            trip_info['includes'] = {}
        if not trip_info['includes'].get('baggage'):
            trip_info['includes']['baggage'] = {}
        if not trip_info['includes']['baggage'].get('checked_baggage'):
            trip_info['includes']['baggage']['checked_baggage'] = []
        for baggage in baggages:
            trip_info['includes']['baggage']['checked_baggage'].append(
                {'count': 1, 'weight': baggage['baggage_weight'], 'all_weight': baggage['baggage_weight']}
            )
    if trip_info.get('includes', {}).get('baggage', {}).get('checked_baggage'):
        checked_baggage_weight = sum(
            [baggage['weight'] for baggage in trip_info['includes']['baggage']['checked_baggage']]
        )
    baggage_weight_range = '0'
    if checked_baggage_weight > 0 and checked_baggage_weight < 15:
        baggage_weight_range = '0,15'
    elif checked_baggage_weight >= 15 and checked_baggage_weight < 30:
        baggage_weight_range = '15,30'
    elif checked_baggage_weight >= 30:
        baggage_weight_range = '30'

    # 判断是否开启自动调价
    enable_auto_float = fare_rule_row.get('auto_float', '') == EnableStatus.ENABLED.value
    logger.debug(f'自动调价 {enable_auto_float}')
    min_float = fare_rule_row.get('auto_min_float', 0)
    max_float = fare_rule_row.get('auto_max_float', 0)

    actual_float = 0
    trip_info['enable_auto_float'] = enable_auto_float
    trip_info['max_float'] = max_float
    trip_info['min_float'] = min_float
    trip_info['actual_float'] = 0
    for fk, fv in trip_info['fares'].items():

        markup_type = fare_rule_row.get(f'{fk}_markup_type')

        if markup_type:
            # 对航司原价进行统一处理
            fv['src_base'] = round(float(fv['src_base']), 2)
            fv['src_tax'] = round(float(fv['src_tax']), 2)
            fv['src_total'] = round(float(fv['src_total']), 2)

            fv['src_cny_base'] = fv['base']
            fv['src_cny_tax'] = fv['tax']
            fv['src_cny_total'] = fv['total']
            fv['base'] = await coumpute_markup(
                orgin_price=fv['base'], markup=fare_rule_row[f'{fk}_tag_base'], markup_type=markup_type
            )

            fv['tax'] = await coumpute_markup(
                orgin_price=fv['tax'], markup=fare_rule_row[f'{fk}_tag_tax'], markup_type=markup_type
            )

            fv['total'] = fv['base'] + fv['tax']

            if baggages:
                for b in baggages:
                    fv['base'] += b['baggage_tag_price']
                    fv['total'] += b['baggage_tag_price']
            log_suffix = f'{fare_rule_row["channel_code"]}-{trip_info["segments"][0]["dep_airport_code"]}-{trip_info["segments"][0]["arr_airport_code"]}-{trip_info["segments"][0]["dep_date"]}-{trip_info["segments"][0]["flight_no"]}'

            if enable_auto_float and fk == 'adult':
                # fv['max_float'] = max_float
                # fv['min_float'] = min_float

                if update_actual_float:
                    logger.info(f'自动调价，重新计算浮动值 {log_suffix}')
                    dep_city_code = trip_info['segments'][0].get(
                        'dep_city_code',
                        await base_data_service.get_city_code(trip_info['segments'][0]['dep_airport_code']),
                    )
                    arr_city_code = trip_info['segments'][0].get(
                        'arr_city_code',
                        await base_data_service.get_city_code(trip_info['segments'][0]['arr_airport_code']),
                    )
                    actual_float = await get_actual_float(
                        channel_code=fare_rule_row['channel_code'],
                        dep_city_code=dep_city_code,
                        arr_city_code=arr_city_code,
                        dep_date=trip_info['segments'][0]['dep_date'],
                        flight_no=trip_info['segments'][0]['flight_no'],
                        checked_baggage_weight=checked_baggage_weight,
                        total_price=fv['total'],
                        min_float=min_float,
                        max_float=max_float,
                        log_suffix=log_suffix,
                    )

                    await FareActualFloat.bulk_insert_or_update(
                        records=[
                            {
                                "channel_code": fare_rule_row['channel_code'],
                                "airline_code": trip_info['segments'][0]['flight_no'][:2],
                                "dep_airport_code": trip_info['segments'][0]['dep_airport_code'],
                                "arr_airport_code": trip_info['segments'][0]['arr_airport_code'],
                                "dep_date": trip_info['segments'][0]['dep_date'],
                                "flight_no": trip_info['segments'][0]['flight_no'],
                                "actual_float": actual_float,
                                "baggage_weight_range": baggage_weight_range,
                            }
                        ],
                        update_keys=[
                            "channel_code",
                            "dep_airport_code",
                            "arr_airport_code",
                            "dep_date",
                            "flight_no",
                            "baggage_weight_range",
                        ],
                    )
                else:
                    logger.info(f'自动调价，使用历史浮动值 {log_suffix} ')
                    up_time = datetime.now() - timedelta(hours=24)
                    actual_float_row = await FareActualFloat.get_by_async(
                        FareActualFloat.channel_code == fare_rule_row['channel_code'],
                        FareActualFloat.dep_airport_code == trip_info['segments'][0]['dep_airport_code'],
                        FareActualFloat.arr_airport_code == trip_info['segments'][0]['arr_airport_code'],
                        FareActualFloat.dep_date == trip_info['segments'][0]['dep_date'],
                        FareActualFloat.flight_no == trip_info['segments'][0]['flight_no'],
                        FareActualFloat.baggage_weight_range == baggage_weight_range,
                        FareActualFloat.updated >= up_time,
                    )

                    if actual_float_row:
                        logger.info(
                            f'自动调价，使用历史浮动值 {log_suffix} id: {actual_float_row["id"]} actual_float: {actual_float_row["actual_float"]} baggage_weight_range: {actual_float_row["baggage_weight_range"]}'
                        )
                        actual_float = actual_float_row['actual_float']
                        if actual_float > max_float:
                            actual_float = max_float
                            logger.info(f'历史浮动值超过最大浮动，使用最大浮动值：{actual_float}')
                        elif actual_float < min_float:
                            actual_float = min_float
                            logger.info(f'历史浮动值低于最小浮动，使用最小浮动值：{actual_float}')

                    else:
                        actual_float = 0
                        logger.info(f'自动调价 无实际浮动数据 {log_suffix}')

            if actual_float:
                # logger.info(
                #     f'自动调价 {log_suffix} 实际浮动:{actual_float}, {fk} 浮动前 总价:{fv["total"]}（{fv["base"]} + {fv["tax"]}）'
                # )
                # fv['base'] = fv['base'] + actual_float
                # fv['total'] = fv['total'] + actual_float
                # logger.info(
                #     f'自动调价 {log_suffix} 实际浮动:{actual_float}, {fk} 浮动后 总价:{fv["total"]}（{fv["base"]} + {fv["tax"]}）'
                # )
                trip_info['actual_float'] = actual_float
            # 在所有调整结束后检查
            # 如果原价小于1，则将原价设置为1
            if fv['base'] < 1:
                # 因为航司原价小于1，这里改成 1 相当于加价，所以差额是用原价减去1
                diff = fv['base'] - 1  # 这里应该会得到一个负数，所以下面是+diff
                # 将差额加（减）到税上
                fv['base'] = 1
                fv['tax'] = fv['tax'] + diff

            new_fares[fk] = fv
    trip_info['fares'] = new_fares


async def append_ow_datas(data_map: dict, fare_rule_rows: list, ow_datas: list, update_actual_float: bool = False):
    mg_datas = crawler_callback_services.merge_results(datas=ow_datas)
    return await append_fare_rule(
        data_map=data_map, fare_rule_rows=fare_rule_rows, mg_datas=mg_datas, update_actual_float=update_actual_float
    )


async def append_fare_rule(data_map: dict, fare_rule_rows: list, mg_datas: dict, update_actual_float: bool = False):
    unique_id = mg_datas['task_info']['unique_id']
    for mg_data in mg_datas['data']['results']:
        trip_info = mg_data['trips'][0]
        mg_data['unique_id'] = unique_id
        flight_no = ','.join(trip_info['flight_nos'])
        cabin_code = ','.join(trip_info['cabin_codes'])
        for fare_rule_row in fare_rule_rows:
            if flight_no in fare_rule_row['exclude_flight_nos'].split('/'):
                logger.debug(
                    f'{fare_rule_row["id"]} flight_no {flight_no} in exclude_flight_nos {fare_rule_row["exclude_flight_nos"]}'
                )
                continue
            if fare_rule_row['flight_nos'] not in ('*', '') and flight_no not in fare_rule_row['flight_nos'].split('/'):
                logger.debug(
                    f'{fare_rule_row["id"]} flight_no {flight_no} not in flight_nos {fare_rule_row["flight_nos"]}'
                )
                continue
            if cabin_code not in fare_rule_row['cabin_codes'].split('/') and fare_rule_row['cabin_codes'] not in (
                '*',
                '',
            ):
                logger.debug(
                    f'{fare_rule_row["id"]} cabin_code {cabin_code} not in cabin_codes {fare_rule_row["cabin_codes"]}'
                )
                continue
            # 因为改成了按城市匹配运价，所以这里需要严格判断机场
            if (
                fare_rule_row['dep_airport_code'] != '*'
                and trip_info['dep_airport_code'] != fare_rule_row['dep_airport_code']
            ):
                logger.debug(
                    f'{fare_rule_row["id"]} dep_airport_code {trip_info["dep_airport_code"]} not in dep_airport_code {fare_rule_row["dep_airport_code"]}'
                )
                continue
            if (
                fare_rule_row['arr_airport_code'] != '*'
                and trip_info['arr_airport_code'] != fare_rule_row['arr_airport_code']
            ):
                logger.debug(
                    f'运价ID {fare_rule_row["id"]} arr_airport_code {trip_info["arr_airport_code"]} not in arr_airport_code {fare_rule_row["arr_airport_code"]}'
                )
                continue
            if fare_rule_row['channel_code'] in [FareChannelType.TB_BAGGAGE.value] and trip_info.get(
                'includes', {}
            ).get('baggage', {}).get('checked_baggage'):
                logger.warning(
                    f'运价ID {fare_rule_row["id"]} 为打包类型，不可售卖含托运行李的航班 {trip_info["segments"][0]["batch_key"]} includes: {trip_info["includes"]}'
                )
                continue

            logger.debug(trip_info)
            logger.debug(fare_rule_row)
            await apply_rule(trip_info=trip_info, fare_rule_row=fare_rule_row, update_actual_float=update_actual_float)

            rs_key = f'ow_{flight_no}'
            if rs_key in data_map:
                if trip_info['fares']['adult']['total'] < data_map[rs_key]['trips'][0]['fares']['adult']['total']:
                    data_map[rs_key] = mg_data
            else:
                data_map[rs_key] = mg_data
            break


async def append_journey_data(data_map: dict, fare_rule_rows: list, journey_datas: list):
    for journey_data in journey_datas:
        journey_data['result']['origin_trip_type'] = journey_data['result']['trip_type']
        journey_data['result']['trip_type'] = 'ow'
        journey_data['result']['is_journey'] = True
    return await append_ow_datas(data_map=data_map, fare_rule_rows=fare_rule_rows, ow_datas=journey_datas)


async def get_flight_info_map(airline_codes: list, dep_airport_code: str, arr_airport_code: str, dep_date: str):
    mongo_service = CommonMongoService(db_name='base_data', collection_name='flight_info')
    weekday = datetime.strptime(dep_date, '%Y-%m-%d').isoweekday()
    query = {
        'airline_code': {'$in': airline_codes},
        'dep_airport_code': dep_airport_code,
        'arr_airport_code': arr_airport_code,
        # 'start_date': {'$lte': dep_date},
        # 'end_date': {'$gte': dep_date},
        # 基础数据有错误，这里暂时不判断班期，仅靠有效期控制
        # 'schedules': {'$regex': f'{weekday}', "$options": "i"},
    }
    total, flight_infos = await mongo_service.search(query=query)
    logger.debug(flight_infos)
    flight_info_map = {}
    for flight_info in flight_infos:
        logger.debug(flight_info)
        f_key = f'{flight_info["airline_code"]}_{flight_info["dep_airport_code"]}_{flight_info["arr_airport_code"]}_{flight_info["flight_no"]}'
        if f_key not in flight_info_map:
            flight_info_map[f_key] = flight_info
    return flight_info_map


async def search_simple(
    channel_code: str,
    dep_city_code: str,
    arr_city_code: str,
    dep_date: str,
    return_date: str = None,
    adult: int = 1,
    child: int = 0,
    infant: int = 0,
    update_actual_float: bool = False,
):
    data_map = {}
    today = str(datetime.now().date())
    # 获取投放策略
    fare_rule_map = await get_fare_rule_map(
        channel_code=channel_code,
        dep_city_code=dep_city_code,
        arr_city_code=arr_city_code,
        dep_date=dep_date,
        today=today,
    )

    logger.debug(fare_rule_map)

    flight_fare_mongo = FlightFareMongoService('flight_fare')
    collection_name = 'airline_fare_cache'

    await flight_fare_mongo.check_index(
        collection_name=collection_name,
        index_name='fare_search_index',
        index_fields=[
            ('task_info.airline_code', 1),
            ('result.dep_city_code', 1),
            ('result.arr_city_code', 1),
            ('result.dep_airport_code', 1),
            ('result.arr_airport_code', 1),
            ('result.dep_date', 1),
            ('result.trip_type', 1),
        ],
    )

    for airline_code, fare_rule_rows in fare_rule_map.items():
        airline_row = await Airline.get_by_async(Airline.airline_code == airline_code)
        ow_datas = await flight_fare_mongo.get_ow_datas(
            collection_name=collection_name,
            airline_code=airline_code,
            dep_city_code=dep_city_code,
            arr_city_code=arr_city_code,
            dep_date=dep_date,
            return_date=return_date,
            trip_type='ow',
        )
        logger.debug(f'ow_datas: {ow_datas}')
        if ow_datas:
            await append_ow_datas(
                data_map=data_map,
                fare_rule_rows=fare_rule_rows,
                ow_datas=ow_datas,
                update_actual_float=update_actual_float,
            )

        logger.debug(f'ow_datas: {ow_datas}')
        # 弃程
        if airline_row['abandoned_journey'] == EnableStatus.ENABLED.value:
            journey_datas = await flight_fare_mongo.get_ow_datas(
                collection_name=collection_name,
                airline_code=airline_code,
                dep_city_code=dep_city_code,
                arr_city_code=arr_city_code,
                dep_date=dep_date,
                return_date=return_date,
                trip_type='ct',
            )
            if journey_datas:
                await append_journey_data(data_map=data_map, fare_rule_rows=fare_rule_rows, journey_datas=journey_datas)
        logger.debug(f'ow_datas: {ow_datas}')

    logger.debug(data_map)
    data_list = list(data_map.values())
    logger.debug(data_list)
    result = await format_simple(dep_city_code=dep_city_code, arr_city_code=arr_city_code, data_list=data_list)
    return result


async def format_simple(dep_city_code, arr_city_code, data_list):
    result = []
    for data in data_list:
        logger.debug(f'format_simple {data}')

        trip = data['trips'][0]
        flight_no = '/'.join(trip['flight_nos'])
        airline_code = flight_no[:2]

        fare_rule_snapshot = trip['fare_rule_snapshot']

        cabin_class = trip['segments'][0]['cabin']['cabin_class']

        segments = [
            {
                "segment_index": s['segment_index'],
                "airline_code": s['flight_no'][:2],
                "flight_no": s['flight_no'],
                "dep_airport_code": s['dep_airport_code'],
                "arr_airport_code": s['arr_airport_code'],
                "dep_date": s['dep_date'],
                "dep_time": ':'.join(s['dep_time'].split(':')[:2]),
                "arr_time": ':'.join(s['arr_time'].split(':')[:2]),
                "arr_date": s['arr_date'],
                "aircraft_code": s['aircraft_code'],
                "share_code": s['share_code'],
                "stop_times": s['stop_times'],
                "stops": s['stops'],
            }
            for s in trip['segments']
        ]
        product = copy.deepcopy(trip['fares'])
        # add_ons = await FareRuleAddOn.get_all_async(FareRuleAddOn.fare_rule_id == fare_rule_snapshot['id'])
        # baggages = await FareRuleBaggage.get_all_async(FareRuleBaggage.fare_rule_id == fare_rule_snapshot['id'])
        # logger.debug(f'####### product ####### {product}')
        min_quantity = settings.AIRLINE_MIN_QUANTITY.get(airline_code, 0)

        pub_quantity = product['adult']['quantity']
        product['adult']['is_protected'] = False

        # 只对标准运价进行保护
        if min_quantity > 0 and fare_rule_snapshot['fare_type'] == FareType.NORMAL.value:
            not_pay_later_hours = settings.CAN_NOT_PAY_LATER.get(airline_code)
            not_pay_later_time = None
            if not_pay_later_hours:
                not_pay_later_time = datetime.now() + timedelta(hours=not_pay_later_hours)
            full_dep_time = datetime.strptime(
                f'{trip["dep_date"]} {trip["segments"][0]["dep_time"]}', '%Y-%m-%d %H:%M:%S'
            )
            logger.debug(f'not_pay_later_time: {not_pay_later_time}, full_dep_time: {full_dep_time}')
            if not not_pay_later_time or full_dep_time <= not_pay_later_time:
                # 这里执行保护逻辑
                # 注意适配的减n逻辑需要删除，但是扣除锁单的逻辑继续保留
                pub_quantity = product['adult']['quantity'] - min_quantity
                # 这里加个标记，用于实时验价时判断是否需要进行余票数过滤
                product['adult']['is_protected'] = True
                logger.info(f'对航班 {flight_no} 进行保护，减少 {min_quantity} 张票，剩余 {pub_quantity} 张票')
                if pub_quantity < 1:
                    logger.warning(f'航班成人票不足, pub_quantity:{pub_quantity}, product:{product}')
                    continue

        for k, v in product.items():
            if pub_quantity == 1 and k != 'adult':
                v['quantity'] = 0
            else:
                v['quantity'] = pub_quantity

        fare_key = generate_fare_key(
            dep_city_code=dep_city_code,
            arr_city_code=arr_city_code,
            dep_date=trip['dep_date'],
            cabin=fare_rule_snapshot['adult_cabin_code'],
            cabin_class=cabin_class,
            src_currency=product['adult']['src_currency'],
            flight_no=flight_no,
            fare_id=fare_rule_snapshot['id'],
            unique_id=data['unique_id'],
            fare_type=fare_rule_snapshot['fare_type'],
        )

        product.update(
            {
                "fare_type": fare_rule_snapshot['fare_type'],
                "fare_key": fare_key,
                # todo 暂时不销售辅营
                "includes": trip['includes'],
                # "add_ons": [
                #     {
                #         "type": a['add_on_type'],
                #         "desc": a['add_on_desc'],
                #         'code': a['add_on_code'],
                #         'name': a['add_on_name'],
                #         'price': a['add_on_tag_price'],
                #     }
                #     for a in add_ons
                #     if a['add_on_tag_price'] > 0
                # ],
                # "baggages": [
                #     {
                #         'type': b['baggage_type'],
                #         'code': b['baggage_code'],
                #         'price': b['baggage_tag_price'],
                #         'weight': b['baggage_weight'],
                #     }
                #     for b in baggages
                #     if b['baggage_tag_price'] > 0
                # ],
                "cabin": fare_rule_snapshot['adult_cabin_code'],
                "cabin_class": cabin_class,
                "enable_auto_float": trip['enable_auto_float'],
                "max_float": trip['max_float'],
                "min_float": trip['min_float'],
                "actual_float": trip['actual_float'],
            }
        )
        adult_quantity = product['adult']['quantity']

        if 'child' in product:
            if adult_quantity == 1:
                product['child']['quantity'] = 0
                logger.warning(f'航班儿童票不足, childs:{product["child"]["quantity"]}, product:{product}')
            product['child_includes'] = trip['includes']

        if 'infant' in product:
            if adult_quantity == 1:
                product['infant']['quantity'] = 0
            product['infant_includes'] = trip['includes']

        # if product.get('baggage')
        expire_seconds = 300
        if 'expire_time' in data:
            tmp_seconds = (datetime.strptime(data['expire_time'], '%Y-%m-%d %H:%M:%S') - datetime.now()).seconds
            if tmp_seconds > 0:
                expire_seconds = tmp_seconds
        flight = {
            'flight_info': {
                "expire_seconds": expire_seconds,
                "trip_type": data['trip_type'],
                "dep_airport_code": trip['dep_airport_code'],
                "arr_airport_code": trip['arr_airport_code'],
                "airline_code": '/'.join(trip['airline_codes']),
                "flight_no": flight_no,
                "dep_date": trip['dep_date'],
                "arr_date": segments[-1]['arr_date'],
                "dep_time": segments[0]['dep_time'],
                "arr_time": segments[-1]['arr_time'],
                "segments": segments,
            },
            "products": [product],
        }
        result.append(flight)

    logger.debug(result)
    return result


# async def append_base_data(result):
#     airline_codes = [flight['flight_info']['airline_code'] for flight in result]

#     append_result = []

#     for flight in result:
#         dep_airport_code = flight['flight_info']['dep_airport_code']
#         arr_airport_code = flight['flight_info']['arr_airport_code']
#         dep_date = flight['flight_info']['dep_date']
#         # todo 暂时传固定值
#         # if flight['flight_info']['airline_code'] == 'ZE':
#         #     for product in flight['products']:
#         #         if product['cabin_class'] != 'E':
#         #             product['includes'] = {"baggage": {"checked_baggage": {"weight": 15, "count": 1, "all_weight": 15}}}
#         flight_info_map = await get_flight_info_map(
#             airline_codes=airline_codes,
#             dep_airport_code=dep_airport_code,
#             arr_airport_code=arr_airport_code,
#             dep_date=dep_date,
#         )
#         f_key = f'{flight["flight_info"]["airline_code"]}_{dep_airport_code}_{arr_airport_code}_{flight["flight_info"]["flight_no"]}'
#         base_data = flight_info_map.get(f_key, {})
#         logger.debug(base_data)
#         if 'trip_type' not in flight['flight_info']:
#             flight['flight_info']['trip_type'] = 'ow'
#         if base_data:
#             arr_date = datetime.strptime(flight["flight_info"]['dep_date'], '%Y-%m-%d') + timedelta(
#                 days=base_data.get('days', 0)
#             )
#             arr_date = arr_date.strftime('%Y-%m-%d')
#             flight['flight_info']['arr_date'] = arr_date

#             # 起降时刻改成用爬虫返回 2025-04-06

#             # flight['flight_info']['dep_time'] = base_data.get('dep_time', '')
#             # flight['flight_info']['arr_time'] = base_data.get('arr_time', '')
#             flight['flight_info']['segments'][0]['arr_date'] = arr_date
#             # flight['flight_info']['segments'][0]['arr_time'] = base_data.get('arr_time', '')
#             # flight['flight_info']['segments'][0]['dep_time'] = base_data.get('dep_time', '')
#             # flight['flight_info']['segments'][0]['arr_time'] = base_data.get('arr_time', '')

#             # todo 以下数据稍后也要改成用爬虫返回 2025-04-06
#             flight['flight_info']['segments'][0]['aircraft_code'] = base_data.get('aircraft_code', '')
#             flight['flight_info']['segments'][0]['share_code'] = base_data.get('share_code', False)
#             flight['flight_info']['segments'][0]['stops'] = base_data.get('stops', [])
#             flight['flight_info']['segments'][0]['stop_times'] = len(base_data.get('stop_times', []))
#             append_result.append(flight)

#     return append_result


def generate_fare_key(
    dep_city_code,
    arr_city_code,
    dep_date,
    flight_no,
    cabin,
    cabin_class,
    src_currency,
    fare_id,
    unique_id,
    fare_type=FareType.NORMAL.value,
):

    first = f'{dep_city_code}_{arr_city_code}_{dep_date}_{flight_no}_{cabin}_{cabin_class}_{src_currency}'
    second = f'{fare_type}_{fare_id}'
    third = f'{unique_id}'
    flight_id = (
        base64.b64encode(first.encode('utf-8')).decode('utf-8')
        + '#'
        + base64.b64encode(second.encode('utf-8')).decode('utf-8')
        + '#'
        + third
    )
    return flight_id


def decode_fare_key(fare_key: str):
    tmp = fare_key.split('#')
    if len(tmp) == 2:
        first, second = tmp
        third = ''
    elif len(tmp) == 3:
        first, second, third = tmp
    else:
        raise ValueError(f'invalid fare_key: {fare_key}')
    # first, second, third = fare_key.split('#')
    first = base64.b64decode(first.encode('utf-8')).decode('utf-8')
    second = base64.b64decode(second.encode('utf-8')).decode('utf-8')

    result = {
        'dep_city_code': first.split('_')[0],
        'arr_city_code': first.split('_')[1],
        'dep_date': first.split('_')[2],
        'flight_no': first.split('_')[3],
        'cabin': first.split('_')[4],
        'cabin_class': first.split('_')[5],
        'src_currency': first.split('_')[6],
        'fare_type': second.split('_')[0],
        'fare_id': int(second.split('_')[1]),
        'unique_id': third,
    }
    return result


async def search_from_pre_order(
    channel_code: str,
    dep_city_code: str,
    arr_city_code: str,
    dep_date: str,
    return_date: str = None,
    adult: int = 1,
    child: int = 0,
    infant: int = 0,
):
    po_sdk = FlightPreOrderSdk(host=settings.FLIGHT_PRE_ORDER_URL)
    try:
        resp = await po_sdk.search(
            {
                'channel_code': channel_code,
                'dep_city_code': dep_city_code,
                'arr_city_code': arr_city_code,
                'dep_date': dep_date,
                'return_date': return_date,
                'adult': adult,
                'child': child,
                'infant': infant,
            }
        )
        return resp
    except Exception as e:
        logger.error(e)
    return None


async def search_hood_price(
    channel_code: str,
    dep_city_code: str,
    arr_city_code: str,
    dep_date: str,
    return_date: str = None,
    adult: int = 1,
    child: int = 0,
    infant: int = 0,
    update_actual_float: bool = False,
):
    today = str(datetime.now().date())
    fare_map = await get_fare_rule_map(
        today=today,
        channel_code=channel_code,
        dep_city_code=dep_city_code,
        arr_city_code=arr_city_code,
        dep_date=dep_date,
        fare_type=FareType.PRE_ORDER.value,
    )
    logger.debug(f'hood fares {fare_map}')
    dep_airport_codes = await base_data_service.get_airport_codes(city_code=dep_city_code)
    arr_airport_codes = await base_data_service.get_airport_codes(city_code=arr_city_code)
    hood_rows = await hood_service.get_hood_datas(
        dep_airport_codes=dep_airport_codes, arr_airport_codes=arr_airport_codes, dep_date=dep_date
    )
    flight_datas = hood_service.marge_hood_datas(rows=hood_rows)
    logger.debug(f'hood rows {flight_datas}')
    data_list = await hood_service.compute_fare(
        hood_datas=flight_datas, fare_map=fare_map, update_actual_float=update_actual_float
    )
    result = await format_simple(dep_city_code=dep_city_code, arr_city_code=arr_city_code, data_list=data_list)

    return result


async def verify_hood_price(
    fare_key: str, dep_airport_code: str, arr_airport_code: str, flight_no: str, adult: int, child: int, infant: int
):
    fare_key_data = decode_fare_key(fare_key)
    logger.debug(fare_key_data)
    fare_id = fare_key_data['fare_id']
    fare_row = await FareRule.get_by_async(FareRule.id == fare_id, FareRule.status == EnableStatus.ENABLED.value)
    if not fare_row:
        logger.warning(f'运价 {fare_id} 不存在')
        ApiCodes.FARE_VERIFY_INVALID.raise_error()
    airline_code = fare_row['airline_code']
    dep_date = fare_key_data['dep_date']
    hood_rows = await hood_service.get_hood_datas(
        dep_airport_codes=[dep_airport_code],
        arr_airport_codes=[arr_airport_code],
        dep_date=dep_date,
        flight_no=flight_no,
    )
    logger.debug(f'hood rows {hood_rows}')
    flight_datas = hood_service.marge_hood_datas(rows=hood_rows)
    logger.debug(f'flight_datas  {flight_datas}')
    data_list = await hood_service.compute_fare(
        hood_datas=flight_datas, fare_map={airline_code: [fare_row]}, update_actual_float=False
    )

    dep_city_code = fare_key_data['dep_city_code']
    arr_city_code = fare_key_data['arr_city_code']
    result = await format_simple(dep_city_code=dep_city_code, arr_city_code=arr_city_code, data_list=data_list)

    return result


def merge_result(result, result2):
    if not result:
        return result2
    if not result2:
        return result
    for flight in result:
        for flight2 in result2:
            # logger.debug(flight)
            # logger.debug(flight2)
            if (
                flight['flight_info']['dep_airport_code'] == flight2['flight_info']['dep_airport_code']
                and flight['flight_info']['arr_airport_code'] == flight2['flight_info']['arr_airport_code']
                and flight['flight_info']['dep_date'] == flight2['flight_info']['dep_date']
                and flight['flight_info']['flight_no'] == flight2['flight_info']['flight_no']
            ):
                # logger.debug(flight['products'])
                # logger.debug(flight2['products'])
                flight['products'] += flight2['products']
                break
        else:
            result.append(flight2)
    return result


async def verify_by_cache(
    airline_code: str, dep_city_code: str, arr_city_code: str, dep_date: str, fare_row: dict, airline_row: dict
):
    flight_fare_mongo = FlightFareMongoService('flight_fare')
    collection_name = 'airline_fare_cache'

    data_map = {}
    ow_datas = await flight_fare_mongo.get_ow_datas(
        collection_name=collection_name,
        airline_code=airline_code,
        dep_city_code=dep_city_code,
        arr_city_code=arr_city_code,
        dep_date=dep_date,
        # return_date=return_date,
        trip_type='ow',
    )
    logger.debug(f'ow_datas: {ow_datas}')
    logger.info(f'cache data: {len(ow_datas)}')
    if ow_datas:
        await append_ow_datas(data_map=data_map, fare_rule_rows=[fare_row], ow_datas=ow_datas)
        logger.debug(f'ow_datas: {ow_datas}')
        # 弃程
        if airline_row['abandoned_journey'] == EnableStatus.ENABLED.value:
            journey_datas = await flight_fare_mongo.get_ow_datas(
                collection_name=collection_name,
                airline_code=airline_code,
                dep_city_code=dep_city_code,
                arr_city_code=arr_city_code,
                dep_date=dep_date,
                # return_date=return_date,
                trip_type='ct',
            )
            if journey_datas:
                await append_journey_data(data_map=data_map, fare_rule_rows=[fare_row], journey_datas=journey_datas)
        logger.debug(f'ow_datas: {ow_datas}')
    logger.debug(f'data_map: {data_map}')
    data_list = list(data_map.values())
    logger.info(f'cache data_list: {len(data_list)}')
    return data_list


async def verify_by_crawler(
    airline_code: str,
    dep_airport_code: str,
    arr_airport_code: str,
    dep_date: str,
    fare_row: dict,
    airline_row: dict,
    src_currency: str,
    adult: int = 1,
    child: int = 0,
    infant: int = 0,
):

    flight_fare_mongo = FlightFareMongoService('flight_fare')
    data_list = []
    try:
        # 实时取booking结果
        sdk_client = sdks.base.SdkClient(host=settings.CRAWLER_URL)
        crawler_search_req = sdks.crawler.search.SearchRequest(
            airline_code=airline_code,
            dep_airport_code=dep_airport_code,
            arr_airport_code=arr_airport_code,
            dep_date=dep_date,
            adult=adult,
            child=child,
            infant=infant,
            currency_code=src_currency,
        )
        resp = await sdk_client.send_async(crawler_search_req)
        if resp['code'] != ApiCodes.SUCCESS.value or not resp.get('data', {}).get('results'):
            # ApiCodes.FARE_VERIFY_NO_RESULT.raise_error()
            ApiCodes(resp['code']).raise_error()

        search_result = copy.deepcopy(resp)
        logger.debug(search_result)
        # logger.debug(ow_datas['data']['results'][0]['trips'][0])
        # 处理汇率
        search_result = await crawler_callback_services.parse_exchange(search_result=search_result)
        #
        # search_result = crawler_callback_services.cache_fuse(search_result=search_result)
        # 分割数据
        unique_id = hashlib.md5(f'{time.time()}'.encode('utf-8')).hexdigest()
        task_key = f'{airline_row["airline_code"]}-{dep_airport_code}-{arr_airport_code}-{dep_date}'
        search_result['task_info'] = {
            'unique_id': unique_id,
            'site_code': airline_row['site_code'],  # 注意这里是ota的site_code
            'site_type': airline_row['site_type'],
            'dep_airport_code': dep_airport_code,
            'arr_airport_code': arr_airport_code,
            'dep_date': dep_date,
            'return_date': '',
            'trip_type': 'ow',
            'status': TaskStatus.SUCCESS.value,
            'airline_code': airline_code,  # 这里的airline_code表示要从OTA获取的航司
            'schedule_id': 0,
            'fetch_rule_id': 0,
            'expire_seconds': 600,  # 验价这里固定写成10分钟，有更短的运价时会自动替换
            'expire_time': (datetime.now() + timedelta(seconds=600)).strftime("%Y-%m-%d %H:%M:%S"),
            'task_key': task_key,
            'currency_code': 'CNY',
            'create_time': datetime.now().strftime("%Y-%m-%dT%H:%M:%S"),
        }
        search_result['error'] = {'code': resp['code'], 'message': resp['message']}
        datas = await crawler_callback_services.split_by_segment(data=search_result)
        if datas:
            # 更新缓存
            await flight_fare_mongo.bulk_update_cache(datas=datas, collection_name='airline_fare_cache')
            await crawler_callback_services.apply_shopping_push(search_result=search_result)
            logger.debug(search_result)
            # 使用新的查询结果和投放策略进行重新计算
            data_map = {}
            await append_fare_rule(data_map=data_map, fare_rule_rows=[fare_row], mg_datas=search_result)

            data_list = list(data_map.values())

    except:
        raise

    return data_list


# async def verify_by_crawler_async(
#     airline_code: str,
#     dep_airport_code: str,
#     arr_airport_code: str,
#     dep_date: str,
#     fare_row: dict,
#     airline_row: dict,
#     src_currency: str,
#     adult: int = 1,
#     child: int = 0,
#     infant: int = 0,
# ):
#     cache_key = f'verify-{airline_code}-{dep_airport_code}-{arr_airport_code}-{dep_date}'


#     return None


async def price_verify_normal(
    fare_key: str, dep_airport_code: str, arr_airport_code: str, use_cache: bool, adult: int, child: int, infant: int
):
    """
    2024-11-20 新版平台用验价
    """
    fare_key_data = decode_fare_key(fare_key)
    logger.debug(fare_key_data)
    fare_id = fare_key_data['fare_id']
    fare_row = await FareRule.get_by_async(FareRule.id == fare_id, FareRule.status == EnableStatus.ENABLED.value)
    if not fare_row:
        ext_msg = f'运价 {fare_id} 不存在，或已失效'
        logger.warning(ext_msg)
        ApiCodes.FARE_VERIFY_INVALID.raise_error(ext_msg=f'ID: {fare_id}')
    logger.info(f'fare id: {fare_row["id"]}')

    airline_code = fare_row['airline_code']
    dep_city_code = fare_key_data['dep_city_code']
    arr_city_code = fare_key_data['arr_city_code']
    dep_date = fare_key_data['dep_date']
    flight_no = fare_key_data['flight_no']
    src_currency = fare_key_data['src_currency']
    dep_airport_code = dep_airport_code
    arr_airport_code = arr_airport_code
    airline_row = await Airline.get_by_async(Airline.airline_code == airline_code)
    if use_cache:
        logger.info('use cache')
        data_list = await verify_by_cache(
            airline_code=airline_code,
            dep_city_code=dep_city_code,
            arr_city_code=arr_city_code,
            dep_date=dep_date,
            fare_row=fare_row,
            airline_row=airline_row,
        )
    else:
        logger.info('use crawler')
        data_list = await verify_by_crawler(
            airline_code=airline_code,
            dep_airport_code=dep_airport_code,
            arr_airport_code=arr_airport_code,
            dep_date=dep_date,
            adult=adult,
            child=child,
            infant=infant,
            fare_row=fare_row,
            airline_row=airline_row,
            src_currency=src_currency,
        )

    # logger.info(f'data_list before format: {len(data_list)}')
    if not data_list:
        ext_msg = '无缓存数据 before format:'
        logger.warning(f'{ext_msg} fare_key: {fare_key} data_list: {data_list}')
        ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(ext_msg=ext_msg)

    result = await format_simple(dep_city_code=dep_city_code, arr_city_code=arr_city_code, data_list=data_list)

    # logger.info(f'data_list after format: {len(result)}')
    if not result:
        ext_msg = '运价计算失败 after format:'
        logger.warning(f'{ext_msg} fare_key: {fare_key} data_list: {data_list}')
        ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(ext_msg=ext_msg)

    flight = [f for f in result if f['flight_info']['flight_no'] == flight_no]

    # logger.info(f'flight_no {flight_no} result: {result} fare_key_data: {fare_key_data} flight: {flight}')
    if not flight:
        flight_no_list = [f['flight_info']['flight_no'] for f in result]
        ext_msg = f'缓存中无目标航班 {flight_no} flight_no_list: {flight_no_list}'
        logger.warning(f'{ext_msg} result: {result} fare_key_data: {fare_key_data} flight: {flight}')
        ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(ext_msg=ext_msg)

    return flight


async def price_verify_fpo(
    fare_key: str, dep_airport_code: str, arr_airport_code: str, flight_no: str, request: Request
):
    # flight_fare_mongo = FlightFareMongoService('flight_fare')

    # price_verify_log = {"request": item.model_dump(), "sub_response": None, "response": None}
    po_sdk = FlightPreOrderSdk(host=settings.FLIGHT_PRE_ORDER_URL)
    # result = {'success': False, 'reason': '价格查询失败'}
    resp = {}
    try:
        data = {
            "fare_key": fare_key,
            "dep_airport_code": dep_airport_code,
            "arr_airport_code": arr_airport_code,
            "flight_no": flight_no,
        }
        resp = await po_sdk.price_verify(data=data)

        # result = resp['data']
        # price_verify_log['sub_response'] = resp
    except Exception as e:
        logger.exception(e)
    finally:
        # price_verify_log['response'] = resp
        # try:
        #     await flight_fare_mongo.insert_one(collection_name='price_verify_logs', data=price_verify_log)
        #     logger.debug(price_verify_log)
        # except Exception as e:
        #     logger.error(e)
        pass
    if resp.get('code') != ApiCodes.SUCCESS.value:
        raise Exception(resp['code'], resp['message'])
    return resp


def fare_fuse(result):
    new_result = []
    for r in result:
        tmp_r = copy.deepcopy(r)
        del tmp_r['products']
        tmp_r['products'] = []
        log_flight = f'{r["flight_info"]["dep_airport_code"]}-{r["flight_info"]["arr_airport_code"]}-{r["flight_info"]["dep_date"]}-{r["flight_info"]["flight_no"]}'
        for product in r['products']:

            discount_rate = product['adult']['total'] / product['adult']['src_cny_total']
            logger.debug(f'discount_rate: {discount_rate}')
            if discount_rate < settings.MIN_DISCOUNT_RATE:
                logger.warning(f'{log_flight} 折扣率 {discount_rate} 低于 {settings.MIN_DISCOUNT_RATE}，跳过')
                continue

            tmp_r['products'].append(product)
        if len(tmp_r['products']) > 0:
            new_result.append(tmp_r)
        else:
            logger.warning(f'{log_flight} 无票，跳过')
    return new_result


def low_quantity_fuse(result, min_quantity):
    logger.debug(f'{result}过滤低余票，最小余票数：{min_quantity}')
    new_result = []
    for r in result:

        if r['products'][0]['adult']['is_protected']:
            # 已经通过能否占座进行过扣减的，不再进行余票数过滤
            new_result.append(r)
        elif r['products'][0]['adult']['quantity'] > min_quantity:
            # 余票数足够，不进行余票数过滤
            new_result.append(r)
        else:
            logger.warning(f'余票数 {r["products"][0]["adult"]["quantity"]} 低于 {min_quantity}，跳过')
    return new_result


async def verify_create_order(params):
    fare_key_info = decode_fare_key(params['fare_key'])
    dep_date = fare_key_info['dep_date']

    tmp_order_row = await VerifyTmpOrder.create_at_async(
        order_no=params['order_no'],
        mock_pnr=params['mock_pnr'],
        dep_airport_code=params['dep_airport_code'],
        arr_airport_code=params['arr_airport_code'],
        dep_date=dep_date,
        flight_no=params['flight_no'],
        task_status=TaskStatus.PENDING.value,
        order_info=orjson.dumps(params).decode('utf-8'),
        product_type=params.get('fare_type', ''),
    )
    airline_code = params['flight_no'][:2]

    if airline_code not in settings.ALLOW_CREATE_ORDER_AIRLINES:
        error_msg = f'{airline_code} 暂不支持自动预定'
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.id == tmp_order_row['id'],
            task_status=TaskStatus.CANCELLED.value,
            code=ApiCodes.FARE_VERIFY_STOP_BOOK.value,
            message=error_msg,
        )
        raise ApiCodes.FARE_VERIFY_STOP_BOOK.raise_error()

    not_pay_later_hours = settings.CAN_NOT_PAY_LATER.get(airline_code)
    full_dep_time = datetime.strptime(params['dep_time'], '%Y%m%d%H%M')
    if not_pay_later_hours is not None:
        logger.debug(f'{airline_code} 停止验价占座天数: {not_pay_later_hours}')
        not_pay_later_time = datetime.now() + timedelta(hours=not_pay_later_hours)
        if full_dep_time <= not_pay_later_time:
            error_msg = f'{airline_code} 起飞时间小于等于 {not_pay_later_hours} 小时（{full_dep_time.strftime("%Y-%m-%d %H:%M:%S")}） 停止验价占座'
            await VerifyTmpOrder.update_by_async(
                VerifyTmpOrder.id == tmp_order_row['id'],
                task_status=TaskStatus.CANCELLED.value,
                code=ApiCodes.FARE_VERIFY_STOP_BOOK.value,
                message=error_msg,
            )
            try:
                logger.bind(write_tag="api_es_log").info(
                    '',
                    api_type="celery_send",
                    api_path=task_name,
                    request=orjson.dumps(params, default=str).decode("utf-8"),
                    response=error_msg,
                    status="failed",
                    code=ApiCodes.FARE_VERIFY_STOP_BOOK.value,
                    message=f"成功：celery id: {celery_task_id}",
                    cost_time=0,
                )
            except Exception as e:
                logger.exception(e)
            ApiCodes.FARE_VERIFY_STOP_BOOK.raise_error()
    # 金额放大倍数，默认 1
    amount_multiplier = 1
    # 预占座订单，金额放大10倍
    if fare_key_info['fare_type'] == FareType.PRE_ORDER.value:
        amount_multiplier = 10

    passengers = params['passengers']
    passengers = verify_tmp_order_service.parse_passenger_auxes(
        dep_airport_code=params['dep_airport_code'],
        arr_airport_code=params['arr_airport_code'],
        flight_no=params['flight_no'],
        dep_date=dep_date,
        passengers=passengers,
        passenger_auxes=params.get('passenger_auxes', []),
    )
    contact = verify_tmp_order_service.parse_contact(airline_code=airline_code, passengers=passengers)

    req = sdks.crawler.create_order.CreateOrderRequest(
        callback_url=settings.VERIFY_BOOK_CALLBACK_URL,
        order_no=params['order_no'],
        mock_pnr=params['mock_pnr'],
        airline_code=airline_code,
        dep_airport_code=params['dep_airport_code'],
        arr_airport_code=params['arr_airport_code'],
        dep_date=dep_date,
        flight_no=params['flight_no'],
        adult=params['adult'],
        child=params['child'],
        infant=params['infant'],
        currency_code=fare_key_info['src_currency'],
        src_adult_base=params['src_adult_base'] * amount_multiplier,
        src_adult_tax=params['src_adult_tax'] * amount_multiplier,
        passengers=passengers,
        contact=contact,
    )

    airline_code = params['flight_no'][:2]
    task_name = f'{airline_code}_verify_book_task'.lower()
    celery_task_params = req.model_dump()
    celery_task_params['unique_id'] = req._headers['X-Request-ID']
    if task_name in tasks.crawler_celery_tasks:
        celery_task_id = tasks.crawler_celery_tasks[task_name].apply_async(args=(celery_task_params,))
    elif task_name in settings.SPECIAL_CRAWLER_TASK_ROUTES:
        tasks.simple_task_push(task_name, celery_task_params)
        celery_task_id = celery_task_params['unique_id']
    else:
        raise Exception(f'任务[{task_name}]未找到')
    if celery_task_id:
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.id == tmp_order_row['id'],
            celery_task_id=celery_task_id,
            task_status=TaskStatus.PENDING.value,
        )
    else:
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.id == tmp_order_row['id'],
            celery_task_id='',
            task_status=TaskStatus.FAILED.value,
            message='celery 任务发送失败',
        )
    try:
        logger.bind(write_tag="api_es_log").info(
            '',
            api_type="celery_send",
            api_path=task_name,
            request=orjson.dumps(celery_task_params, default=str).decode("utf-8"),
            response='',
            status="success",
            code=0,
            message=f"成功：celery id: {celery_task_id}",
            cost_time=0,
        )
    except Exception as e:
        logger.exception(e)
    # logger.debug(req.model_dump())
    # sdk_client = sdks.base.SdkClient(host=settings.CRAWLER_URL)
    # sdk_client.timeout = 180
    # resp = await sdk_client.send_async(req)
    # if resp.get('code', -1) != ApiCodes.SUCCESS.value:
    #     raise Exception(resp.get('code', -1), resp.get('message', ''))
    # return resp['data']


#


async def get_verify_cache(cache_key: str, flight_no: str):
    async with AsyncRedisPool(**ASYNC_REDIS_CFG) as redis:
        cache = await redis.get(cache_key)


async def verify_real_time(params, mock_pnr: str = None):
    from commons.extensions.logger_extras import log_uid

    fare_key_data = decode_fare_key(params['fare_key'])
    logger.debug(fare_key_data)
    fare_id = fare_key_data['fare_id']
    fare_row = await FareRule.get_by_async(FareRule.id == fare_id, FareRule.status == EnableStatus.ENABLED.value)
    if not fare_row:
        logger.warning(f'运价 {fare_id} 不存在')
        ApiCodes.FARE_VERIFY_INVALID.raise_error(ext_msg=f'ID: {fare_id}')

    airline_code = fare_row['airline_code']
    dep_city_code = fare_key_data['dep_city_code']
    arr_city_code = fare_key_data['arr_city_code']
    dep_date = fare_key_data['dep_date']
    flight_no = fare_key_data['flight_no']
    src_currency = fare_key_data['src_currency']
    dep_airport_code = params['dep_airport_code']
    arr_airport_code = params['arr_airport_code']
    passenger_count = params['adult'] + params['child'] + params['infant']

    verify_task_row = await VerifyTask.get_by_async(
        VerifyTask.dep_airport_code == dep_airport_code,
        VerifyTask.arr_airport_code == arr_airport_code,
        VerifyTask.dep_date == dep_date,
        VerifyTask.passenger_count == passenger_count,
        VerifyTask.task_keep_end_time > (datetime.now() - timedelta(seconds=5)).strftime('%Y-%m-%d %H:%M:%S'),
        order_by=[VerifyTask.id.desc()],
    )
    cache_data = None
    if verify_task_row:
        if verify_task_row['task_status'] in [TaskStatus.PENDING.value, TaskStatus.RUNNING.value]:
            raise ApiCodes.TASK_RUNNING.raise_error()
        elif verify_task_row['task_status'] == TaskStatus.FAILED.value:
            raise Exception(verify_task_row['code'], verify_task_row['message'])
        elif verify_task_row['task_status'] == TaskStatus.SUCCESS.value:
            # 超过1分钟的缓存数据不再使用
            if verify_task_row.get('callback_time') and datetime.now() - timedelta(minutes=1) > datetime.strptime(
                verify_task_row['callback_time'], '%Y-%m-%d %H:%M:%S'
            ):
                raise ApiCodes.TASK_RUNNING.raise_error('缓存数据已过期')
            cache_data = orjson.loads(verify_task_row['verify_result'])

    else:
        verify_task_row = await VerifyTask.create_at_async(
            fare_id=fare_id,
            unique_id=str(log_uid),
            fare_key=params['fare_key'],
            airline_code=airline_code,
            dep_airport_code=dep_airport_code,
            arr_airport_code=arr_airport_code,
            dep_date=dep_date,
            flight_no=flight_no,
            currency_code=src_currency,
            passenger_count=passenger_count,
            # task_keep_end_time=(datetime.now() + timedelta(seconds=10 * 60)).strftime('%Y-%m-%d %H:%M:%S'),
            task_keep_end_time=(datetime.now() + timedelta(seconds=params['keep_time'])).strftime('%Y-%m-%d %H:%M:%S'),
            verify_params=orjson.dumps(params, default=str).decode('utf-8'),
            verify_result='',
            task_status=TaskStatus.RUNNING.value,
        )
        celery_id = task_services.send_verify_tasks(verify_task_row)
        if not celery_id:
            ApiCodes.UNKNOWN.raise_error('实时查询任务发送失败')
        # if not celery_id:
        #     await VerifyTask.update_by_async(
        #         VerifyTask.id == verify_task_row['id'],
        #         VerifyTask.task_status == TaskStatus.PENDING.value,
        #         message='celery 任务发送失败',
        #     )
        raise ApiCodes.TASK_RUNNING.raise_error('新建查询任务')

    if not cache_data:
        ApiCodes.TASK_RUNNING.raise_error('无缓存数据')
    else:
        # flight_fare_mongo = FlightFareMongoService('flight_fare')
        # datas = await crawler_callback_services.split_by_segment(data=cache_data)
        # 更新缓存
        # await flight_fare_mongo.bulk_update_cache(datas=datas, collection_name='airline_fare_cache')
        # await crawler_callback_services.apply_shopping_push(search_result=cache_data)
        logger.debug(cache_data)
        # 使用新的查询结果和投放策略进行重新计算
        data_map = {}
        await append_fare_rule(data_map=data_map, fare_rule_rows=[fare_row], mg_datas=cache_data)

        data_list = list(data_map.values())
        result = await format_simple(dep_city_code=dep_city_code, arr_city_code=arr_city_code, data_list=data_list)
        if mock_pnr:
            result = await verify_tmp_order_service.deduct_ticket_by_temp_order(result, mock_pnr=mock_pnr)
        flight = [f for f in result if f['flight_info']['flight_no'] == flight_no]
        if not flight:
            now_flight_nos = [f['flight_info']['flight_no'] for f in result]
            raise ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(
                ext_msg=f'航班 {flight_no} 已售完, 当前航班号: {now_flight_nos}'
            )
        logger.debug(flight)
        return flight


async def get_exchange_rate(
    src_currency: str, dst_currency: str, src_price: Union[int, float], airline_code: str = None
):

    fix_rate_row = None
    rate = None
    if airline_code:
        fix_rate_row = await AirlineRate.get_by_async(
            AirlineRate.airline_code == airline_code, AirlineRate.currency_code == src_currency
        )
        if fix_rate_row:
            rate = round(fix_rate_row['fixed_rate'], 6)

    if rate is None:
        # 使用实时汇率
        pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)
        resp = await pay_center_sdk.send_async(
            request=GetExchangeRateRequest(from_currency=src_currency, to_currency=dst_currency)
        )
        rate = round(resp['data']['rate'], 6)
    dst_price = src_price * rate
    result = {
        'airline_code': airline_code,
        'src_currency': src_currency,
        'dst_currency': dst_currency,
        'src_price': src_price,
        'dst_price': dst_price,
        'rate': rate,
    }
    return result
