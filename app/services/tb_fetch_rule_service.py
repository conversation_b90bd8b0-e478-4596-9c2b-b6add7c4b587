from app.models.tb_fetch_rule import TbFetchRule
from commons.consts.common_status import EnableStatus
from datetime import datetime, timedelta
from loguru import logger
from app.services import schedule_services
from app.depends import redis_pool
import uuid
import socket


async def get_auto_float_conditions():
    """获取自动调价条件（优化last_fetch_time更新策略）"""
    tb_fetch_rule_rows = await TbFetchRule.get_all_async(TbFetchRule.status == EnableStatus.ENABLED.value)
    fetch_conditions = []
    rule_condition_mapping = {}  # 记录规则ID与条件的映射关系
    current_time = datetime.now()

    for tb_fetch_rule_row in tb_fetch_rule_rows:
        # 检查时间间隔
        if tb_fetch_rule_row['last_fetch_time']:
            last_fetch_time = datetime.strptime(tb_fetch_rule_row['last_fetch_time'], '%Y-%m-%d %H:%M:%S')
            diff_seconds = (datetime.now() - last_fetch_time).total_seconds()
            if diff_seconds < tb_fetch_rule_row['rule_interval']:
                logger.info(
                    f'{tb_fetch_rule_row["id"]} 距离上次抓取时间 {tb_fetch_rule_row["last_fetch_time"]} 不足 {tb_fetch_rule_row["rule_interval"]} 秒，跳过'
                )
                continue

        # 检查抓取时间段
        start_time = datetime(
            year=current_time.year,
            month=current_time.month,
            day=current_time.day,
            hour=int(tb_fetch_rule_row['rule_start_time'].split(':')[0]),
            minute=int(tb_fetch_rule_row['rule_start_time'].split(':')[1]),
        )
        end_time = datetime(
            year=current_time.year,
            month=current_time.month,
            day=current_time.day,
            hour=int(tb_fetch_rule_row['rule_end_time'].split(':')[0]),
            minute=int(tb_fetch_rule_row['rule_end_time'].split(':')[1]),
        )
        if current_time < start_time or current_time > end_time:
            logger.info(f'{tb_fetch_rule_row["id"]} 不在抓取时间段内 {start_time} - {end_time}，跳过')
            continue

        # 生成抓取条件
        tmp_conditions = await schedule_services.split_lines(
            airline_code=tb_fetch_rule_row['airline_code'],
            dep_airport_code=tb_fetch_rule_row['dep_airport_code'],
            arr_airport_code=tb_fetch_rule_row['arr_airport_code'],
            start_days=tb_fetch_rule_row['start_days'],
            end_days=tb_fetch_rule_row['end_days'],
            use_city=True,
        )

        # 检查是否有有效条件
        valid_conditions = []
        for tmp_condition in tmp_conditions:
            if tmp_condition in fetch_conditions:
                logger.info(f'{tb_fetch_rule_row["id"]} 条件重复，跳过')
                continue
            valid_conditions.append(tmp_condition)

        # 如果有有效条件，记录映射关系但暂不更新last_fetch_time
        if valid_conditions:
            logger.info(f'规则 {tb_fetch_rule_row["id"]} 准备处理，包含 {len(valid_conditions)} 个条件')

            # 添加条件到总列表
            fetch_conditions.extend(valid_conditions)

            # 记录规则与条件的映射关系，但不更新last_fetch_time
            rule_condition_mapping[tb_fetch_rule_row['id']] = {
                'conditions': valid_conditions,
                'rule_info': {
                    'airline_code': tb_fetch_rule_row['airline_code'],
                    'dep_airport_code': tb_fetch_rule_row['dep_airport_code'],
                    'arr_airport_code': tb_fetch_rule_row['arr_airport_code'],
                    'rule_interval': tb_fetch_rule_row['rule_interval'],
                },
                'last_fetch_time_updated': False,  # 标记是否已更新last_fetch_time
            }

    logger.info(f'共获取 {len(fetch_conditions)} 个条件，涉及 {len(rule_condition_mapping)} 个规则')
    return fetch_conditions, rule_condition_mapping


async def update_rule_last_fetch_time_on_first_execution(condition: str, rule_condition_mapping: dict):
    """在规则的第一个条件实际开始执行时更新last_fetch_time

    Args:
        condition: 当前执行的条件
        rule_condition_mapping: 规则条件映射关系
    """
    # 找到该条件属于哪个规则
    for rule_id, mapping in rule_condition_mapping.items():
        if condition in mapping['conditions'] and not mapping['last_fetch_time_updated']:
            # 这是该规则第一个实际执行的条件，更新last_fetch_time
            current_time = datetime.now()
            current_time_str = current_time.strftime('%Y-%m-%d %H:%M:%S')

            await TbFetchRule.update_by_async(TbFetchRule.id == rule_id, last_fetch_time=current_time_str)

            # 标记已更新，避免重复更新
            mapping['last_fetch_time_updated'] = True
            mapping['actual_start_time'] = current_time

            logger.info(f'规则 {rule_id} 第一个条件 {condition} 开始执行，更新last_fetch_time为 {current_time_str}')
            break
