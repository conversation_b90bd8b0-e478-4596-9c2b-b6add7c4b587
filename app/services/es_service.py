from loguru import logger


def write_price_log(search_result: dict):
    try:
        # logger.debug(search_result)
        if search_result['data']['results']:
            for data in search_result['data']['results']:
                trip_info = data['trips'][0]
                # logger.debug(trip_info)
                logger.bind(write_tag="elasticsearch").info(
                    '',
                    server_name=f'{data["trip_type"]}_search_results',
                    airline_code=','.join(trip_info['airline_codes']),
                    dep_airport_code=trip_info['dep_airport_code'],
                    arr_airport_code=trip_info['arr_airport_code'],
                    dep_date=trip_info['dep_date'],
                    flight_no=','.join(trip_info['flight_nos']),
                    cabin_code=trip_info['segments'][0]['cabin']['code'],
                    cabin_level=trip_info['segments'][0]['cabin']['cabin_class'],
                    cabin_name=trip_info['segments'][0]['cabin']['name'],
                    adult_base=trip_info['fares']['adult']['base'],
                    adult_tax=trip_info['fares']['adult']['tax'],
                    total_price=trip_info['fares']['adult']['total'],
                    quantity=trip_info['fares']['adult']['quantity'],
                    currency_code='CNY',
                    exchange_rate=round(search_result['data']['exchange']['rate']['rate'], 6),
                    src_adult_base=trip_info['fares']['adult']['src_base'],
                    src_adult_tax=trip_info['fares']['adult']['src_tax'],
                    src_total_price=trip_info['fares']['adult']['src_total'],
                    src_currency_code=trip_info['fares']['adult']['src_currency'],
                    cabin_baggage_weight=trip_info['includes']['baggage']
                    .get('cabin_baggage', [{}])[0]
                    .get('weight', 0),
                    checked_baggage_weight=trip_info['includes']['baggage']
                    .get('checked_baggage', [{}])[0]
                    .get('weight', 0),
                    start_time=search_result['task_info']['start_time'],
                    end_time=search_result['task_info']['end_time'],
                )
    except Exception as e:
        logger.error(e)
