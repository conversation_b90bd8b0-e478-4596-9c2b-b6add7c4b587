'''
@Author: your name
@Date: 2020-02-06 10:26:15

航线表相关业务逻辑
'''

from datetime import datetime
from io import BytesIO
import re
from loguru import logger
from openpyxl import load_workbook
import orjson
import pandas as pd

from app import utils
from app.models.schedule import Schedule
from app.services import base_data_service
from app.services.flight_base_services import is_empty_line
from commons.consts.common_status import EnableStatus


async def dataframe_to_records(data: pd.DataFrame):
    records = []
    today = datetime.today().date()
    for row in data.values.tolist():
        try:
            if is_empty_line(row=row, line_len=5):
                logger.debug('跳过空行')
                continue
            if not row[0]:
                continue

            start_date = row[1]
            if isinstance(start_date, (pd.Timestamp, datetime)):
                start_date = str(start_date.date()).strip()
            else:
                start_date = str(start_date).strip()
            end_date = row[2]
            if isinstance(end_date, (pd.Timestamp, datetime)):
                end_date = str(end_date.date()).strip()
            else:
                end_date = str(end_date).strip()

            end_day = datetime.strptime(end_date, '%Y-%m-%d').date()
            if end_day < today:
                logger.debug(f'跳过过期数据 {row}')
                continue

            # 处理数据
            tmp = {
                'airline_code': row[0].upper().strip(),
                'start_date': start_date,
                'end_date': end_date,
                'dep_airport_code': row[3].upper().strip(),
                'arr_airport_code': row[4].upper().strip(),
                'schedules': str(int(row[5])).strip(),
            }
            # 判断班期是空或者是1~7
            pattern = re.compile(r"^[1-7]+$")
            if not bool(pattern.match(tmp['schedules'])):
                logger.error(f"班期格式不正确,班期为{tmp['schedules']}")
                raise Exception("班期必须是1~7数字")
            records.append(tmp)
        except Exception as e:
            logger.exception(e)

    return records


async def split_lines(
    airline_code: str,
    dep_airport_code: str,
    arr_airport_code: str,
    start_days: int = None,
    end_days: int = None,
    flight_start_date: str = None,
    flight_end_date: str = None,
    use_city: bool = False,
):
    schedule_rows = await Schedule.get_all_async(
        Schedule.airline_code == airline_code,
        Schedule.end_date >= str(datetime.now().date()),
        Schedule.status == EnableStatus.ENABLED.value,
    )
    logger.debug(schedule_rows)
    line_map = {}
    for schedule_row in schedule_rows:
        if dep_airport_code != '*' and schedule_row['dep_airport_code'] != dep_airport_code:
            logger.debug(
                f'出发机场不匹配，dep: {dep_airport_code} schedule: {schedule_row["dep_airport_code"]} schedule id: {schedule_row["id"]}'
            )
            continue
        if arr_airport_code != '*' and schedule_row['arr_airport_code'] != arr_airport_code:
            logger.debug(
                f'到达机场不匹配，arr：{arr_airport_code} schedule: {schedule_row["arr_airport_code"]} schedule id:{schedule_row["id"]}'
            )
            continue

        # 默认使用机场码拼接key
        key_prefix = f'{airline_code}-{schedule_row["dep_airport_code"]}-{schedule_row["arr_airport_code"]}'

        # 按需求使用城市码拼接key
        if use_city:
            dep_city_code = await base_data_service.get_city_code(airport_code=schedule_row['dep_airport_code'])
            arr_city_code = await base_data_service.get_city_code(airport_code=schedule_row['arr_airport_code'])
            key_prefix = f'{airline_code}-{dep_city_code}-{arr_city_code}'
        if start_days is not None and end_days is not None:
            dep_dates = utils.calculate_dates(
                start_days=start_days, end_days=end_days, schedule=schedule_row['schedules']
            )
        elif flight_start_date is not None and flight_end_date is not None:
            dep_dates = utils.calculate_dates_by_data_range(
                flight_start_date=flight_start_date,
                flight_end_date=flight_start_date,
                schedule=schedule_row['schedules'],
            )
        else:
            logger.warning(
                f'没有指定航班日期范围，start_days: {start_days} end_days: {end_days} 或 flight_start_date: {flight_start_date} flight_end_date: {flight_end_date}'
            )
            continue

        for dep_date in dep_dates:
            line_key = f'{key_prefix}-{dep_date}'
            if line_key in line_map:
                logger.debug(f'{line_key} key 重复')
                continue

            if (
                datetime.strptime(dep_date, '%Y-%m-%d').date()
                < datetime.strptime(schedule_row['start_date'], '%Y-%m-%d').date()
                or datetime.strptime(dep_date, '%Y-%m-%d').date()
                > datetime.strptime(schedule_row['end_date'], '%Y-%m-%d').date()
            ):
                logger.warning(
                    f'{line_key} 日期不在时刻表范围 {schedule_row["start_date"]}-{schedule_row["end_date"]} schedule id: {schedule_row["id"]}'
                )
                continue
            line_map[line_key] = line_key
    logger.debug(line_map)
    return line_map

async def get_all_schedule(airline_code: str = None):
    query = [
        Schedule.end_date >= str(datetime.now().date()),
        Schedule.status == EnableStatus.ENABLED.value,
    ]
    if airline_code:
        query.append(Schedule.airline_code == airline_code)
    schedule_rows = await Schedule.get_all_async(*query)
    return schedule_rows
