"""
缓存服务模块，提供缓存相关的功能
"""

from datetime import datetime, timedelta
from typing import List, Tu<PERSON>, Optional

from loguru import logger

from app.services.mongo_services import FlightFareMongoService
from app.services import fetch_rule_services, task_services, crawler_callback_services
from app.services import es_service
from commons.consts.api_codes import ApiCodes


async def update_cache_from_verify_result(
    search_result: dict, collection_name: str = "airline_fare_cache"
) -> <PERSON><PERSON>[List[str], int]:
    """
    从验价结果更新缓存

    Args:
        search_result: 验价结果
        collection_name: 集合名称

    Returns:
        Tuple[List[str], int]: 有效航班号列表和过期时间
    """
    try:

        task_info = search_result['task_info']

        # 确保site_code存在，格式与search对齐
        search_result['task_info']['site_code'] = f'airline_{task_info["airline_code"].lower()}'
        task_key = f'{task_info["airline_code"]}-{task_info["dep_airport_code"]}-{task_info["arr_airport_code"]}-{task_info["dep_date"]}'
        if 'task_key' not in task_info:
            search_result['task_info']['task_key'] = task_key

        # task_key = f'{task_info["airline_code"]}-{task_info["dep_airport_code"]}-{task_info["arr_airport_code"]}-{task_info["dep_date"]}'

        real_expire_seconds = task_info.get('expire_seconds', 600)
        valid_flight_nos = []

        # 初始化mongo
        flight_fare_mongo = FlightFareMongoService('flight_fare')

        # 获取规则
        try:
            fetch_rule_row = await fetch_rule_services.get_rule(
                airline_code=task_info['airline_code'],
                dep_airport_code=task_info['dep_airport_code'],
                arr_airport_code=task_info['arr_airport_code'],
                dep_date=task_info['dep_date'],
            )
        except Exception as e:
            logger.exception(e)
            fetch_rule_row = {}

        # 处理数据
        if search_result.get('data', {}).get('results', []):
            logger.debug(search_result)
            try:
                # 入库前已处理过汇率
                # search_result = await crawler_callback_services.parse_exchange(search_result=search_result)
                search_result = crawler_callback_services.cache_fuse(search_result=search_result)
                es_service.write_price_log(search_result=search_result)

                # 分割数据
                datas = await crawler_callback_services.split_by_segment(data=search_result)
                if datas:
                    for data in datas:
                        valid_flight_nos.append(data['result']['flight_no'])
                        data = await crawler_callback_services.check_low_quantity(splited_data=data)

                        data, tmp_expire_secords = crawler_callback_services.coumpute_expire_time(
                            split_data=data, fetch_rule_row=fetch_rule_row
                        )
                        if tmp_expire_secords < real_expire_seconds:
                            real_expire_seconds = tmp_expire_secords
                            if real_expire_seconds < 1:
                                real_expire_seconds = 1
                    logger.debug(datas)

                    # 更新缓存
                    await flight_fare_mongo.bulk_update_cache(datas=datas, collection_name=collection_name)
                    logger.info(f'更新缓存成功，最短过期时间为{real_expire_seconds} 秒')
            except Exception as e:
                logger.exception(e)
                datas = []

        # 删除缓存
        await flight_fare_mongo.delete_cache(
            collection_name=collection_name,
            site_code=f'airline_{task_info["airline_code"].lower()}',
            dep_airport_code=task_info['dep_airport_code'],
            arr_airport_code=task_info['arr_airport_code'],
            dep_date=task_info['dep_date'],
            return_date=task_info.get('return_date', ''),
            flight_nos=valid_flight_nos,
        )

        # 设置过期时间
        await task_services.set_cache_expire(task_key=task_key, value=real_expire_seconds, expire=real_expire_seconds)
        logger.info(f'缓存最短过期时间为{real_expire_seconds} 秒')

        # 应用购物推送
        await crawler_callback_services.apply_shopping_push(search_result=search_result)

        return valid_flight_nos, real_expire_seconds
    except Exception as e:
        logger.exception(e)
        return [], 0


async def clear_cache_for_error(task_info: dict, error_code: int) -> bool:
    """
    清除特定错误情况下的缓存

    Args:
        task_info: 任务信息
        error_code: 错误码

    Returns:
        bool: 是否清除了缓存
    """
    try:
        if error_code in [
            ApiCodes.BOOK_TICKET_NOT_ENOUGH.value,
            ApiCodes.BOOK_PRICE_ERROR.value,
            ApiCodes.BOOK_FLIGHT_NOT_FOUND.value,
        ]:
            # 构建任务键
            task_key = f'{task_info["airline_code"]}-{task_info["dep_airport_code"]}-{task_info["arr_airport_code"]}-{task_info["dep_date"]}'

            # 初始化mongo
            flight_fare_mongo = FlightFareMongoService('flight_fare')
            collection_name = 'airline_fare_cache'

            # 删除缓存
            await flight_fare_mongo.delete_cache(
                collection_name=collection_name,
                site_code=f'airline_{task_info["airline_code"].lower()}',
                dep_airport_code=task_info['dep_airport_code'],
                arr_airport_code=task_info['arr_airport_code'],
                dep_date=task_info['dep_date'],
                return_date=task_info.get('return_date', ''),
                invalid_flight_nos=[task_info['flight_no']],
            )

            # 删除任务锁
            await task_services.del_task_key(task_key=task_key)
            logger.warning(f'删除任务锁: {task_key}')
            return True
    except Exception as e:
        logger.exception(e)
    return False


def calculate_adjusted_expire_time(original_expire_time: str) -> str:
    """
    计算调整后的过期时间

    Args:
        original_expire_time: 原始过期时间

    Returns:
        str: 调整后的过期时间
    """
    expire_time = datetime.strptime(original_expire_time, "%Y-%m-%d %H:%M:%S")
    expire_minutes = (expire_time - datetime.now()).seconds / 60

    # 根据原始过期时间调整
    if expire_minutes < 60:  # 60分钟以下，缩短一半
        adjusted_minutes = expire_minutes / 2
    else:  # 60分钟以上，减30分钟
        adjusted_minutes = expire_minutes - 30

    # 确保至少有1分钟
    adjusted_minutes = max(1, adjusted_minutes)

    # 计算新的过期时间
    new_expire_time = (datetime.now() + timedelta(minutes=adjusted_minutes)).strftime("%Y-%m-%d %H:%M:%S")

    return new_expire_time
