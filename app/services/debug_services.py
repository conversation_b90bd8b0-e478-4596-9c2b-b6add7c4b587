from loguru import logger


async def debug():
    from app.services import crawler_callback_services

    search_result = {
        'status': 'success',
        'code': 0,
        'message': '成功',
        'data': {
            'results': [
                {
                    'trip_type': 'ow',
                    'trips': [
                        {
                            'trip_index': 1,
                            'airline_codes': ['ZE'],
                            'cabin_codes': ['T'],
                            'flight_nos': ['ZE872'],
                            'dep_airport_code': 'PVG',
                            'arr_airport_code': 'ICN',
                            'dep_date': '2024-10-20',
                            'dep_time': '00:30:00',
                            'arr_date': '2024-10-20',
                            'arr_time': '03:30:00',
                            'stop_times': 0,
                            'across_days': 0,
                            'segments': [
                                {
                                    'segment_index': 1,
                                    'flight_number': '872',
                                    'flight_no': 'ZE872',
                                    'dep_airport_code': 'PVG',
                                    'arr_airport_code': 'ICN',
                                    'dep_date': '2024-10-20',
                                    'dep_time': '00:30:00',
                                    'arr_date': '2024-10-20',
                                    'arr_time': '03:30:00',
                                    'stop_times': 0,
                                    'cabin': {'code': 'T', 'cabin_class': 'Y', 'name': '经济舱', 'desc': '经济舱'},
                                    'stops': [],
                                }
                            ],
                            'fares': {
                                'adult': {'base': 69000.0, 'tax': 84100.0, 'total': 153100.0, 'quantity': 1},
                                'child': {'base': 69000.0, 'tax': 84100.0, 'total': 153100.0, 'quantity': 0},
                                'infant': {'base': 69000.0, 'tax': 84100.0, 'total': 153100.0, 'quantity': 0},
                            },
                            'includes': {},
                            'add_ons': {},
                        }
                    ],
                }
            ],
            'exchange': {'currency_code': 'KRW', 'rates': {}, 'rate': {}},
        },
        'client_ip': '',
        'request_id': '',
    }
    search_result = await crawler_callback_services.parse_exchange(search_result=search_result)
    logger.debug(search_result)
