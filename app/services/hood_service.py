import copy
from datetime import datetime, timedelta
from typing import Union

from sqlalchemy import func
from app.config import settings
import orjson
from app.consts.types import FareType
from app.models.hood_data import HoodData
from app.services import base_data_service, public_services
from loguru import logger


async def get_hood_datas(
    dep_airport_codes: Union[list, str],
    arr_airport_codes: Union[list, str],
    dep_date: str,
    ex: int = 0,
    flight_no: str = None,
):
    """_summary_

    Args:
        dep_city_code (_type_): _description_
        arr_city_code (_type_): _description_
        dep_date (_type_): _description_
        ex (int, optional): 提前过期秒数，根据外部使用场景不同传入不同的值. Defaults to 0.
    """

    expire_time = datetime.now() - timedelta(seconds=ex)
    where_clause = []
    if isinstance(dep_airport_codes, str):
        where_clause.append(HoodData.dep_airport_code == dep_airport_codes)
    if isinstance(dep_airport_codes, list):
        where_clause.append(HoodData.dep_airport_code.in_(dep_airport_codes))
    if isinstance(arr_airport_codes, str):
        where_clause.append(HoodData.arr_airport_code == arr_airport_codes)
    if isinstance(arr_airport_codes, list):
        where_clause.append(HoodData.arr_airport_code.in_(arr_airport_codes))
    where_clause.append(HoodData.dep_date == dep_date)
    where_clause.append(HoodData.expired_time > expire_time.strftime('%Y-%m-%d %H:%M:%S'))
    if flight_no:
        where_clause.append(HoodData.flight_no == flight_no)
    rows = await HoodData.get_all_async(*where_clause, order_by=HoodData.src_adult_base.desc())
    logger.debug(f'rows: {rows}')
    return rows


def marge_hood_datas(rows):
    """
    按 airline_code,dep,arr,date,flight_no进行合并


    Args:
        rows (_type_): _description_
    """

    def replace_price(fare: dict, row: dict):
        fare['src_base'] = fare['base']
        fare['src_tax'] = fare['tax']
        fare['src_total'] = fare['total']
        fare['base'] = row['adult_base']
        fare['tax'] = row['adult_tax']
        fare['total'] = row['adult_base'] + row['adult_tax']
        fare['src_currency'] = row['src_currency_code']
        fare['quantity'] = row['quantity']
        return fare

    # 按价格由大到小进行排序
    rows.sort(key=lambda x: x['adult_base'] + x['adult_tax'], reverse=True)
    flights = {}
    for r in rows:
        ukey = f'{r["airline_code"]}_{r["dep_airport_code"]}_{r["arr_airport_code"]}_{r["dep_date"]}_{r["flight_no"]}'
        flight_info = orjson.loads(r['flight_info'])
        expire_seconds = int(
            (datetime.strptime(r['expired_time'], '%Y-%m-%d %H:%M:%S') - datetime.now()).total_seconds()
        )
        if ukey not in flights:

            flights[ukey] = copy.deepcopy(flight_info)
            flights[ukey]['trips'][0]['expire_seconds'] = expire_seconds
            flights[ukey]['trips'][0]['fares']['adult'] = replace_price(flights[ukey]['trips'][0]['fares']['adult'], r)
            flights[ukey]['trips'][0]['fares']['child'] = replace_price(flights[ukey]['trips'][0]['fares']['child'], r)
            flights[ukey]['trips'][0]['fares']['infant'] = replace_price(
                flights[ukey]['trips'][0]['fares']['infant'], r
            )
            flights[ukey]['unique_id'] = [str(r['id'])]
        else:
            # 按最短过期时间为准
            if expire_seconds < flights[ukey]['trips'][0]['expire_seconds']:
                flights[ukey]['trips'][0]['expire_seconds'] = expire_seconds
            flights[ukey]['trips'][0]['fares']['adult']['quantity'] += r['quantity']
            flights[ukey]['trips'][0]['fares']['child']['quantity'] += r['quantity']
            flights[ukey]['trips'][0]['fares']['infant']['quantity'] += r['quantity']
            flights[ukey]['unique_id'].append(str(r['id']))
    logger.debug(f'flights ({len(flights.keys())}): {list(flights.keys())}')
    result = list(flights.values())
    # todo 对压位数量进行保护，暂时固定在代码里
    for r in result:
        r['unique_id'] = '_'.join(r['unique_id'])
        r['trips'][0]['fares']['adult']['quantity'] = 1
        r['trips'][0]['fares']['child']['quantity'] = 0
        r['trips'][0]['fares']['infant']['quantity'] = 0
        # quantity = r['trips'][0]['fares']['adult']['quantity']
        # if quantity <= 3:
        #     r['trips'][0]['fares']['adult']['quantity'] = 1
        #     r['trips'][0]['fares']['child']['quantity'] = 0
        #     r['trips'][0]['fares']['infant']['quantity'] = 0
        # elif quantity <= 5:
        #     r['trips'][0]['fares']['adult']['quantity'] = 2
        #     r['trips'][0]['fares']['child']['quantity'] = 0
        #     r['trips'][0]['fares']['infant']['quantity'] = 0
        # elif quantity <= 9:
        #     r['trips'][0]['fares']['adult']['quantity'] = 3
        #     r['trips'][0]['fares']['child']['quantity'] = 0
        #     r['trips'][0]['fares']['infant']['quantity'] = 0
        # elif quantity > 9:
        #     r['trips'][0]['fares']['adult']['quantity'] = quantity / 2
        #     r['trips'][0]['fares']['child']['quantity'] = quantity / 2
        #     r['trips'][0]['fares']['infant']['quantity'] = 0
    return result


async def compute_fare(hood_datas: list, fare_map: dict, update_actual_float: bool):
    result = []
    for hd in hood_datas:
        airline_code = ','.join(hd['trips'][0]['airline_codes'])
        dep_airport_code = hd['trips'][0]['dep_airport_code']
        arr_airport_code = hd['trips'][0]['arr_airport_code']
        flight_no = ','.join(hd['trips'][0]['flight_nos'])
        dep_date = hd['trips'][0]['dep_date']
        fare_row = filter_fare(airline_code, dep_airport_code, arr_airport_code, flight_no, fare_map)
        logger.debug(f'fare_row: {fare_row}')
        if not fare_row:
            continue

        # fare_key = public_services.generate_fare_key(
        #     dep_city_code=dep_city_code,
        #     arr_city_code=arr_city_code,
        #     dep_date=dep_date,
        #     flight_no=flight_no,
        #     cabin=fare_row['adult_cabin_code'],
        #     cabin_class='Y',
        #     src_currency=hd['trips'][0]['fares']['adult']['src_currency'],
        #     fare_id=fare_row['id'],
        #     unique_id=hd['trips'][0]['fares']['fare_key'],
        #     fare_type=FareType.PRE_ORDER.value,
        # )
        await public_services.apply_rule(
            trip_info=hd['trips'][0], fare_rule_row=fare_row, update_actual_float=update_actual_float
        )
        logger.debug(f'rs: {hd["trips"][0]}')
        result.append(hd)
        # if fare_row:
        #     result.append(fare_row)
    return result


def filter_fare(airline_code: str, dep_airport_code: str, arr_airport_code: str, flight_no: str, fare_map: dict):
    rows = fare_map.get(airline_code, [])
    if not rows:
        return None
    for row in rows:
        if flight_no in row['exclude_flight_nos']:
            logger.debug(f'{row["id"]} flight_no {flight_no} in exclude_flight_nos {row["exclude_flight_nos"]}')
            continue
        if row['flight_nos'] not in ('*', '') and flight_no not in row['flight_nos']:
            logger.debug(f'{row["id"]} flight_no {flight_no} not in flight_nos {row["flight_nos"]}')
            continue
        # if cabin_code not in row['cabin_codes'] and row['cabin_codes'] not in ('*', ''):
        #         logger.debug(
        #             f'{row["id"]} cabin_code {cabin_code} not in cabin_codes {row["cabin_codes"]}'
        #         )
        #         continue
        if row['dep_airport_code'] not in [dep_airport_code, '*']:
            logger.debug(
                f'{row["id"]} dep_airport_code {dep_airport_code} not in dep_airport_code {row["dep_airport_code"]}'
            )
            continue
        if row['arr_airport_code'] not in [arr_airport_code, '*']:
            logger.debug(
                f'{row["id"]} arr_airport_code {arr_airport_code} not in arr_airport_code {row["arr_airport_code"]}'
            )
            continue
        return row
    return None


async def check_hood_data(
    dep_airport_code: str,
    arr_airport_code: str,
    dep_date: str,
    flight_no: str,
    src_adult_base: float,
    src_adult_tax: float,
    passenger_num: int,
):
    limit_time = datetime.now() - timedelta(minutes=settings.HOOD_DATA_EXPIRM_TIME)
    row = await HoodData.get_by_async(
        HoodData.dep_airport_code == dep_airport_code,
        HoodData.arr_airport_code == arr_airport_code,
        HoodData.dep_date == dep_date,
        HoodData.flight_no == flight_no,
        HoodData.expired_time > limit_time,
        HoodData.src_adult_base <= src_adult_base,
        HoodData.src_adult_tax <= src_adult_tax,
        columns=[func.sum(HoodData.quantity).label('quantity')],
    )

    if row and row['quantity'] >= passenger_num:
        return True
    return False

