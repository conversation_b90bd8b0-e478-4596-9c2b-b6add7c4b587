'''
@Author: your name
@Date: 2020-02-06 10:26:15

航线表相关业务逻辑
'''

from loguru import logger
import orjson
from pydantic import BaseModel

from app.models.fetch_rule import FetchRule
from app.models.airline import Airline
from commons.consts.common_types import YesOrNo
from commons.consts.flight.resource_site import FlightSiteStatus, FlightSiteType
from app.extensions.db_extras import get_db_session_async
from sqlalchemy import and_, or_, select, update, delete

from sqlalchemy.orm import aliased


async def get_by_air_route(dep_code: str, arr_code: str):
    """取得航线对应的资源站"""
    # resource_site_rows = await ResourceSite.get_all_async(
    #     ResourceSite.site_status == FlightResourceSiteStatus.NORMAL.value,
    # )
    # 这个判断计划下放到booking
    # 因为个航司的航线数据各不相同

    async with get_db_session_async() as session:

        stmt = (
            select(
                Airline.site_code,
                Airline.site_name,
                Airline.airline_code,
                Airline.currency_code,
                FetchRule.dep_airport_code,
                FetchRule.arr_airport_code,
            )
            .join(FetchRule, Airline.id == FetchRule.resource_site_id, isouter=True)
            .where(
                or_(
                    and_(FetchRule.dep_airport_code == dep_code, FetchRule.arr_airport_code == arr_code),
                    Airline.site_type == FlightSiteType.OTA.value,
                )
            )
        )
        row_set = await session.execute(stmt)
        resource_site_rows = [Airline.to_dict(row) for row in row_set.all()]

    return resource_site_rows


def request_pre_process(item: BaseModel):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    if 'airline_code' in params:
        params['airline_code'] = params['airline_code'].upper()
    if item.site_type == FlightSiteType.AIRLINE.value:
        if 'airline_code' in params:
            params['site_code'] = 'airline_' + params['airline_code'].lower()
        if 'airline_name' in params:
            params['site_name'] = params['airline_name']
    elif item.site_type == FlightSiteType.OTA.value:
        if 'site_code' in params:
            params['airline_code'] = params['site_code'].upper()
        if 'site_name' in params:
            params['airline_name'] = params['site_name']
    return params
