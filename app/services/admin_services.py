from datetime import datetime, timed<PERSON>ta
from typing import Union
from typing_extensions import Annotated
from fastapi import Depends, HTTPException
import jwt
from loguru import logger
from passlib.context import Crypt<PERSON>ontext
from fastapi import status
from pydantic import BaseModel
from app.consts.status import AdminU<PERSON>Status
from app.depends import get_current_admin_id
from app.models.admin import Admin
from app.models.admin_role import AdminRole
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from app.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


# def verify_password(plain_password, hashed_password):
#     return pwd_context.verify(plain_password, hashed_password)


# def get_password_hash(password):
#     return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Union[timedelta, None] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


async def create_admin(
    username: str,
    password: str,
    roles: str,
    username_desc: str = '',
    is_super: bool = False,
    status: str = AdminUserStatus.NORMAL.value,
):
    admin_row = await Admin.get_by_async(Admin.username == username)
    if admin_row:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"用户 {username} 已存在")
    if password.startswith('$'):
        hashed_password = password
    else:
        hashed_password = pwd_context.hash(password)
    admin_row = await Admin.create_at_async(
        username=username,
        password=hashed_password,
        username_desc=username_desc,
        roles=roles,
        is_super=YesOrNo.YES.value if is_super else YesOrNo.NO.value,
        status=status,
    )
    return admin_row


async def authenticate_admin(username: str, password: str):
    admin_row = await Admin.get_by_async(Admin.username == username)

    if not admin_row:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"用户 {username} 不存在")

    if not pwd_context.verify(password, admin_row['password']):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="密码错误")

    if admin_row['status'] != AdminUserStatus.NORMAL.value:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户已被禁用")

    return admin_row


async def get_current_admin(admin_id: Annotated[int, Depends(get_current_admin_id)]):
    # logger.debug(admin_id)
    admin_row = await Admin.get_by_async(Admin.id == admin_id)
    if not admin_row:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="验证失败，用户不存在")

    if not admin_row['access_token']:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="验证失败，token 已过期，请重新登录")

    if admin_row['status'] != AdminUserStatus.NORMAL.value:
        await Admin.update_by_async(Admin.id == admin_id, access_token='')
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="用户已被禁用")

    admin_row = await get_privileges(admin_row)

    return admin_row


def request_pre_process(item: BaseModel):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    if 'roles' in params and params['roles']:
        params['roles'] = ','.join(params['roles'])
    if 'confirm_password' in params:
        del params['confirm_password']
    if 'password' in params:
        params['password'] = pwd_context.hash(params['password'])
    return params


def response_pre_process(row: dict):
    if 'roles' in row and row['roles']:
        row['roles'] = row['roles'].split(",")
    else:
        row['roles'] = []
    return row


async def get_privileges(current_admin: dict):
    privileges = []
    if current_admin['is_super'] == YesOrNo.YES.value:
        privileges = ["*"]
    elif current_admin['roles']:
        roles = current_admin['roles'].split(",")
        admin_role_rows = await AdminRole.get_all_async(
            AdminRole.role_code.in_(roles), AdminRole.status == EnableStatus.ENABLED.value
        )
        for admin_role_row in admin_role_rows:
            privileges += admin_role_row['privileges'].split(",")
    current_admin['privileges'] = list(set(privileges))
    return current_admin
