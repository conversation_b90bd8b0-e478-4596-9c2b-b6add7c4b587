from loguru import logger
from app.models.airline_account import AirlineAccount
from commons.consts.common_status import EnableStatus
from commons.utils import CipherUtils
from app.config import settings

async def create_airline_account(airline_code: str, username: str, password: str):
    """创建航空公司账号"""
    airline_account_row = await AirlineAccount.get_by_async(AirlineAccount.airline_code == airline_code, AirlineAccount.username == username)
    if airline_account_row:
        if password == airline_account_row['password']:
            logger.warning(f"航空公司账号已存在: {airline_code} {username}，密码相同")
        else:
            await AirlineAccount.update_by_async(AirlineAccount.id == airline_account_row['id'], password=password)
            logger.info(f"航空公司账号密码更新: {airline_code} {username}")
    else:
        airline_account_row = await AirlineAccount.create_at_async(airline_code=airline_code.upper(), username=username, password=password)
        logger.info(f"创建航空公司账号: {airline_code} {username}")
    
    return airline_account_row

async def switch_airline_account(airline_code: str, username: str):
    """切换航空公司账号"""
    airline_account_row = await AirlineAccount.get_by_async(AirlineAccount.airline_code == airline_code, AirlineAccount.username == username)
    if airline_account_row:
        await AirlineAccount.update_by_async(AirlineAccount.id == airline_account_row['id'], status=EnableStatus.ENABLED.value)
        await AirlineAccount.update_by_async(AirlineAccount.airline_code == airline_code, AirlineAccount.id != airline_account_row['id'], status=EnableStatus.DISABLED.value)
        logger.info(f"切换航空公司账号: {airline_code} {username}")
    else:
        logger.warning(f"航空公司账号不存在: {airline_code} {username}")
