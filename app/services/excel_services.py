'''
@Author: your name
@Date: 2020-02-06 10:26:15

航线表相关业务逻辑
'''

from io import BytesIO
from loguru import logger
from openpyxl import load_workbook
import pandas as pd


async def get_template(empty_dates):
    df = pd.DataFrame(empty_dates)
    # 处理数据
    # 示例处理：这里只是简单地将 DataFrame 转换回 Excel
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Sheet1')
    output.seek(0)
    return output


async def upload_to_dataframe(file):
    content = file.file.read()
    # 使用 openpyxl 读取 Excel 文件并计算公式
    workbook = load_workbook(filename=BytesIO(content), data_only=False)
    sheet = workbook.active

    # 将 Excel 表格数据转化为 DataFrame
    data = sheet.values
    columns = next(data)  # 获取第一行作为列名
    df = pd.DataFrame(data, columns=columns)
    return df
