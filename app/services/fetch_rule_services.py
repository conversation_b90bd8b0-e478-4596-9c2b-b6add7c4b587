'''
@Author: your name
@Date: 2020-02-06 10:26:15

航线表相关业务逻辑
'''

from datetime import datetime
from loguru import logger

import orjson
from sqlalchemy import and_, or_


from app.consts.types import FareValidType, FetchType
from app.models.fetch_rule import FetchRule
from app.models.operate_log import OperateLog
from commons.consts.common_status import EnableStatus, SuccessStatus


def request_pre_process(new_row):
    if 'dep_airport_code' in new_row:
        new_row['dep_airport_code'] = new_row['dep_airport_code'].upper()
    if 'arr_airport_code' in new_row:
        new_row['arr_airport_code'] = new_row['arr_airport_code'].upper()
    if 'currency_code' in new_row:
        new_row['currency_code'] = new_row['currency_code'].upper()
    if new_row.get('float_seconds'):
        new_row['float_seconds'] = orjson.dumps(new_row['float_seconds']).decode('utf-8')
    else:
        new_row['float_seconds'] = ''
    return new_row


def format_out(row):
    if row.get('float_seconds'):
        row['float_seconds'] = orjson.loads(row['float_seconds'])
    else:
        row['float_seconds'] = []
    return row


async def get_rule(airline_code, dep_airport_code, arr_airport_code, dep_date):
    flight_days = (datetime.strptime(dep_date, '%Y-%m-%d').date() - datetime.now().date()).days
    fetch_rule_row = await FetchRule.get_by_async(
        FetchRule.airline_code == airline_code,
        FetchRule.status == EnableStatus.ENABLED.value,
        and_(
            or_(
                and_(
                    or_(FetchRule.dep_airport_code == dep_airport_code, FetchRule.dep_airport_code == '*'),
                    or_(FetchRule.arr_airport_code == arr_airport_code, FetchRule.arr_airport_code == '*'),
                )
            ),
            or_(
                and_(
                    FetchRule.fetch_type == FetchType.DATE.value,
                    FetchRule.flight_start_date <= dep_date,
                    FetchRule.flight_end_date >= dep_date,
                ),
                and_(
                    FetchRule.fetch_type == FetchType.DAYS.value,
                    FetchRule.start_days <= flight_days,
                    FetchRule.end_days >= flight_days,
                ),
            ),
        ),
        order_by=[FetchRule.priority_level.desc()],
    )
    return fetch_rule_row
