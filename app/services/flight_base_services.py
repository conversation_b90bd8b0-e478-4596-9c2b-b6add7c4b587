'''
@Author: your name
@Date: 2020-02-06 10:26:15

航线表相关业务逻辑
'''

from datetime import datetime, time
from io import BytesIO
import re
from fastapi import HTTPException
from loguru import logger
from openpyxl import load_workbook
import orjson
import pandas as pd


def is_empty_line(row, line_len=1):
    empty_line = False
    for i in range(line_len):
        if isinstance(row[i], str):
            row[i] = row[i].strip()
        if not row[i] or pd.isna(row[i]):
            continue
        break
    else:
        empty_line = True
    return empty_line


def check_not_null(row):
    logger.error('*' * 20)
    if not row[0]:
        raise HTTPException(500, '航司二字码不能为空，请修正后重新上传')
    if not row[1]:
        raise HTTPException(500, '航班号不能为空，请修正后重新上传')
    if not row[2]:
        raise HTTPException(500, '出发机场不能为空，请修正后重新上传')
    if not row[3]:
        raise HTTPException(500, '到达机场不能为空，请修正后重新上传')
    if not row[4]:
        raise HTTPException(500, '起飞时刻不能为空，请修正后重新上传')
    if not row[5]:
        raise HTTPException(500, '到达时刻不能为空，请修正后重新上传')
    if not row[6]:
        raise HTTPException(500, '是否共享航班不能为空，请修正后重新上传')
    if not row[7]:
        raise HTTPException(500, '生效日期不能为空，请修正后重新上传')
    if not row[8]:
        raise HTTPException(500, '失效日期不能为空，请修正后重新上传')
    if not row[9]:
        raise HTTPException(500, '机型不能为空，请修正后重新上传')

    # if not row[10]:
    # raise HTTPException(500, '有航司为空的记录，请修正后重新上传')
    if not row[11]:
        raise HTTPException(500, '班期不能为空，请修正后重新上传')
    row[11] = str(int(row[11]))
    pattern = re.compile(r"^[1-7]+$")
    if not bool(pattern.match(row[11])):
        # logger.error(f"班期格式不正确,班期为{tmp['schedules']}")
        raise HTTPException("有班期错误的记录，班期必须是1~7数字")


async def dataframe_to_records(data: pd.DataFrame):
    records = []
    today = datetime.today().date()
    for row in data.values.tolist():
        logger.debug(row)
        try:
            if is_empty_line(row=row, line_len=12):
                logger.debug('跳过空行')
                continue

            check_not_null(row=row)

            dep_time = row[4]
            logger.debug(type(dep_time))
            if isinstance(dep_time, time):
                dep_time = dep_time.strftime('%H:%M')
            else:
                dep_time = dep_time.strip()
            arr_time = row[5]
            logger.debug(type(arr_time))
            if isinstance(arr_time, time):
                arr_time = arr_time.strftime('%H:%M')
            else:
                arr_time = arr_time.strip()
            start_date = row[7]
            if isinstance(start_date, (pd.Timestamp, datetime)):
                start_date = str(start_date.date()).strip()
            else:
                start_date = str(start_date).strip()
            end_date = row[8]
            if isinstance(end_date, (pd.Timestamp, datetime)):
                end_date = str(end_date.date()).strip()
            else:
                end_date = str(end_date).strip()

            end_day = datetime.strptime(end_date, '%Y-%m-%d').date()
            if end_day < today:
                logger.debug(f'跳过过期数据 {row}')
                continue

            # 处理数据
            tmp = {
                'airline_code': row[0].upper().strip(),
                'flight_no': row[1].upper().strip(),
                'dep_airport_code': row[2].upper().strip(),
                'arr_airport_code': row[3].upper().strip(),
                'dep_time': dep_time,
                'arr_time': arr_time,
                'share_code': True if row[6].upper().strip() == 'Y' else False,
                'aircraft_code': str(row[9]).upper().strip(),
                'start_date': start_date,
                'end_date': end_date,
                'stops': parse_stops(row[10]),
                'schedules': str(int(row[11])).strip(),
                'days': int(row[12]),
            }
            # 判断班期是空或者是1~7

            records.append(tmp)
        except Exception as e:
            # logger.exception(e)
            raise

    return records


def parse_stops(stops_str: str):
    stops = []
    if not stops_str:
        return stops
    rows = stops_str.split(',')
    for idx in range(len(rows)):
        tmp = rows[idx].strip().split(';')
        stops.append(
            {
                'stop_index': idx,
                'stop_airport_code': tmp[0].strip(),
                'stop_airport_name': tmp[1].strip(),
                'stop_arr_time': tmp[2].strip(),
                'stop_dep_time': tmp[3].strip(),
            }
        )
    return stops
