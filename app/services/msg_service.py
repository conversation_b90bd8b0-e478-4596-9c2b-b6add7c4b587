from datetime import datetime
from typing import Any
from app.consts import status

from loguru import logger

from app.extensions.ding_robot import Robot

from app.config import settings


async def verify_order_alert(
    mock_pnr: str,
    code: int,
    message: str,
    real_pnr: str = None,
    sure_expire_time: str = None,
    session_id: str = None,
    unique_id: str = None,
    flight_info_str: str = None,
):
    try:
        title = f" 生单占座提醒 ({settings.DING_ENV})：{code}：{message}"
        msg = [
            f"#### 生单占座提醒 ({settings.DING_ENV})",
            f"- 航班信息：{flight_info_str}",
            f"- 假PNR：{mock_pnr}",
            f"- 真PNR：**{real_pnr}**",
            f'- 状态：**{code}：{message}**',
            f"- 过期时间：{sure_expire_time}",
            f"- session_id：{session_id}",
            f"- request_id：{unique_id}",
            f"- time： {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        ]
        # logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')

        Robot.send(token=settings.DING_TOKEN, msg='\n'.join(msg), msg_type='markdown', title=title)
    except Exception as e:
        logger.exception(e)


async def scan_order_alert(
    mock_pnr: str,
    code: int,
    message: str,
    real_pnr: str = None,
    sure_expire_time: str = None,
    # session_id: str = None,
    # unique_id: str = None,
    flight_info_str: str = None,
):
    try:
        title = f" 扫描占座提醒 ({settings.DING_ENV})：{code}：{message}"
        msg = [
            f"#### 扫描占座提醒 ({settings.DING_ENV})",
            f"- 航班信息：{flight_info_str}",
            f"- 假PNR：{mock_pnr}",
            f"- 扫描PNR：**{real_pnr}**",
            f'- 状态：**{code}：{message}**',
            f"- 过期时间：{sure_expire_time}",
            # f"- session_id：{session_id}",
            # f"- request_id：{unique_id}",
            f"- time： {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        ]
        # logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')

        Robot.send(
            token=settings.DING_TOKENS.get('business', settings.DING_TOKEN),
            msg='\n'.join(msg),
            msg_type='markdown',
            title=title,
        )
    except Exception as e:
        logger.exception(e)


def scan_timeout(mock_pnr: str):
    try:
        title = f" 扫描超时提醒 ({settings.DING_ENV})：{mock_pnr}"
        msg = [
            f"#### 扫描超时提醒 ({settings.DING_ENV})",
            f"- 假PNR：**{mock_pnr}**",
            f"- 扫描超时，请**立即**操作出票",
            f"- time： {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        ]
        # logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')

        Robot.send(
            token=settings.DING_TOKENS.get('business', settings.DING_TOKEN),
            msg='\n'.join(msg),
            msg_type='markdown',
            title=title,
        )
    except Exception as e:
        logger.exception(e)


async def tb_fetch_alert(msg: str):
    try:
        title = f" 淘宝数据抓取提醒 ({settings.DING_ENV})"
        msg = [
            f"#### 淘宝数据抓取提醒 ({settings.DING_ENV})",
            f"- {msg}",
            f"- time： {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        ]
        # logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')

        Robot.send(
            # 技术部
            token=settings.DING_TOKENS.get('technology', settings.DING_TOKEN),
            msg='\n'.join(msg),
            msg_type='markdown',
            title=title,
        )
    except Exception as e:
        logger.exception(e)
