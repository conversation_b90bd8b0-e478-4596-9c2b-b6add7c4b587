from pydantic import BaseModel


def request_pre_process(item: BaseModel):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    if 'role_code' in params:
        params['role_code'] = params['role_code'].upper()
    if 'privileges' in params and params['privileges']:
        params['privileges'] = ','.join(params['privileges'])
    return params


def response_pre_process(row: dict):
    if 'privileges' in row and row['privileges']:
        row['privileges'] = row['privileges'].split(",")
    else:
        row['privileges'] = []
    return row
