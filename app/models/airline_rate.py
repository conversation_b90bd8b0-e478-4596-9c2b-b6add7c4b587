from pydantic import BaseModel
from sqlalchemy import DECIMAL, TEXT, Column, Integer, String, UniqueConstraint
from commons.consts.common_status import EnableStatus
from commons.consts.flight.resource_site import FlightSiteStatus, FlightSiteType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class AirlineRate(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "airline_rates"
    __table_args__ = (
        UniqueConstraint("airline_code", "currency_code", name="uix_airline_currency"),
        {"comment": "航空公司汇率表"},
    )

    airline_code = Column(String(8), nullable=False, server_default="", comment="航班二字码")
    currency_code = Column(
        String(8),
        nullable=False,
        server_default='',
        comment='抓取时指定支付币种，传空时爬虫自行使用目标网站(城市)默认币种',
    )
    # 固定汇率
    fixed_rate = Column(DECIMAL(24, 12), nullable=True, server_default="0", comment="固定汇率，小数点后8位")
    # 减免手续费
    discount_fee = Column(
        DECIMAL(20, 2), nullable=True, server_default="0", comment="减免手续费，小数点后2位，注意和抓取币种一致"
    )
