from pydantic import BaseModel
from sqlalchemy import TEXT, <PERSON><PERSON><PERSON>, Integer, String
from commons.consts.common_status import EnableStatus
from commons.consts.flight.resource_site import FlightSiteStatus, FlightSiteType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class OTAPlatform(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "ota_platforms"
    __table_args__ = {"comment": "投放平台表"}

    ota_name = Column(String(64), nullable=False, server_default="", comment="公司中文名")
    ota_code = Column(String(16), nullable=False, server_default="", unique=True, comment="平台代码")

    status = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.ENABLED.value,
        comment=f"站点状态: {EnableStatus.mappings()}",
    )
