from sqlalchemy import DATETIME, DECIMAL, TEXT, Column, Integer, String, UniqueConstraint
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class CountyDefaultAirport(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "county_default_airports"
    __table_args__ = (
        UniqueConstraint("country_id", "airport_id", name="uix_cid_aid"),
        {"comment": "基础数据，国家默认机场表"},
    )

    country_id = Column(Integer, nullable=False, server_default="0", comment="国家ID")
    airport_id = Column(Integer, nullable=False, server_default="0", comment="机场ID")
