from sqlalchemy import <PERSON>ATETIM<PERSON>, DECIMAL, TEXT, Column, Date, Index, Integer, String, Constraint
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from sqlalchemy.dialects.mysql import LONGTEXT


class HoodData(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "hood_datas"
    __table_args__ = (
        Index("idx_dep_arr_date", "dep_airport_code", "arr_airport_code", "dep_date"),
        {"comment": "压位记录表"},
    )

    airline_code = Column(String(4), nullable=False, comment="航司(站点)ID，外键")
    dep_airport_code = Column(String(4), nullable=False, comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, comment="到达机场三字码")
    dep_date = Column(Date, nullable=True, comment="航班开始日期")
    flight_no = Column(String(16), nullable=False, comment="航班号")
    cabin_code = Column(String(8), nullable=False, server_default="", comment="舱位编码")
    cabin_level = Column(String(8), nullable=False, server_default="", comment="舱位等级")
    quantity = Column(Integer, nullable=False, server_default="0", comment="占座数量")

    adult_base = Column(DECIMAL(20, 2), nullable=False, comment="压位价格(人民币)")
    adult_tax = Column(DECIMAL(20, 2), nullable=False, comment="税费(人民币)")
    src_adult_base = Column(DECIMAL(20, 2), nullable=False, comment="压位原始价格")
    src_adult_tax = Column(DECIMAL(20, 2), nullable=False, comment="税费")
    src_currency_code = Column(String(4), nullable=False, comment="原始币种")

    expired_time = Column(DATETIME, nullable=True, index=True, comment="过期时间")

    flight_info = Column(LONGTEXT, nullable=False, comment="航班信息")
    extra_info = Column(LONGTEXT, nullable=True, comment="额外信息")

    # 汇率
    ex_rate = Column(DECIMAL(24, 12), nullable=True, server_default="0", comment="汇率，小数点后8位")
    # 减免手续费
    discount_fee = Column(
        DECIMAL(20, 2), nullable=True, server_default="0", comment="减免手续费，小数点后2位，注意和抓取币种一致"
    )
