from pydantic import BaseModel
from sqlalchemy import DECIMAL, TEXT, Column, Integer, String, DATE
from app.consts.types import FareChannelType, FareFlightDateType, FareRuleMarkupType, FareType, FareValidType, FetchType
from commons.consts.common_status import EnableStatus
from commons.consts.flight.resource_site import FlightSiteStatus, FlightSiteType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class FareRule(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "fare_rules"
    __table_args__ = {"comment": "投放政策表"}
    batch_tags = Column(String(128), nullable=False, comment="批次标签，用于筛选")
    odbn = Column(
        String(32), nullable=False, server_default="", comment="ODBN（OTA Data Batch Number平台比价数据批次号）"
    )
    airline_code = Column(String(4), nullable=False, server_default="", comment="站点code，外键")
    channel_code = Column(
        String(16),
        nullable=False,
        server_default=FareChannelType.TB_NORMAL.value,
        comment="平台+产品线标识,用来区分投放渠道",
    )
    fare_valid_type = Column(
        String(16),
        nullable=False,
        server_default=FareValidType.DATE.value,
        comment=f"运价有效类型: {FareValidType.mappings()}",
    )
    fare_start_date = Column(DATE, nullable=True, comment="运价开始日期（含）yyyy-mm-dd hh:mm:ss")
    fare_end_date = Column(DATE, nullable=True, comment="运价结束日期（含）yyyy-mm-dd hh:mm:ss")

    flight_date_type = Column(
        String(16),
        nullable=False,
        server_default=FareFlightDateType.DATE.value,
        comment=f"航班日期类型: {FareFlightDateType.mappings()}",
    )
    start_days = Column(Integer, nullable=False, server_default="0", comment="从n天开始抓取（含n）")
    end_days = Column(Integer, nullable=False, server_default="0", comment="到n天抓取结束（含n）")
    flight_start_date = Column(DATE, nullable=True, comment="航班运行开始日期")
    flight_end_date = Column(DATE, nullable=True, comment="航班运行结束日期")
    dep_airport_code = Column(String(4), nullable=False, server_default="*", comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, server_default="*", comment="到达机场三字码")
    dep_city_code = Column(String(4), nullable=False, server_default="*", comment="出发城市三字码")
    arr_city_code = Column(String(4), nullable=False, server_default="*", comment="到达城市三字码")
    flight_nos = Column(String(128), nullable=False, server_default="*", comment="完整航班号，如TR101，多个以斜线/分割")
    exclude_flight_nos = Column(
        String(128), nullable=False, server_default="", comment="排除航班号，如TR101，多个以斜线/分割"
    )
    # cabin_class = Column(String(16), nullable=False, server_default="", comment="舱位等级，如经济舱Y")
    cabin_codes = Column(String(128), nullable=False, server_default="*", comment="舱位代码，如W、M等，多个以斜线/分割")
    priority_level = Column(Integer, nullable=False, server_default="20", comment="配置冲突时的优先级：1~99，默认20")
    status = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.ENABLED.value,
        comment=f"政策状态: {EnableStatus.mappings()}",
    )
    adult_markup_type = Column(
        String(16),
        nullable=False,
        server_default=FareRuleMarkupType.UN_SALE.value,
        comment=f"政策类型: {FareRuleMarkupType.mappings()}",
    )
    child_markup_type = Column(
        String(16),
        nullable=False,
        server_default=FareRuleMarkupType.UN_SALE.value,
        comment=f"政策类型: {FareRuleMarkupType.mappings()}",
    )
    infant_markup_type = Column(
        String(16),
        nullable=False,
        server_default=FareRuleMarkupType.UN_SALE.value,
        comment=f"政策类型: {FareRuleMarkupType.mappings()}",
    )

    adult_tag_base = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="成人票投放价，精确到分")
    child_tag_base = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="儿童票投放价，精确到分")
    infant_tag_base = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="婴儿票投放价，精确到分")

    adult_tag_tax = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="成人票税费投放价，精确到分")
    child_tag_tax = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="儿童票税费投放价，精确到分")
    infant_tag_tax = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="婴儿票税费投放价，精确到分")

    adult_cabin_code = Column(String(8), nullable=False, server_default="", comment="成人舱位编码")
    child_cabin_code = Column(String(8), nullable=False, server_default="", comment="儿童舱位编码")
    infant_cabin_code = Column(String(8), nullable=False, server_default="", comment="婴儿舱位编码")

    auto_float = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.DISABLED.value,
        comment=f"是否开启自动调价: {EnableStatus.mappings()}",
    )

    fare_type = Column(
        String(16), nullable=False, server_default=FareType.NORMAL.value, comment=f"运价类型: {FareType.mappings()}"
    )

    auto_min_float = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="自动调价最低浮动值，精确到分")
    auto_max_float = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="自动调价最高浮动值，精确到分")
