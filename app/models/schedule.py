from sqlalchemy import TEXT, Column, Date, Integer, String, UniqueConstraint


from commons.consts.common_status import EnableStatus
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class Schedule(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "schedules"
    __table_args__ = (
        UniqueConstraint(
            "airline_code",
            "start_date",
            "end_date",
            "dep_airport_code",
            "arr_airport_code",
            name="uix_ac_sd_ed_dep_arr",
        ),
        {"comment": "航班时刻表"},
    )

    airline_code = Column(String(4), nullable=False, index=True, server_default="", comment="航空公司二字码")
    start_date = Column(Date, nullable=False, index=True, comment="时刻表生效日期")
    end_date = Column(Date, nullable=False, index=True, comment="时刻表失效日期")
    dep_airport_code = Column(String(4), nullable=False, server_default="", comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, server_default="", comment="到达机场三字码")
    schedules = Column(String(16), nullable=False, server_default="1234567", comment="班期")
    status = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.ENABLED.value,
        comment=f"班期状态: {EnableStatus.mappings()}",
    )
