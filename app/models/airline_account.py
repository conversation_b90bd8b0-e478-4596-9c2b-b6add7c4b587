from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, TEXT, Column, Integer, String, UniqueConstraint
from sqlalchemy.dialects.mysql import LONGTEXT
from app.consts.status import AdminUserStatus
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class AirlineAccount(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "airline_accounts"
    __table_args__ = (UniqueConstraint('airline_code', 'username', name='uix_code_un'), {"comment": "航空公司账号表"})

    airline_code = Column(String(4), nullable=False, index=True, comment="航空公司代码")

    username = Column(String(128), nullable=False, index=True, comment="用户名")
    password = Column(String(512), nullable=False, comment="密码")
    used_times = Column(Integer, nullable=False, default=0, comment="使用次数")

    status = Column(
        String(8),
        nullable=False,
        server_default=EnableStatus.DISABLED.value,
        comment=f"状态: {EnableStatus.mappings()}",
    )
    remark = Column(String(512), nullable=True, comment="备注")
