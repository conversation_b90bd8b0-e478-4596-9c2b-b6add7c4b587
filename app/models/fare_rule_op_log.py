from sqlalchemy import DATETIME, DECIMAL, TEXT, Column, Integer, String, UniqueConstraint
from commons.consts.common_status import EnableStatus, SuccessStatus
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class FareRuleOperateLog(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "fare_rule_operate_logs"
    __table_args__ = ({"comment": "运价投放操作日志表"},)

    fare_rule_id = Column(Integer, nullable=False, index=True, server_default="0", comment="操作的数据ID")
    admin_id = Column(Integer, nullable=False, index=True, comment="账号ID")
    username = Column(String(32), nullable=False, server_default="", comment="用户名")
    op_type = Column(String(32), nullable=False, server_default='', comment="操作类型：新增/修改/删除")
    op_desc = Column(TEXT, nullable=False, comment="操作说明")
    status = Column(String(16), nullable=False, server_default='', comment=f"操作状态: {SuccessStatus.mappings()}")
    api_snapshot = Column(TEXT, nullable=False, comment="接口快照")
