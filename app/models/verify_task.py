from sqlalchemy import DECI<PERSON><PERSON>, TEXT, Column, DateTime, Index, Integer, String
from app.consts.status import TaskStatus
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from sqlalchemy.dialects.mysql import LONGTEXT


class VerifyTask(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "verify_tasks"
    __table_args__ = (
        Index("idx_dep_arr_date_ac", "dep_airport_code", "arr_airport_code", "dep_date", "airline_code"),
        {"comment": "验价任务"},
    )

    fare_id = Column(Integer, nullable=False, comment="运价ID")
    unique_id = Column(String(64), nullable=False, index=True, server_default='', comment="唯一ID")
    fare_key = Column(String(1000), nullable=False, server_default='', comment="运价key")
    airline_code = Column(String(16), nullable=False, server_default='', comment="航司二字码")
    dep_airport_code = Column(String(16), nullable=False, server_default='', comment="出发机场三字码")
    arr_airport_code = Column(String(16), nullable=False, server_default='', comment="到达机场三字码")
    dep_date = Column(String(16), nullable=False, server_default='', comment="出发日期")
    flight_no = Column(String(16), nullable=False, server_default='', comment="航班号")
    currency_code = Column(String(16), nullable=False, server_default='', comment="货币代码")
    passenger_count = Column(Integer, nullable=False, server_default='1', comment="乘客数量")
    task_status = Column(String(16), nullable=False, server_default=TaskStatus.PENDING.value, comment="任务状态")
    task_keep_end_time = Column(DateTime, nullable=False, index=True, comment="任务保留截止时间")
    callback_time = Column(DateTime, nullable=True, comment="回调时间")
    code = Column(Integer, nullable=False, server_default='-1', comment="验价结果")
    message = Column(String(1000), nullable=False, server_default='', comment="验价结果描述")

    verify_params = Column(LONGTEXT, nullable=True, comment="验价参数")
    verify_result = Column(LONGTEXT, nullable=True, comment="验价结果")
