from sqlalchemy import DATETIME, DECIMAL, TEXT, Column, Date, Index, Integer, String, Constraint, UniqueConstraint
from sqlalchemy.dialects.mysql import LONGTEXT
from app.consts.types import AddOnType, FareRuleMarkupType
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class FareRuleAddOn(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "fare_rule_add_ons"
    __table_args__ = (
        UniqueConstraint("fare_rule_id", "add_on_type", "add_on_desc", name="uix_frid_type_desc"),
        {"comment": "运价投放附加项表"},
    )

    fare_rule_id = Column(Integer, nullable=False, index=True, server_default="0", comment="运价ID")
    add_on_type = Column(String(16), nullable=False, comment=f"附加项类型: {AddOnType.mappings()}")
    add_on_code = Column(String(32), nullable=False, server_default='', comment="附加项编码")
    add_on_name = Column(String(32), nullable=False, server_default='', comment="附加项名称")
    add_on_desc = Column(String(256), nullable=False, comment="附加项描述")
    markup_type = Column(
        String(16),
        nullable=False,
        server_default=FareRuleMarkupType.UN_SALE.value,
        comment=f"政策类型: {FareRuleMarkupType.mappings()}",
    )
    add_on_tag_price = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="附加项投放价，精确到分")
