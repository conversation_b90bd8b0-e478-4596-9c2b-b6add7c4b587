from sqlalchemy import <PERSON>ATETIM<PERSON>, TEXT, Column, Integer, String
from sqlalchemy.dialects.mysql import LONGTEXT
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class OperateLog(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "operate_logs"
    __table_args__ = {"comment": "操作日志表"}

    op_table = Column(String(32), nullable=False, server_default='', index='', comment="操作的数据表")
    row_id = Column(Integer, nullable=False, index=True, comment="操作的数据ID")

    admin_id = Column(Integer, nullable=False, index=True, comment="账号ID")
    username = Column(String(32), nullable=False, server_default="", comment="用户名")
    op_type = Column(String(32), nullable=False, server_default='', index=True, comment="操作的数据标识")
    op_time = Column(DATETIME, nullable=False, comment="操作时间")
    op_desc = Column(String(1024), nullable=False, comment="操作说明")
    op_data = Column(TEXT, nullable=False, comment="原始json数据")
