from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, TEX<PERSON>, <PERSON>umn, Integer, String
from sqlalchemy.dialects.mysql import LONG<PERSON>XT
from app.consts.status import AdminUserStatus
from commons.consts.common_types import YesOrNo
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class Admin(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "admins"
    __table_args__ = {"comment": "管理员表"}

    username = Column(String(32), nullable=False, unique=True, comment="用户名")
    password = Column(String(256), nullable=False, comment="密码")

    username_desc = Column(String(32), comment='用户名,汉字')

    status = Column(
        String(8),
        nullable=False,
        server_default=AdminUserStatus.NORMAL.value,
        comment=f"状态: {AdminUserStatus.mappings()}",
    )
    # 注意:
    # mysql 和 mariadb 处理TEXT类型不一样，所以这里允许传入null
    is_super = Column(
        String(8), nullable=False, server_default=YesOrNo.NO.value, comment=f"是否超管: {YesOrNo.mappings()}"
    )
    roles = Column(String(256), nullable=False, server_default='', comment='角色，以逗号分割')

    latest_login_time = Column(DATETIME, nullable=False, server_default="1970-01-01 00:00:00", comment="最后登录时间")
    latest_login_ip = Column(String(64), nullable=False, server_default="", comment="最后登录IP")
    access_token = Column(String(512), nullable=True, comment="访问令牌")
