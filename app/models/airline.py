from pydantic import BaseModel
from sqlalchemy import TEXT, Column, Integer, String
from commons.consts.common_status import EnableStatus
from commons.consts.flight.resource_site import FlightSiteStatus, FlightSiteType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class Airline(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "airlines"
    __table_args__ = {"comment": "资源站点表"}

    airline_name = Column(String(64), nullable=False, server_default="", comment="公司中文名")
    airline_code = Column(String(8), nullable=False, server_default="", unique=True, comment="航班二字码")
    country_name = Column(String(64), nullable=False, server_default="", comment="国家中文名")

    site_code = Column(
        String(16), nullable=False, server_default="", unique=True, comment="站点编码，内部编码，约定后全系统唯一"
    )
    site_name = Column(String(64), nullable=False, server_default="", comment="站点名")
    site_url = Column(String(256), nullable=False, server_default="", comment="站点URL")
    site_type = Column(
        String(16),
        nullable=False,
        server_default=FlightSiteType.AIRLINE.value,
        comment=f"站点类型: {FlightSiteType.mappings()}",
    )
    site_desc = Column(String(256), nullable=False, server_default="", comment="站点描述")
    site_status = Column(
        String(16),
        nullable=False,
        server_default=FlightSiteStatus.NORMAL.value,
        comment=f"站点状态: {FlightSiteStatus.mappings()}",
    )

    abandoned_journey = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.DISABLED.value,
        comment=f"是否开启弃程: {EnableStatus.mappings()}",
    )
    expire_seconds = Column(Integer, nullable=False, server_default="0", comment="缓存过期秒数，仅OTA站点有效")
    auto_book = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.DISABLED.value,
        comment=f"是否开启自动预订: {EnableStatus.mappings()}",
    )
    auto_pay = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.DISABLED.value,
        comment=f"是否开启自动支付: {EnableStatus.mappings()}",
    )
    book_check = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.DISABLED.value,
        comment=f"是否开启预订校验: {EnableStatus.mappings()}",
    )
