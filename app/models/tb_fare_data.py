from sqlalchemy import DECIMAL, TEXT, Column, Constraint, Date, Index, Integer, String, UniqueConstraint


from commons.consts.common_status import EnableStatus
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class TBFareData(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "tb_fare_data"
    __table_args__ = (
        Index("idx_dc_ac_date_fno", "dep_city_code", "arr_city_code", "flight_date", "flight_no"),
        Index("idx_da_aa_date_fno", "dep_airport_code", "arr_airport_code", "flight_date", "flight_no"),
        {"comment": "TB平台比价数据表"},
    )
    channel_code = Column(String(16), nullable=False, index=True, server_default="", comment="平台+产品线标识")
    batch_no = Column(String(32), nullable=False, index=True, server_default="", comment="批次号")
    flight_date = Column(Date, nullable=False, comment="航班日期")
    airline_code = Column(String(4), nullable=False, server_default="", comment="航空公司二字码")
    flight_no = Column(String(16), nullable=False, server_default="", comment="航班号")

    cabin_code = Column(String(4), nullable=False, server_default="", comment="舱位代码")
    cabin_level = Column(String(4), nullable=False, server_default="", comment="舱位等级")

    dep_city_code = Column(String(4), nullable=False, server_default="", comment="出发城市三字码")
    arr_city_code = Column(String(4), nullable=False, server_default="", comment="到达城市三字码")
    dep_airport_code = Column(String(4), nullable=False, server_default="", comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, server_default="", comment="到达机场三字码")

    adult_base = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="成人底价")
    adult_tax = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="成人税费")
    adult_total = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="成人总价")

    self_base = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="自费底价")
    self_tax = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="自费税费")
    self_total = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="自费总价")

    # second_base = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="第二底价")
    # second_tax = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="第二税费")
    # second_total = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="第二总价")

    baggage_min_weight = Column(Integer, nullable=False, server_default="0", comment="行李额下限")
    baggage_max_weight = Column(Integer, nullable=False, server_default="0", comment="行李额上限")
