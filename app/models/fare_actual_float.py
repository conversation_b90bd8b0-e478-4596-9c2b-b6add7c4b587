from sqlalchemy import DECIMA<PERSON>, TEXT, Column, Date, UniqueConstraint, Integer, String
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from sqlalchemy.dialects.mysql import LONGTEXT


class FareActualFloat(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "fare_actual_floats"
    __table_args__ = (
        UniqueConstraint(
            "channel_code",
            "dep_airport_code",
            "arr_airport_code",
            "dep_date",
            "flight_no",
            "baggage_weight_range",
            name="idx_chinal_dep_arr_fn_date_bag",
        ),
        {"comment": "航班实际浮动"},
    )
    channel_code = Column(String(16), nullable=False, index=True, server_default="", comment="平台+产品线标识")
    airline_code = Column(String(4), nullable=False, comment="航司二字码")
    dep_airport_code = Column(String(4), nullable=False, comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, comment="到达机场三字码")
    dep_date = Column(Date, nullable=True, comment="出发日期")
    flight_no = Column(String(16), nullable=False, comment="航班号")
    baggage_weight_range = Column(String(8), nullable=False, comment="行李额范围")
    actual_float = Column(DECIMAL(10, 2), nullable=True, server_default='0.00', comment="实际浮动")
