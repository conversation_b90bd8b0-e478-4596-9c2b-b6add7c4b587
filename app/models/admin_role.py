from sqlalchemy import TEXT, Column, String
from commons.consts.common_status import EnableStatus
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from sqlalchemy.dialects.mysql import LONGTEXT


class AdminRole(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = 'admin_roles'
    __table_args__ = {"comment": "角色表"}

    role_name = Column(String(64), comment='角色名称')
    role_code = Column(String(32), comment='角色编码')
    privileges = Column(LONGTEXT, comment='权限列表，以逗号分割')
    status = Column(String(16), nullable=False, server_default=EnableStatus.ENABLED.value, comment='是否启用')
