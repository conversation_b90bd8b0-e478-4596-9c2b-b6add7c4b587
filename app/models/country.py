from sqlalchemy import DATETIME, DECIMAL, TEXT, Column, Integer, String, UniqueConstraint
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class Country(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "countries"
    __table_args__ = {"comment": "基础数据，国家表"}

    country_zh_name = Column(String(64), nullable=False, server_default='', comment="国家中文名")
    country_en_name = Column(String(128), nullable=False, server_default='', comment="国家英文名")
    country_code = Column(String(8), nullable=False, server_default='', unique=True, comment="国家二字码")
    currency_code = Column(String(8), nullable=False, server_default='', comment="币种")
    currency_name = Column(String(32), nullable=False, server_default='', comment="币种名")
