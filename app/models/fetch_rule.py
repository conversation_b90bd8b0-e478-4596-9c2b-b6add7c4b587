from sqlalchemy import DATETIME, DECIMAL, TEXT, Column, Date, Index, Integer, String, Constraint
from app.consts.types import FetchCacheType, FetchType
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class FetchRule(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "fetch_rules"
    __table_args__ = (
        Index("idx_ac_dep_arr", "airline_code", "dep_airport_code", "arr_airport_code"),
        {"comment": "抓取策略表"},
    )
    batch_tags = Column(String(128), nullable=False, comment="批次标签，用于筛选")

    airline_code = Column(String(4), nullable=False, comment="航司(站点)ID，外键")
    dep_airport_code = Column(String(4), nullable=False, index=True, comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, index=True, comment="到达机场三字码")
    fetch_type = Column(
        String(16), nullable=False, server_default=FetchType.DAYS.value, comment=f"抓取类型: {FetchType.mappings()}"
    )
    start_days = Column(Integer, nullable=False, server_default="0", comment="从n天开始抓取（含n）")
    end_days = Column(Integer, nullable=False, server_default="0", comment="到n天抓取结束（含n）")
    flight_start_date = Column(Date, nullable=True, comment="航班开始日期")
    flight_end_date = Column(Date, nullable=True, comment="航班结束日期")
    ota_codes = Column(String(64), nullable=False, server_default="", comment="需要同时抓取的ota平台：taobao,ctrip")
    expire_seconds = Column(Integer, nullable=False, server_default="0", comment="缓存过期时间，精确到秒，0表示不抓取")
    cache_type = Column(
        String(16),
        nullable=False,
        server_default=FetchCacheType.FIXED.value,
        comment=f"缓存类型：{FetchCacheType.mappings()}",
    )
    float_seconds = Column(TEXT, nullable=True, comment="浮动缓存时间，按余票数计算，精确到秒")
    priority_level = Column(Integer, nullable=False, server_default="20", comment="配置冲突时的优先级：1~99，默认20")
    currency_code = Column(
        String(8),
        nullable=False,
        server_default='',
        comment='抓取时指定支付币种，传空时爬虫自行使用目标网站(城市)默认币种',
    )

    status = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.ENABLED.value,
        comment=f"抓取策略状态: {EnableStatus.mappings()}",
    )
