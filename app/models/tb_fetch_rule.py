from sqlalchemy import DATETIME, DECIMAL, TEXT, Column, Date, Index, Integer, String, Constraint, CheckConstraint
from app.consts.types import FetchCacheType, FetchType
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class TbFetchRule(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "tb_fetch_rules"
    __table_args__ = (
        Index("idx_airline_dep_arr", "airline_code", "dep_airport_code", "arr_airport_code"),
        Index("idx_status_time", "status", "rule_start_time", "rule_end_time"),
        {'mysql_charset': 'utf8mb4', 'mysql_collate': 'utf8mb4_unicode_ci', 'comment': "tb抓取策略表"},
    )
    

    airline_code = Column(String(4), nullable=False, comment="航司(站点)ID，外键")
    dep_airport_code = Column(String(4), nullable=False, comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, comment="到达机场三字码")
   
    start_days = Column(Integer, nullable=False, server_default="0", comment="从n天开始抓取（含n）")
    end_days = Column(Integer, nullable=False, server_default="0", comment="到n天抓取结束（含n）")
    rule_start_time = Column(String(5), nullable=False, server_default="00:00", comment="规则开始时间(HH:mm格式)")
    rule_end_time = Column(String(5), nullable=False, server_default="00:00", comment="规则结束时间(HH:mm格式)")
    rule_interval = Column(Integer, nullable=False, server_default="600", comment="规则间隔时间,单位秒")
    last_fetch_time = Column(DATETIME, nullable=True, comment="最后抓取时间")
    

    status = Column(
        String(16),
        nullable=False,
        server_default=EnableStatus.ENABLED.value,
        comment=f"抓取策略状态: {EnableStatus.mappings()}",
    )
