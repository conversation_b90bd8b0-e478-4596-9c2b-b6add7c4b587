from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, TEXT, Column, Integer, String
from app.consts.status import ScanTask<PERSON>tatus
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions
from sqlalchemy.dialects.mysql import LONGTEXT
from app.consts.status import ScanTaskStatus


class VerifyTmpOrder(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "verify_tmp_orders"
    __table_args__ = {"comment": "验价生成的临时占座订单"}
    order_no = Column(String(64), nullable=False, unique=True, server_default='', comment="订单号")
    product_type = Column(String(16), nullable=False, server_default='', comment="产品类型")
    dep_airport_code = Column(String(4), nullable=False, server_default='', comment="出发机场三字码")
    arr_airport_code = Column(String(4), nullable=False, server_default='', comment="到达机场三字码")
    dep_date = Column(String(16), nullable=False, server_default='', comment="航班日期")
    flight_no = Column(String(16), nullable=False, server_default='', comment="航班号")
    mock_pnr = Column(String(16), nullable=False, server_default='', comment="模拟PNR")
    real_pnr = Column(String(16), nullable=False, server_default='', comment="实际PNR")
    task_status = Column(String(16), nullable=False, server_default='', comment="任务状态")
    celery_task_id = Column(String(256), nullable=False, server_default='', comment="celery任务ID")
    code = Column(Integer, nullable=False, server_default='-1', comment="爬虫回调状态")
    message = Column(String(512), nullable=False, server_default='', comment="爬虫回调信息")
    dep_expire_time = Column(String(32), nullable=False, server_default='', comment="按出发地计算的订单过期时间")
    expire_time = Column(String(32), nullable=False, server_default='', comment="当前系统时区的订单过期时间")
    sure_expire_time = Column(String(32), nullable=False, server_default='', comment="修改后实际用于判断的订单过期时间")
    order_info = Column(LONGTEXT, nullable=True, comment="请求信息")
    book_result = Column(LONGTEXT, nullable=True, comment="响应信息")

    # 以下为扫描出票相关字段
    scan_pnr = Column(String(16), nullable=False, server_default='', comment="扫描出票PNR")
    scan_status = Column(String(16), nullable=False, server_default=ScanTaskStatus.UNKNOW.value, comment="扫描任务状态")
    scan_expire_time = Column(String(32), nullable=False, server_default='', comment="扫描出票成功后的过期时间")
    scan_book_result = Column(LONGTEXT, nullable=True, comment="扫描出票信息")
    scan_times = Column(Integer, nullable=False, server_default='0', comment="扫描次数")

    pay_result = Column(LONGTEXT, nullable=True, comment="支付结果")

    latest_run_time = Column(String(32), nullable=False, server_default='', comment="最新运行时间")

    base_float = Column(Integer, nullable=False, server_default='0', comment="损失浮动")
    is_finish = Column(Integer, nullable=False, server_default='0', comment="是否完成")
