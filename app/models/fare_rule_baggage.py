from sqlalchemy import <PERSON><PERSON>ETIM<PERSON>, DECIMAL, TEXT, Column, Date, Index, Integer, String, Constraint, UniqueConstraint
from sqlalchemy.dialects.mysql import LONGTEXT
from app.consts.types import BaggageType, FareRuleMarkupType
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from commons.consts.currency_type import CurrencyType
from app.cores.base_model import BaseDBModel, MixinFields, MixinFunctions


class FareRuleBaggage(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "fare_rule_baggages"
    __table_args__ = (
        UniqueConstraint("fare_rule_id", "baggage_type", 'baggage_weight', name="uix_frid_type_weight"),
        {"comment": "压位投放价格表"},
    )

    fare_rule_id = Column(Integer, nullable=False, index=True, server_default="0", comment="运价ID")
    baggage_code = Column(String(32), nullable=False, server_default="", comment="行李种类编码")
    baggage_type = Column(
        String(16), nullable=False, server_default=BaggageType.HAND.value, comment=f"行李类型: {BaggageType.mappings()}"
    )
    baggage_weight = Column(Integer, nullable=False, server_default="0", comment="行李重量，单位kg")
    # add_on_name = Column(String(32), nullable=False, comment="附加项名称")
    # add_on_desc = Column(String(256), nullable=False, comment="附加项描述")
    markup_type = Column(
        String(16),
        nullable=False,
        server_default=FareRuleMarkupType.UN_SALE.value,
        comment=f"政策类型: {FareRuleMarkupType.mappings()}",
    )
    baggage_tag_price = Column(DECIMAL(20, 2), nullable=False, server_default="0", comment="附加项投放价，精确到分")
