from typing import Any

from loguru import logger
import orjson
from commons.cores.base_client import BaseAsyncClient


class CrawlerSdk(BaseAsyncClient):
    def __init__(self, host):
        super().__init__()
        self.host = host

    @property
    def default_headers(self):
        return {'Content-Type': 'application/json'}

    async def search(self, data: dict, request_id: str = None):
        url = f'{self.host}/api/v1/crawler/{data["airline_code"].lower()}/public/search'

        logger.debug(f'查询报价, url: {url}, data: {data}')
        if request_id:
            self.append_headers['X-Request-ID'] = request_id
        resp_text = await self._request('POST', url, json=data, timeout=60)
        return orjson.loads(resp_text)
