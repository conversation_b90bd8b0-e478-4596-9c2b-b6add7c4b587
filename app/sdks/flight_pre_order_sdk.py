import copy
from typing import Any

from loguru import logger
import orjson
from commons.cores.base_client import BaseAsyncClient


class FlightPreOrderSdk(BaseAsyncClient):
    def __init__(self, host):
        super().__init__()
        self.host = host

    @property
    def default_headers(self):
        return {'Content-Type': 'application/json'}

    async def search(self, data: dict):
        url = f'{self.host}/api/v1/flight_pre_order/public/fare/search'

        logger.debug(f'查询压位报价, url: {url}, data: {data}')
        resp_text = await self._request('POST', url, json=data)
        return orjson.loads(resp_text)

    async def price_verify(self, data: dict):
        url = f'{self.host}/api/v1/flight_pre_order/public/price/verify'

        logger.debug(f'验证压位报价, url: {url}, data: {data}')
        resp_text = await self._request('POST', url, json=data)
        return orjson.loads(resp_text)
