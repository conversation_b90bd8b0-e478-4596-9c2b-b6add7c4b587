from datetime import datetime, timed<PERSON>ta

from io import Bytes<PERSON>
from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Request, Response, UploadFile
from openpyxl import load_workbook
import orjson
import pandas as pd

from app.consts.status import TaskStatus
from app.consts.types import FareType
from app.models.airline_account import AirlineAccount
from app.models.fare_rule import FareRule
from app.models.ota_platform import OTAPlatform

from app.models.verify_tmp_order import VerifyTmpOrder
from app.services import (
    admin_services,
    base_data_service,
    hood_service,
    public_services,
    schedule_services,
    verify_tmp_order_service,
)
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import ota_platform_schemas, public_schemas
from fastapi import status
from commons.fastapi.schemas import common_schemas
import commons.sdks as hy_sdks

routers = APIRouter(prefix=f'{settings.API_PERFIX}/public', tags=['对外开放的API接口'])


@routers.post("/search", summary="报价查询", response_model=public_schemas.SearchOut, response_model_exclude_none=True)
async def search(item: hy_sdks.flight_fare.search.FlightSearchRequest, client_ip: str = Depends(get_real_client_ip)):
    conditions = item.model_dump(exclude_none=True, exclude_unset=True, exclude=['request_id'])

    result = []
    try:
        # 标准报价
        normal_result = await public_services.search_simple(**conditions)
        logger.debug(f'normal_result: {normal_result}')
        # 压位报价
        hood_result = await public_services.search_hood_price(**conditions)
        logger.debug(f'hood_result: {hood_result}')
        # 合并
        result = public_services.merge_result(normal_result, hood_result)
        logger.debug(f'merge_result: {result}')
        # 扣减临时订单
        result = await verify_tmp_order_service.deduct_ticket_by_temp_order(result)
        # 追加基础数据
        # result = await public_services.append_base_data(result=result)
        # logger.debug(f'append_base_data: {result}')
    finally:
        result = public_services.fare_fuse(result)
    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/search/single", summary="报价查询", response_model=public_schemas.VerifyOut, response_model_exclude_none=True
)
async def search_single(
    request: Request, item: hy_sdks.flight_fare.verify.FlightVerifyRequest, client_ip: str = Depends(get_real_client_ip)
):
    """
    新通用验价，只做查询逻辑，比较交由上层服务完成
    """
    conditions = item.model_dump(exclude_none=True, exclude_unset=True, exclude=['request_id'])
    # fare_type = conditions.pop('fare_type')

    result = []
    try:
        fare_key_data = public_services.decode_fare_key(conditions['fare_key'])
        fare_type = fare_key_data['fare_type']
        logger.debug(f'fare_type: {fare_type}')

        if fare_type == FareType.NORMAL.value:
            logger.info('normal fare')
            result = await public_services.price_verify_normal(
                fare_key=conditions['fare_key'],
                dep_airport_code=conditions['dep_airport_code'],
                arr_airport_code=conditions['arr_airport_code'],
                use_cache=conditions['use_cache'],
                adult=conditions['adult'],
                child=conditions['child'],
                infant=conditions['infant'],
            )
        elif fare_type == FareType.PRE_ORDER.value:
            logger.info('pre order fare')
            # fpo_rs = await public_services.price_verify_fpo(
            #     fare_key=conditions['fare_key'],
            #     dep_airport_code=conditions['dep_airport_code'],
            #     arr_airport_code=conditions['arr_airport_code'],
            #     flight_no=conditions['flight_no'],
            #     request=request,
            # )
            # result = [fpo_rs['data']]
            hood_result = await public_services.verify_hood_price(
                fare_key=conditions['fare_key'],
                dep_airport_code=conditions['dep_airport_code'],
                arr_airport_code=conditions['arr_airport_code'],
                flight_no=conditions['flight_no'],
                adult=conditions['adult'],
                child=conditions['child'],
                infant=conditions['infant'],
            )
            result = [hr for hr in hood_result if hr['flight_info']['flight_no'] == conditions['flight_no']]
        result = await verify_tmp_order_service.deduct_ticket_by_temp_order(result)
        if not result:
            ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(ext_msg='扣除临时订单后无余票')

        # logger.debug(result)
        # result = await public_services.append_base_data(result=result)
        # logger.debug(result)
    finally:
        result = public_services.fare_fuse(result)

    if result:
        result = result[0]

    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    '/verify/order/create',
    summary='订单预订',
    response_model=public_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def verify_order_create(
    item: hy_sdks.flight_fare.verify.FlightVerifyOrderCreateRequest, client_ip: str = Depends(get_real_client_ip)
):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    result = None
    touch_search = False
    try:
        result = await public_services.verify_create_order(params=params)
    except Exception as e:
        touch_search = True
        # 如果预占座失败，则尝试实时验价
        # if e.args[0] == ApiCodes.FARE_VERIFY_STOP_BOOK.value:
        try:
            await public_services.verify_real_time(
                params={
                    'fare_key': params['fare_key'],
                    'dep_airport_code': params['dep_airport_code'],
                    'arr_airport_code': params['arr_airport_code'],
                    'flight_no': params['flight_no'],
                    'adult': params['adult'],
                    'child': params['child'],
                    'infant': params['infant'],
                    'keep_time': 1,
                },
                mock_pnr=params['mock_pnr'],
            )
        except Exception as e2:
            logger.error(e2)

        raise e
    finally:
        if not touch_search:
            try:
                await public_services.verify_real_time(
                    params={
                        'fare_key': params['fare_key'],
                        'dep_airport_code': params['dep_airport_code'],
                        'arr_airport_code': params['arr_airport_code'],
                        'flight_no': params['flight_no'],
                        'adult': 1,
                        'child': 0,
                        'infant': 0,
                        'keep_time': 1,
                    },
                    mock_pnr=params['mock_pnr'],
                )
            except Exception as e2:
                logger.error(e2)

    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/verify/order/get",
    summary="订单查询",
    response_model=public_schemas.FlightVerifyOrderOut,
    response_model_exclude_none=True,
)
async def verify_order_get(
    item: hy_sdks.flight_fare.public.FarePublicGetVerifyOrderRequest, client_ip: str = Depends(get_real_client_ip)
):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    if params.get('order_no'):
        tmp_order_row = await VerifyTmpOrder.get_by_async(
            VerifyTmpOrder.order_no == params['order_no'],
            VerifyTmpOrder.mock_pnr == params['mock_pnr'],
            order_by=[VerifyTmpOrder.id.desc()],
        )
    else:
        tmp_order_row = await VerifyTmpOrder.get_by_async(
            VerifyTmpOrder.mock_pnr == params['mock_pnr'], order_by=[VerifyTmpOrder.id.desc()]
        )
    order_info = orjson.loads(tmp_order_row['order_info'])
    fare_key_info = public_services.decode_fare_key(order_info['fare_key'])
    logger.debug(f'fare_key_info: {fare_key_info}')
    result = {
        "id": tmp_order_row['id'],
        "order_no": tmp_order_row['order_no'],
        "mock_pnr": tmp_order_row['mock_pnr'],
        "real_pnr": tmp_order_row['real_pnr'],
        "expire_time": tmp_order_row['sure_expire_time'],
        "scan_pnr": tmp_order_row['scan_pnr'],
        "book_result": orjson.loads(tmp_order_row['book_result']) if tmp_order_row['book_result'] else '',
        # "order_info": order_info,
        "src_currency": fare_key_info['src_currency'],
    }
    error_code = ApiCodes.UNKNOWN.value
    if fare_key_info['fare_type'] == FareType.PRE_ORDER.value:

        if await hood_service.check_hood_data(
            dep_airport_code=tmp_order_row['dep_airport_code'],
            arr_airport_code=tmp_order_row['arr_airport_code'],
            dep_date=tmp_order_row['dep_date'],
            flight_no=tmp_order_row['flight_no'],
            src_adult_base=order_info['src_adult_base'],
            src_adult_tax=order_info['src_adult_tax'],
            passenger_num=len(order_info['passengers']),
        ):
            # 压位记录存在，则验价成功
            if not result['real_pnr']:
                result['real_pnr'] = f'{FareType.PRE_ORDER.value}ID{fare_key_info["fare_id"]}'.upper()
            error_code = ApiCodes.SUCCESS.value
        else:
            # 压位记录全部过期时，则验价失败
            error_code = ApiCodes.FARE_VERIFY_PAY_TIMEOUT.value
    else:
        # 标准报价
        if tmp_order_row['real_pnr'] and tmp_order_row['sure_expire_time']:
            expire_time = datetime.strptime(tmp_order_row['sure_expire_time'], '%Y-%m-%d %H:%M:%S')
            if datetime.now() > expire_time:
                error_code = ApiCodes.FARE_VERIFY_PAY_TIMEOUT.value
            else:
                error_code = ApiCodes.SUCCESS.value
        elif tmp_order_row['task_status'] in [TaskStatus.PENDING.value, TaskStatus.RUNNING.value]:
            error_code = ApiCodes.TASK_RUNNING.value
        else:
            error_code = tmp_order_row['code']
            if error_code == -1:
                error_code = ApiCodes.UNKNOWN.value
    resp = ApiCodes(error_code).generate_api_result(data=result)
    return resp


@routers.post("/verify/order/finish", summary="订单完成", response_model=common_schemas.BaseApiOut)
async def verify_order_finish(
    item: hy_sdks.flight_fare.public.FarePublicFinishVerifyOrderRequest, client_ip: str = Depends(get_real_client_ip)
):
    # params = item.model_dump(exclude_none=True, exclude_unset=True)
    await VerifyTmpOrder.update_by_async(VerifyTmpOrder.mock_pnr == item.mock_pnr, is_finish=1)
    return ApiCodes.SUCCESS.generate_api_result(data=True)


@routers.post(
    "/verify/real_time", summary="实时验价", response_model=public_schemas.VerifyOut, response_model_exclude_none=True
)
async def verify_real_time(
    item: hy_sdks.flight_fare.verify.FlightRealTimeVerifyRequest, client_ip: str = Depends(get_real_client_ip)
):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    result = []
    try:
        result = await public_services.verify_real_time(params=params)
        logger.debug(f'verify_real_time: {result}')
        if not result:
            return ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(ext_msg='实时验价无结果')

        if item.mock_pnr:
            # 验价3才进行低余票过滤，验价1通过format时已对无法占座的航线进行了过滤
            # 如果是可占座航线，则表示可能占座失败，需要进行余票数过滤
            result = public_services.low_quantity_fuse(result=result, min_quantity=settings.MIN_TICKET_QUANTITY)

        logger.debug(f'low_quantity_fuse: {result}')
        if not result:
            return ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(
                ext_msg=f'实时验价余票低于{settings.MIN_TICKET_QUANTITY}张'
            )

        # 再进行临时订单扣减
        result = await verify_tmp_order_service.deduct_ticket_by_temp_order(result, mock_pnr=params.get('mock_pnr'))
        logger.debug(f'deduct_ticket_by_temp_order: {result}')
        if not result:
            return ApiCodes.FARE_VERIFY_NO_RESULT.raise_error(ext_msg='扣除临时订单后无余票')

        # logger.debug(result)
        # logger.debug(result)
    finally:
        result = public_services.fare_fuse(result)

    if result:
        result = result[0]
    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/ticket/channels",
    summary="查询出票渠道",
    response_model=public_schemas.GetTicketChannelsOut,
    response_model_exclude_none=True,
)
async def ticket_channels(
    item: hy_sdks.flight_fare.public.GetTicketChannelsRequest, client_ip: str = Depends(get_real_client_ip)
):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    result = await public_services.ticket_channels(params=params)
    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/airline/account/get",
    summary="获取航空公司账号",
    response_model=public_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def airline_account_get(
    item: hy_sdks.flight_fare.public.GetAirlineAccountRequest, client_ip: str = Depends(get_real_client_ip)
):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    conditions = [AirlineAccount.airline_code == params['airline_code']]

    if params.get('account_name'):
        conditions.append(AirlineAccount.username == params['account_name'])
    else:
        conditions.append(AirlineAccount.status == EnableStatus.ENABLED.value)
    result = await AirlineAccount.get_by_async(*conditions, order_by=[AirlineAccount.used_times.asc()])
    if result:
        await AirlineAccount.update_by_async(
            AirlineAccount.id == result['id'], used_times=AirlineAccount.used_times + 1
        )
    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/schedule/all", summary="获取所有航线", response_model=public_schemas.BaseApiOut, response_model_exclude_none=True
)
async def schedule_all(
    item: hy_sdks.flight_fare.public.GetAllScheduleRequest, client_ip: str = Depends(get_real_client_ip)
):
    result = await schedule_services.get_all_schedule(item.airline_code)
    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/exchange/rate/get",
    summary="获取汇率",
    response_model=public_schemas.GetExchangeRateOut,
    response_model_exclude_none=True,
)
async def exchange_rate_get(
    item: hy_sdks.flight_fare.public.FarePublicGetExchangeRateRequest, client_ip: str = Depends(get_real_client_ip)
):
    result = await public_services.get_exchange_rate(
        src_currency=item.src_currency,
        dst_currency=item.dst_currency,
        src_price=item.src_price,
        airline_code=item.airline_code,
    )
    return ApiCodes.SUCCESS.generate_api_result(data=result)
