from datetime import datetime, timedelta

from io import Bytes<PERSON>
from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Response, UploadFile
from openpyxl import load_workbook
import pandas as pd

from app.models.ota_platform import OTAPlatform

from app.services import admin_services
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import ota_platform_schemas
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/ota_platform', tags=['OTA投放平台管理接口'])


@routers.post(
    "/add",
    summary="新增OTA投放平台",
    response_model=ota_platform_schemas.OtaPlatformOut,
    response_model_exclude_none=True,
)
async def add(
    item: ota_platform_schemas.OtaPlatformBase,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):

    new_row = item.model_dump(exclude_none=True, exclude_unset=True)
    logger.debug(new_row)
    ota_platform_row = await OTAPlatform.create_at_async(**new_row)
    return ApiCodes.SUCCESS.generate_api_result(data=ota_platform_row)


@routers.post(
    "/update",
    summary="编辑OTA投放平台",
    response_model=ota_platform_schemas.OtaPlatformOut,
    response_model_exclude_none=True,
)
async def update(
    item: ota_platform_schemas.UpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):

    update_row = item.model_dump(exclude_none=True, exclude_unset=True)
    logger.debug(update_row)
    if len(update_row.keys()) == 1:
        raise HTTPException(status_code=400, detail="更新数据不能为空")

    await OTAPlatform.update_by_async(OTAPlatform.id == item.id, **update_row)
    ota_platform_row = await OTAPlatform.get_by_async(OTAPlatform.id == item.id)
    return ApiCodes.SUCCESS.generate_api_result(data=ota_platform_row)


@routers.post(
    "/search",
    summary="查询OTA投放平台",
    response_model=ota_platform_schemas.SearchOut,
    response_model_exclude_none=True,
)
async def search(
    item: ota_platform_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        conditions = []
        if item.conditions.ota_platform_id:
            conditions.append(OTAPlatform.id == item.conditions.ota_platform_id)

        if item.conditions.status:
            conditions.append(OTAPlatform.status == item.conditions.status)
        total = await OTAPlatform.count_async(*conditions)
        page = item.page if item.page else 1
        page_size = item.page_size if item.page_size else 10
        offset = (page - 1) * page_size
        results = await OTAPlatform.get_all_async(
            *conditions, limit=page_size, offset=offset, order_by=[OTAPlatform.id.desc()]
        )
        return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
    except Exception as e:
        logger.exception("查询OTA投放平台异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/list", summary="OTA投放平台列表", response_model=ota_platform_schemas.ListOut, response_model_exclude_none=True
)
async def list(item: ota_platform_schemas.ListIn, current_admin: dict = Depends(admin_services.get_current_admin)):

    results = await OTAPlatform.get_all_async(OTAPlatform.status == EnableStatus.ENABLED.value)
    return ApiCodes.SUCCESS.generate_api_result(data=results)
