def register_routes(app):
    # 注意在方法内部引入需要的模块
    # 避免引发循环导入

    from . import admin_views
    from . import airline_views
    from . import admin_role_views
    from . import schedule_views
    from . import fetch_rule_views
    from . import ota_platform_views
    from . import fare_rule_views
    from . import crawler_callback_views
    from . import public_views
    from . import cache_views
    from . import base_data_views
    from . import verify_tmp_order_views
    from . import hood_data_views

    app.include_router(admin_views.routers)
    app.include_router(airline_views.routers)
    app.include_router(schedule_views.routers)
    app.include_router(fetch_rule_views.routers)
    app.include_router(admin_role_views.routers)
    app.include_router(ota_platform_views.routers)
    app.include_router(fare_rule_views.routers)
    app.include_router(crawler_callback_views.routers)
    app.include_router(public_views.routers)
    app.include_router(cache_views.routers)
    app.include_router(base_data_views.routers)
    app.include_router(verify_tmp_order_views.routers)
    app.include_router(hood_data_views.routers)
