from datetime import datetime, timedelta
from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2P<PERSON>wordRequestForm
import or<PERSON><PERSON>
from sqlalchemy import or_

from app.models.admin import Admin
from app.models.admin_role import AdminRole
from app.models.operate_log import OperateLog
from app.services import admin_services
from app.views.schemas import admin_schemas
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus, SuccessStatus
from commons.consts.common_types import YesOrNo
from commons.depends import get_real_client_ip
from app.config import settings
from fastapi import status

from commons.sdks import flight_fare

routers = APIRouter(prefix=f'{settings.API_PERFIX}/admin', tags=['管理账号接口'])


@routers.post("/login/token", summary="登录接口")
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()], client_ip: str = Depends(get_real_client_ip)
):
    logger.debug(form_data)
    admin_row = await admin_services.authenticate_admin(username=form_data.username, password=form_data.password)
    access_token = admin_services.create_access_token(
        data={"id": admin_row['id'], "username": admin_row['username'], "username_desc": admin_row['username_desc']},
        expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES),
    )
    await Admin.update_by_async(
        Admin.id == admin_row['id'],
        latest_login_ip=client_ip,
        latest_login_time=datetime.now(),
        access_token=access_token,
    )
    # return ApiCodes.SUCCESS.generate_api_result(data={"access_token": access_token, "token_type": "bearer"})
    return {"access_token": access_token, "token_type": "bearer"}


@routers.post("/login/info", summary="登录用户信息")
async def login_info(current_admin: dict = Depends(admin_services.get_current_admin)):
    current_admin.pop("password")
    current_admin.pop("access_token")

    return current_admin


@routers.post("/logout", summary="退出登录")
async def logout(current_admin: dict = Depends(admin_services.get_current_admin)):
    await Admin.update_by_async(Admin.id == current_admin['id'], access_token='')
    return '退出成功'


@routers.post("/add", summary="添加管理员", response_model=admin_schemas.AdminOut, response_model_exclude_none=True)
async def add(item: admin_schemas.CreateIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    admin_row = await Admin.get_by_async(Admin.username == item.username)

    if admin_row:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"用户 {item.username} 已存在")
    if item.password != item.confirm_password:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="两次密码不一致")
    new_row = admin_services.request_pre_process(item)

    admin_row = await admin_services.create_admin(**new_row)

    admin_row = admin_services.response_pre_process(admin_row)

    return ApiCodes.SUCCESS.generate_api_result(data=admin_row)


@routers.post("/update", summary="更新管理员", response_model=admin_schemas.AdminOut, response_model_exclude_none=True)
async def update(item: admin_schemas.UpdateIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    update_row = admin_services.request_pre_process(item)

    await Admin.update_by_async(Admin.id == item.id, **update_row)

    admin_row = await Admin.get_by_async(Admin.id == item.id)
    admin_row = admin_services.response_pre_process(admin_row)

    return ApiCodes.SUCCESS.generate_api_result(data=admin_row)


@routers.post(
    "/password/update", summary="更新密码", response_model=admin_schemas.BaseApiOut, response_model_exclude_none=True
)
async def update_password(
    item: admin_schemas.UpdatePasswordIn, current_admin: dict = Depends(admin_services.get_current_admin)
):
    if current_admin['id'] != item.id:
        raise HTTPException(status_code=403, detail="无操作权限")

    from app.services.admin_services import pwd_context

    admin_row = await Admin.get_by_async(Admin.id == item.id)
    logger.debug(pwd_context.verify(item.old_password, admin_row['password']))
    if not pwd_context.verify(item.old_password, admin_row['password']):
        raise HTTPException(status_code=400, detail="原密码不正确")
    if item.new_password == item.old_password:
        raise HTTPException(status_code=400, detail="新密码不能与原密码相同")
    if item.new_password != item.confirm_password:
        raise HTTPException(status_code=400, detail="两次密码不一致")

    await Admin.update_by_async(Admin.id == item.id, password=pwd_context.hash(item.new_password))

    return ApiCodes.SUCCESS.generate_api_result(data='密码更新成功')


@routers.post(
    "/password/reset", summary="重置密码", response_model=admin_schemas.BaseApiOut, response_model_exclude_none=True
)
async def reset_password(
    item: admin_schemas.ResetPasswordIn, current_admin: dict = Depends(admin_services.get_current_admin)
):
    if current_admin['is_super'] == YesOrNo.NO.value and 'ADMIN_RESET' not in current_admin['privileges']:
        raise HTTPException(status_code=403, detail="无操作权限")
    if not item.new_password:
        raise HTTPException(status_code=400, detail="新密码不能为空")
    if item.new_password != item.confirm_password:
        raise HTTPException(status_code=400, detail="两次密码不一致")

    from app.services.admin_services import pwd_context

    await Admin.update_by_async(Admin.id == item.id, password=pwd_context.hash(item.new_password), access_token='')

    return ApiCodes.SUCCESS.generate_api_result(data='密码重置成功')


@routers.post("/search", summary="搜索管理员", response_model=admin_schemas.SearchOut, response_model_exclude_none=True)
async def search(item: admin_schemas.SearchIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    conditions = []
    if item.conditions:
        if item.conditions.keyword:
            conditions.append(
                or_(
                    Admin.username.like(f'%{item.conditions.keyword}%'),
                    Admin.username_desc.like(f'%{item.conditions.keyword}%'),
                )
            )
        if item.conditions.status:
            conditions.append(Admin.status == item.conditions.status)

    total = await Admin.count_async(*conditions)
    page = item.page if item.page else 1
    page_size = item.page_size if item.page_size else 10
    offset = (page - 1) * page_size
    results = await Admin.get_all_async(*conditions, limit=page_size, offset=offset, order_by=[Admin.id.desc()])
    for result in results:
        result = admin_services.response_pre_process(result)

    return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})


@routers.post(
    "/auth/password", summary="验证密码", response_model=admin_schemas.BaseApiOut, response_model_exclude_none=True
)
async def auth_password(
    item: flight_fare.auth_by_password.AuthByPasswordRequest,
    current_admin: dict = Depends(admin_services.get_current_admin),
):
    admin_row = await admin_services.authenticate_admin(username=current_admin['username'], password=item.password)

    return ApiCodes.SUCCESS.generate_api_result(data=True)
