import copy
from datetime import datetime, timedelta

from io import BytesIO
import re
from typing_extensions import Annotated
from app.consts.status import ScanTaskStatus
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Request, Response, UploadFile
from openpyxl import load_workbook
import orjson
import pandas as pd
from sqlalchemy import and_, or_

from app.consts.types import UpdateModeCode
from app.models.fare_rule import FareRule
from app.models.fare_rule_add_on import FareRuleAddOn
from app.models.fare_rule_baggage import FareRuleBaggage
from app.models.fare_rule_op_log import FareRuleOperateLog
from app.models.operate_log import OperateLog
from app.models.verify_tmp_order import VerifyTmpOrder
from app.services import (
    admin_services,
    base_data_service,
    excel_services,
    fare_rule_services,
    public_services,
    schedule_services,
    verify_tmp_order_service,
)
from app.services.operate_log_services import FareRuleOpLogServices
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus, SuccessStatus
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import fare_rule_schemas, verify_tmp_order_schemas
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/verify_tmp_order', tags=['投放策略管理接口'])

OPERATION_TAG = "verify_tmp_order"


@routers.post(
    "/search",
    summary="查询投放策略",
    response_model=verify_tmp_order_schemas.SearchOut,
    response_model_exclude_none=True,
)
async def search(
    item: verify_tmp_order_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        conditions = []
        if item.conditions.mock_pnr:
            conditions.append(VerifyTmpOrder.mock_pnr == item.conditions.mock_pnr)

        if item.conditions.task_status:
            conditions.append(VerifyTmpOrder.task_status == item.conditions.task_status)

        # if item.conditions.flight_date:
        #     conditions.append(VerifyTmpOrder.dep_date == item.conditions.flight_date)
        if item.conditions.order_date:
            if isinstance(item.conditions.order_date, list):
                conditions.append(VerifyTmpOrder.created >= f'{item.conditions.order_date[0]} 00:00:00')
                conditions.append(VerifyTmpOrder.created <= f'{item.conditions.order_date[1]} 23:59:59')
            else:
                conditions.append(VerifyTmpOrder.created >= f'{item.conditions.order_date} 00:00:00')

        if item.conditions.flight_no:
            conditions.append(VerifyTmpOrder.flight_no == item.conditions.flight_no)

        if item.conditions.dep_airport_code:
            conditions.append(VerifyTmpOrder.dep_airport_code == item.conditions.dep_airport_code)

        if item.conditions.arr_airport_code:
            conditions.append(VerifyTmpOrder.arr_airport_code == item.conditions.arr_airport_code)

        if not conditions:
            conditions.append(VerifyTmpOrder.created > (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'))

        total = await VerifyTmpOrder.count_async(*conditions)
        page = item.page if item.page else 1
        page_size = item.page_size if item.page_size else 10
        offset = (page - 1) * page_size
        results = await VerifyTmpOrder.get_all_async(
            *conditions, limit=page_size, offset=offset, order_by=[VerifyTmpOrder.created.desc()]
        )
        for result in results:
            book_result = None
            if result['real_pnr'] and result['book_result']:
                book_result = orjson.loads(result['book_result'])
            result['book_result'] = book_result
            result['order_info'] = orjson.loads(result['order_info'])
            fare_key_data = public_services.decode_fare_key(result['order_info']['fare_key'])
            result['order_info']['src_currency'] = fare_key_data['src_currency']
            if result['scan_book_result']:
                scan_book_result = orjson.loads(result['scan_book_result'])
                result['scan_book_result'] = scan_book_result
            if result['pay_result']:
                pay_result = orjson.loads(result['pay_result'])
                result['pay_result'] = pay_result
            # 补丁
            if (
                not result['flight_no']
                or not result['dep_date']
                or not result['dep_airport_code']
                or not result['arr_airport_code']
            ):
                await VerifyTmpOrder.update_by_async(
                    VerifyTmpOrder.id == result['id'],
                    flight_no=result['order_info']['flight_no'],
                    dep_date=datetime.strptime(result['order_info']['dep_time'][:8], '%Y%m%d').strftime('%Y-%m-%d'),
                    dep_airport_code=result['order_info']['dep_airport_code'],
                    arr_airport_code=result['order_info']['arr_airport_code'],
                )
        logger.debug(results)
        return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
    except Exception as e:
        logger.exception("查询抓取策略异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/scan/turnon",
    summary="扫描出票开启",
    response_model=verify_tmp_order_schemas.ScanTurnOut,
    response_model_exclude_none=True,
)
async def scan_turnon(
    item: verify_tmp_order_schemas.ScanTurnOn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        order_no = item.order_no
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.order_no == order_no, scan_status=ScanTaskStatus.PREPARE.value
        )
        return ApiCodes.SUCCESS.generate_api_result(data="更新成功")
    except Exception as e:
        logger.exception("查询抓取策略异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/scan/turnoff",
    summary="扫描出票关闭",
    response_model=verify_tmp_order_schemas.ScanTurnOut,
    response_model_exclude_none=True,
)
async def scan_turnoff(
    item: verify_tmp_order_schemas.ScanTurnOff,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        order_no = item.order_no
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.order_no == order_no, scan_status=ScanTaskStatus.CLOSED.value
        )
        return ApiCodes.SUCCESS.generate_api_result(data="更新成功")
    except Exception as e:
        logger.exception("查询抓取策略异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/direct_pay",
    summary="自动出票",
    response_model=verify_tmp_order_schemas.ScanTurnOut,
    response_model_exclude_none=True,
)
async def direct_pay(
    item: verify_tmp_order_schemas.DirectPay,
    request: Request,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        order_no = item.order_no
        base_float = item.base_float
        await verify_tmp_order_service.direct_pay(order_no, base_float, request_id=request.state.request_id)

        return ApiCodes.SUCCESS.generate_api_result(data="自动出票发送成功")
    except Exception as e:
        logger.exception("自动出票异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/confirm_pay",
    summary="预占座支付",
    response_model=verify_tmp_order_schemas.ScanTurnOut,
    response_model_exclude_none=True,
)
async def confirm_pay(
    item: verify_tmp_order_schemas.DirectPay,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        order_no = item.order_no
        await verify_tmp_order_service.confirm_pay(order_no)
        return ApiCodes.SUCCESS.generate_api_result(data="预占座支付推送成功")
    except Exception as e:
        logger.exception("预占座支付推送异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
