from datetime import datetime, timed<PERSON>ta

from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Response, UploadFile


from app.config import settings
from app.services import admin_services, excel_services, flight_base_services
from app.services.mongo_services import CommonMongoService, FlightFareMongoService
from app.views.schemas import airline_schemas, base_data_schemas
from fastapi import status

from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip

routers = APIRouter(prefix=f'{settings.API_PERFIX}/base_data', tags=['航班基础数据管理接口'])


@routers.post("/flight/tpl/download", summary="下载模板")
async def download_flight_base_data_template(
    current_admin: dict = Depends(admin_services.get_current_admin), client_ip: str = Depends(get_real_client_ip)
):
    empty_dates = {
        '航司二字码': ['ZE'],
        '航班号': ['ZE1234'],
        '出发机场': ['PEK'],
        '到达机场': ['SHA'],
        '起飞时刻': ['07:00'],
        '到达时刻': ['13:00'],
        '是否共享航班': ['N或Y'],
        '生效日期': [datetime.now().strftime('%Y-%m-%d')],
        '失效日期': [datetime.now().strftime('%Y-%m-%d')],
        '机型': ['737'],
        '经停信息': ['经停机场三字码;经停机场中文名;降落时刻;起飞时刻,CAN;广州白云机场;10:10;10:30'],
        '班期': ['1234567'],
        '跨天': ['0'],
    }
    output = await excel_services.get_template(empty_dates=empty_dates)
    # 设置响应头
    headers = {
        'Content-Disposition': 'attachment; filename="output.xlsx"',
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }

    return Response(
        content=output.getvalue(),
        headers=headers,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    )


@routers.post('/flight/upload', response_model=base_data_schemas.BaseApiOut)
async def upload_flight_base_data(
    file: UploadFile = File(...),
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    '''导入航班基础数据'''
    try:
        df = await excel_services.upload_to_dataframe(file)
        flight_base_rows = await flight_base_services.dataframe_to_records(df)

        mongo_service = CommonMongoService(db_name='base_data', collection_name='flight_info')
        await mongo_service.bulk_insert_or_update(
            datas=flight_base_rows,
            unique_keys=[
                'airline_code',
                'flight_no',
                'dep_airport_code',
                'arr_airport_code',
                # 'dep_time',
                # 'arr_time',
                'end_date',
            ],
        )
        logger.debug(flight_base_rows)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return ApiCodes.SUCCESS.generate_api_result(data='上传成功')


@routers.post('/flight/search', response_model=base_data_schemas.BaseApiOut)
async def search_flight_base_data(
    item: base_data_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    '''搜索航班基础数据'''
    mongo_service = CommonMongoService(db_name='base_data', collection_name='flight_info')
    offset = (item.page - 1) * item.page_size
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    query = {}
    if params.get('conditions', {}).get('airline_code'):
        query['airline_code'] = params['conditions']['airline_code'].upper()
    if params.get('conditions', {}).get('dep_airport_code'):
        query['dep_airport_code'] = params['conditions']['dep_airport_code'].upper()
    if params.get('conditions', {}).get('arr_airport_code'):
        query['arr_airport_code'] = params['conditions']['arr_airport_code'].upper()

    total, rows = await mongo_service.search(query=query, limit=item.page_size, offset=offset)
    for row in rows:
        row['id'] = str(row.pop('_id'))
    result = {'total': total, 'rows': rows}

    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post('/flight/delete/batch', response_model=airline_schemas.BaseApiOut)
async def delete_flight_base_data(item: base_data_schemas.IdListIn):
    mongo_service = CommonMongoService(db_name='base_data', collection_name='flight_info')
    await mongo_service.delete_by_ids(item.ids)
    return ApiCodes.SUCCESS.generate_api_result(data='删除成功')


@routers.post('/flight/update', response_model=airline_schemas.BaseApiOut)
async def update_flight_base_data(item: base_data_schemas.UpdateIn):
    mongo_service = CommonMongoService(db_name='base_data', collection_name='flight_info')
    update_row = item.model_dump(exclude_none=True, exclude_unset=True)
    id = update_row.pop('id')
    await mongo_service.update_by_id(id=id, update=update_row)
    return ApiCodes.SUCCESS.generate_api_result(data='更新成功')
