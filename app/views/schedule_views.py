from datetime import datetime, timed<PERSON>ta

from io import Bytes<PERSON>
from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Response, UploadFile
from openpyxl import load_workbook
import pandas as pd

from app.models.schedule import Schedule
from app.services import admin_services, excel_services, schedule_services
from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import schedule_schemas
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/schedule', tags=['时刻表管理接口'])


@routers.post(
    "/upload",
    summary="批量添加",
    response_model=schedule_schemas.BatchCreateOrUpdateOut,
    response_model_exclude_none=True,
)
async def upload(
    file: UploadFile = File(...),
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        df = await excel_services.upload_to_dataframe(file)
        records = await schedule_services.dataframe_to_records(df)
        logger.debug(records)
        await Schedule.bulk_insert_or_update(
            records=records,
            update_keys=['dep_airport_code', 'arr_airport_code', 'airline_code', 'start_date', 'end_date'],
        )
        return ApiCodes.SUCCESS.generate_api_result(data=len(records))
    except Exception as e:
        logger.exception("批量创建/更新时刻表异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post("/tpl/download", summary="下载模板")
async def tpl_download(
    current_admin: dict = Depends(admin_services.get_current_admin), client_ip: str = Depends(get_real_client_ip)
):
    empty_dates = {
        '航司': ['CA'],
        '生效日期': [datetime.now().strftime('%Y-%m-%d')],
        '失效日期': [datetime.now().strftime('%Y-%m-%d')],
        '出发机场': ['PEK'],
        '到达机场': ['SHA'],
        '班期': ['1234567'],
    }
    output = await excel_services.get_template(empty_dates=empty_dates)
    # 设置响应头
    headers = {
        'Content-Disposition': 'attachment; filename="output.xlsx"',
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }

    return Response(
        content=output.getvalue(),
        headers=headers,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    )


@routers.post("/update", summary="", response_model=schedule_schemas.UpdateOut, response_model_exclude_none=True)
async def update(
    item: schedule_schemas.UpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        new_row = item.model_dump(exclude_none=True, exclude_unset=True)
        logger.debug(new_row)
        if len(new_row.keys()) == 1:
            raise HTTPException(status_code=400, detail="更新数据不能为空")
        await Schedule.update_by_async(Schedule.id == item.id, **item.model_dump(exclude_none=True, exclude_unset=True))
        schedule_row = await Schedule.get_by_async(Schedule.id == item.id)
        return ApiCodes.SUCCESS.generate_api_result(data=schedule_row)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.exception("更新航司异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/search", summary="时刻表查询接口", response_model=schedule_schemas.SearchOut, response_model_exclude_none=True
)
async def search(
    item: schedule_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        conditions = []
        if item.conditions.dep_airport_code:
            conditions.append(Schedule.dep_airport_code == item.conditions.dep_airport_code)

        if item.conditions.airline_code:
            conditions.append(Schedule.airline_code == item.conditions.airline_code)
        if item.conditions.arr_airport_code:
            conditions.append(Schedule.arr_airport_code == item.conditions.arr_airport_code)
        if item.conditions.schedules:
            conditions.append(Schedule.schedules.like(f'%{item.conditions.schedules}%'))
        total = await Schedule.count_async(*conditions)
        page = item.page if item.page else 1
        page_size = item.page_size if item.page_size else 10
        offset = (page - 1) * page_size
        results = await Schedule.get_all_async(
            *conditions, limit=page_size, offset=offset, order_by=[Schedule.id.desc()]
        )
        return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
    except Exception as e:
        logger.exception("航司查询异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/delete/batch", summary="删除时刻表", response_model=schedule_schemas.BaseApiOut, response_model_exclude_none=True
)
async def delete_batch(
    item: schedule_schemas.DeleteIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        await Schedule.delete_all_async(Schedule.id.in_(item.ids))
        return ApiCodes.SUCCESS.generate_api_result(data=True)
    except Exception as e:
        logger.exception("删除时刻表异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post("/export", summary="导出时刻表")
async def export(
    item: schedule_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    conditions = []
    if item.conditions.dep_airport_code:
        conditions.append(Schedule.dep_airport_code == item.conditions.dep_airport_code)

    if item.conditions.airline_code:
        conditions.append(Schedule.airline_code == item.conditions.airline_code)
    if item.conditions.arr_airport_code:
        conditions.append(Schedule.arr_airport_code == item.conditions.arr_airport_code)
    if item.conditions.schedules:
        conditions.append(Schedule.schedules.like(f'%{item.conditions.schedules}%'))
    results = await Schedule.get_all_async(*conditions)
    result_dates = {'航司': [], '生效日期': [], '失效日期': [], '出发机场': [], '到达机场': [], '班期': []}
    for row in results:
        result_dates['航司'].append(row['airline_code'])
        result_dates['生效日期'].append(str(row['start_date']))
        result_dates['失效日期'].append(str(row['end_date']))
        result_dates['出发机场'].append(row['dep_airport_code'])
        result_dates['到达机场'].append(row['arr_airport_code'])
        result_dates['班期'].append(row['schedules'])
    output = await excel_services.get_template(empty_dates=result_dates)
    # 设置响应头
    headers = {
        'Content-Disposition': 'attachment; filename="output.xlsx"',
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }

    return Response(
        content=output.getvalue(),
        headers=headers,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    )


@routers.post("/detail", summary="时刻表详情")
async def detail(
    item: schedule_schemas.DetailIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    schedule_row = await Schedule.get_by_async(Schedule.id == item.id)
    return ApiCodes.SUCCESS.generate_api_result(data=schedule_row)
