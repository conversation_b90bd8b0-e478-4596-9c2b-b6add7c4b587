import copy
from datetime import datetime, timed<PERSON>ta
from loguru import logger
from fastapi import APIRouter, Depends
import or<PERSON><PERSON>

from app.consts.status import ScanTaskStatus, TaskStatus
from app.models.verify_task import VerifyTask
from app.models.verify_tmp_order import VerifyTmpOrder
from app.services import (
    crawler_callback_services,
    es_service,
    fetch_rule_services,
    msg_service,
    public_services,
    task_services,
    cache_services,
)
from app.services.mongo_services import FlightFareMongoService
from commons.consts.api_codes import ApiCodes
from commons.consts.flight.resource_site import FlightSiteType
from commons.depends import get_real_client_ip
from commons.fastapi.schemas import common_schemas
from app.config import settings
from app.views.schemas import crawler_callback_schemas
from app.extensions.db_extras import mongo_client
from commons import sdks as hy_sdks

routers = APIRouter(prefix=f'{settings.API_PERFIX}/crawler/callback', tags=['爬虫回调接口'])


@routers.post(
    "/search/result", summary="爬虫搜索结果", response_model=common_schemas.BaseApiOut, response_model_exclude_none=True
)
async def search_result(item: crawler_callback_schemas.SearchCallbackIn, client_ip: str = Depends(get_real_client_ip)):
    search_result = item.model_dump()
    task_info = search_result['task_info']
    task_key = task_info['task_key']

    real_expire_seconds = task_info['expire_seconds']

    error_code = search_result.get("error", {}).get("code")
    if (isinstance(error_code, str) and error_code.upper() == "SUCCESS") or (
        isinstance(error_code, int) and error_code == ApiCodes.SUCCESS.value
    ):
        fetch_rule_row = await fetch_rule_services.get_rule(
            airline_code=task_info['airline_code'],
            dep_airport_code=task_info['dep_airport_code'],
            arr_airport_code=task_info['arr_airport_code'],
            dep_date=task_info['dep_date'],
        )

        # 初始化mongo
        flight_fare_mongo = FlightFareMongoService('flight_fare')

        collection_name = 'airline_fare_cache'
        if task_info['site_type'] == FlightSiteType.OTA.value:
            collection_name = f'{task_info["site_code"]}_fare_cache'
        # 检查索引

        await flight_fare_mongo.check_index(
            collection_name=collection_name,
            index_name='fare_delete_index',
            index_fields=[
                ('task_info.site_code', 1),
                ('task_info.dep_airport_code', 1),
                ('task_info.arr_airport_code', 1),
                ('task_info.dep_date', 1),
                ('task_info.return_date', 1),
            ],
        )

        valid_flight_nos = []
        # 插入新数据
        if search_result['data']['results']:

            # 按航段分割数据
            # 一组数据的过期时间是一样的
            # search_result['task_info']['expire_time'] = expire_time.strftime("%Y-%m-%d %H:%M:%S")
            logger.debug(search_result)
            # 汇率转换改成分割之前操作
            search_result = await crawler_callback_services.parse_exchange(search_result=search_result)
            search_result = crawler_callback_services.cache_fuse(search_result=search_result)
            es_service.write_price_log(search_result=search_result)
            datas = await crawler_callback_services.split_by_segment(data=search_result)
            if datas:
                for data in datas:
                    valid_flight_nos.append(data['result']['flight_no'])
                    data = await crawler_callback_services.check_low_quantity(splited_data=data)

                    data, tmp_expire_secords = crawler_callback_services.coumpute_expire_time(
                        split_data=data, fetch_rule_row=fetch_rule_row
                    )
                    if tmp_expire_secords < real_expire_seconds:
                        real_expire_seconds = tmp_expire_secords
                        if real_expire_seconds < 1:
                            real_expire_seconds = 1
                logger.debug(datas)

                await flight_fare_mongo.bulk_update_cache(datas=datas, collection_name=collection_name)
        # 删除缓存
        await flight_fare_mongo.delete_cache(
            collection_name=collection_name,
            site_code=task_info['site_code'],
            dep_airport_code=task_info['dep_airport_code'],
            arr_airport_code=task_info['arr_airport_code'],
            dep_date=task_info['dep_date'],
            return_date=task_info['return_date'],
            flight_nos=valid_flight_nos,
        )

        # 这里需要用expire_seconds作为缓存value，避免影响推送脚本中的优先级判断
        await task_services.set_cache_expire(
            task_key=task_key, value=task_info['expire_seconds'], expire=real_expire_seconds
        )
        logger.info(f'缓存最短过期时间为{real_expire_seconds} 秒')
        await crawler_callback_services.apply_shopping_push(search_result=search_result)
    else:
        # 失败删除task锁
        await task_services.del_task_key(task_key=task_key)
    try:
        crawler_start_time = datetime.strptime(task_info['start_time'], '%Y-%m-%dT%H:%M:%S')
        crawler_end_time = datetime.strptime(task_info['end_time'], '%Y-%m-%dT%H:%M:%S')
        task_create_time = datetime.strptime(task_info['create_time'], '%Y-%m-%dT%H:%M:%S')
        error_message = search_result.get('error', {}).get('message', '')
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name="crawler_search_result",
            unique_id=task_info['unique_id'],
            cost_time=(crawler_end_time - crawler_start_time).total_seconds(),
            task_wait_time=(crawler_start_time - task_create_time).total_seconds(),
            status='success' if ApiCodes.SUCCESS.value == error_code else 'failed',
            code=error_code,
            message=error_message,
            dep_airport_code=task_info['dep_airport_code'],
            arr_airport_code=task_info['arr_airport_code'],
            dep_date=task_info['dep_date'],
            airline_code=task_info['airline_code'],
            flights=len(search_result['data']['results']) if search_result['data']['results'] else 0,
        )
    except Exception as e:
        logger.exception(f'爬虫搜索结果ES日志输出失败：{e}')
    return ApiCodes.SUCCESS.generate_api_result(data='')


@routers.post("/verify/book/result", summary="爬虫订座结果", response_model=common_schemas.BaseApiOut)
async def verify_book_result(
    item: crawler_callback_schemas.VerifyBookResultIn, client_ip: str = Depends(get_real_client_ip)
):
    order_no = item.task_info['order_no']
    mock_pnr = item.task_info['mock_pnr']
    unique_id = item.task_info['unique_id']
    sure_expire_time = None
    err_msg = item.error.message
    session_id = ''
    flight_info_str = ''
    try:
        if item.error.code == ApiCodes.SUCCESS.value and item.data:
            # 如果实际过期时间大于30分钟，按30分钟计算
            # 否则将实际过期时间缩短一半
            dep_expire_time = item.data['book'].get('dep_expire_time')
            expire_time = item.data['book'].get('expire_time')
            sure_expire_time = item.data['book'].get('expire_time')
            # 自动出票也会调用这里，先临时修改，后期订单接入后将接口分开
            if sure_expire_time:
                sure_expire_minutes = (
                    datetime.strptime(sure_expire_time, "%Y-%m-%d %H:%M:%S") - datetime.now()
                ).seconds / 60
                if int(sure_expire_minutes) < 60:  # 60分钟以下，缩短一半
                    sure_expire_minutes = int(sure_expire_minutes) / 2
                else:  # 60分钟以上，减30分钟
                    sure_expire_minutes -= 30
                sure_expire_time = (datetime.now() + timedelta(minutes=sure_expire_minutes)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
            else:
                dep_expire_time = (datetime.now() + timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M:%S")
                expire_time = dep_expire_time
                sure_expire_time = dep_expire_time
            await VerifyTmpOrder.update_by_async(
                VerifyTmpOrder.order_no == order_no,
                VerifyTmpOrder.mock_pnr == mock_pnr,
                task_status=TaskStatus.SUCCESS.value,
                book_result=orjson.dumps(item.data).decode('utf-8'),
                real_pnr=item.data['book']['pnr'],
                dep_expire_time=dep_expire_time,
                expire_time=expire_time,
                sure_expire_time=sure_expire_time,
                code=item.error.code,
                message=item.error.message,
                scan_status=ScanTaskStatus.INIT.value,  # 占座成功之后
            )

        else:
            await VerifyTmpOrder.update_by_async(
                VerifyTmpOrder.order_no == order_no,
                VerifyTmpOrder.mock_pnr == mock_pnr,
                VerifyTmpOrder.task_status != TaskStatus.SUCCESS.value,
                task_status=TaskStatus.FAILED.value,
                book_result=orjson.dumps(item.data).decode('utf-8'),
                code=item.error.code,
                message=item.error.message,
            )
            # 使用抽象函数清除缓存
            await cache_services.clear_cache_for_error(task_info=item.task_info, error_code=item.error.code)

        verify_tmp_order_row = await VerifyTmpOrder.get_by_async(
            VerifyTmpOrder.order_no == order_no, VerifyTmpOrder.mock_pnr == mock_pnr
        )
        if verify_tmp_order_row:
            order_info = orjson.loads(verify_tmp_order_row['order_info'])
            session_id = order_info.get('session_id', '')
            fare_key_data = public_services.decode_fare_key(order_info['fare_key'])
            flight_info_str = (
                ' '.join(
                    [
                        order_info.get('dep_airport_code', ''),
                        order_info.get('arr_airport_code', ''),
                        fare_key_data.get('dep_date', ''),
                        order_info.get('flight_no', ''),
                        fare_key_data.get('src_currency', ''),
                    ]
                )
            ).strip()

    except Exception as e:
        logger.exception(e)
        err_msg += f'，{str(e)}'
    finally:
        # real_pnr = ''
        # if item.data:
        #     book_data = item.data.get('book')
        #     if book_data and isinstance(book_data, dict):
        #         real_pnr = book_data.get('pnr', '')
        # await msg_service.verify_order_alert(
        #     mock_pnr=mock_pnr,
        #     code=item.error.code,
        #     message=err_msg,
        #     real_pnr=real_pnr,
        #     sure_expire_time=sure_expire_time,
        #     session_id=session_id,
        #     unique_id=unique_id,
        #     flight_info_str=flight_info_str,
        # )
        pass

    return ApiCodes.SUCCESS.generate_api_result(data='')


@routers.post("/verify/result", summary="爬虫验价结果", response_model=common_schemas.BaseApiOut)
async def verify_result(item: crawler_callback_schemas.VerifyResultIn, client_ip: str = Depends(get_real_client_ip)):
    verify_result = item.model_dump(exclude_none=True, exclude_unset=True)
    if verify_result.get('error', {}).get('code') == ApiCodes.SUCCESS.value:
        verify_result = await crawler_callback_services.parse_exchange(search_result=verify_result)
        await VerifyTask.update_by_async(
            VerifyTask.id == verify_result['task_info']['verify_task_id'],
            task_status=TaskStatus.SUCCESS.value,
            code=verify_result['error']['code'],
            verify_result=orjson.dumps(verify_result).decode('utf-8'),
            message=verify_result['error']['message'],
            callback_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        )
        try:
            # 确保task_key存在
            

            # 使用抽象函数更新缓存
            collection_name = 'airline_fare_cache'
            await cache_services.update_cache_from_verify_result(
                search_result=verify_result, collection_name=collection_name
            )
        except Exception as e:
            logger.exception(e)
    else:
        # 查询失败后，重新查询
        verify_task_id = verify_result['task_info']['verify_task_id']
        await VerifyTask.update_by_async(
            VerifyTask.id == verify_task_id,
            VerifyTask.task_keep_end_time > datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            task_status=TaskStatus.PENDING.value,
            code=verify_result['error']['code'],
            message=verify_result['error']['message'],
        )
        

    return ApiCodes.SUCCESS.generate_api_result(data='')


@routers.post("/scan/book/result", summary="扫描订座结果回调", response_model=common_schemas.BaseApiOut)
async def scan_book_result(
    item: crawler_callback_schemas.ScanBookResultIn, client_ip: str = Depends(get_real_client_ip)
):
    order_no = item.task_info['order_no']
    mock_pnr = item.task_info['mock_pnr']
    unique_id = item.task_info['unique_id']
    sure_expire_time = None
    err_msg = item.error.message
    # session_id = ''
    flight_info_str = ''
    try:
        if item.error.code == ApiCodes.SUCCESS.value and item.data:
            # 如果扫描到更低的票价并占座成功，就需要
            # 1. 记录本次占座成功的PNR
            # 2. 更新ScanTask状态为success
            # 3. 发出钉钉提醒
            scan_expire_time = (item.data.get('book', {}).get('expire_time', ''),)
            await VerifyTmpOrder.update_by_async(
                VerifyTmpOrder.order_no == order_no,
                VerifyTmpOrder.scan_status == ScanTaskStatus.RUNNING.value,
                scan_status=ScanTaskStatus.SUCCESS.value,
                scan_pnr=item.data.get('book', {}).get('pnr', ''),
                scan_expire_time=scan_expire_time,
                scan_book_result=orjson.dumps(item.data).decode('utf-8'),
                code=item.error.code,
                message=item.error.message,
            )
            sure_expire_time = item.data['book']['expire_time']
        else:
            await VerifyTmpOrder.update_by_async(
                VerifyTmpOrder.order_no == order_no,
                VerifyTmpOrder.mock_pnr == mock_pnr,
                code=item.error.code,
                message=item.error.message,
            )
            if item.error.code == ApiCodes.UNKNOWN.value:
                # 如果有是9999错误，更新error_code和error_message, 设置状态为failed
                await VerifyTmpOrder.update_by_async(
                    VerifyTmpOrder.order_no == order_no,
                    VerifyTmpOrder.mock_pnr == mock_pnr,
                    VerifyTmpOrder.scan_status.notin_([ScanTaskStatus.SUCCESS.value, ScanTaskStatus.CLOSED.value]),
                    scan_status=ScanTaskStatus.FAILED.value,
                )
            else:
                # 没有明确错误，就设置状态为prepare，等待下次扫描
                await VerifyTmpOrder.update_by_async(
                    VerifyTmpOrder.order_no == order_no,
                    VerifyTmpOrder.mock_pnr == mock_pnr,
                    VerifyTmpOrder.scan_status.notin_([ScanTaskStatus.SUCCESS.value, ScanTaskStatus.CLOSED.value]),
                    scan_status=ScanTaskStatus.PREPARE.value,
                )
        verify_tmp_order_row = await VerifyTmpOrder.get_by_async(
            VerifyTmpOrder.order_no == order_no, VerifyTmpOrder.mock_pnr == mock_pnr
        )
        if verify_tmp_order_row:
            order_info = orjson.loads(verify_tmp_order_row['order_info'])
            # session_id = order_info.get('session_id', '')
            fare_key_data = public_services.decode_fare_key(order_info['fare_key'])
            flight_info_str = (
                ' '.join(
                    [
                        order_info.get('dep_airport_code', ''),
                        order_info.get('arr_airport_code', ''),
                        fare_key_data.get('dep_date', ''),
                        order_info.get('flight_no', ''),
                        fare_key_data.get('src_currency', ''),
                    ]
                )
            ).strip()

    except Exception as e:
        logger.exception(e)
    finally:
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.order_no == order_no,
            VerifyTmpOrder.mock_pnr == mock_pnr,
            scan_times=VerifyTmpOrder.scan_times + 1,
        )
        if item.error.code in [ApiCodes.SUCCESS.value, ApiCodes.UNKNOWN.value]:
            real_pnr = item.data.get('book')
            if real_pnr and isinstance(real_pnr, dict):
                real_pnr = real_pnr.get('pnr', '')
            else:
                real_pnr = ''
            await msg_service.scan_order_alert(
                mock_pnr=mock_pnr,
                code=item.error.code,
                message=err_msg,
                real_pnr=real_pnr,
                sure_expire_time=sure_expire_time,
                # session_id=session_id,
                # unique_id=unique_id,
                flight_info_str=flight_info_str,
            )

    return ApiCodes.SUCCESS.generate_api_result(data='')


@routers.post("/hood/result", summary="压位占座结果", response_model=common_schemas.BaseApiOut)
async def hood_result(
    item: hy_sdks.flight_fare.callback.HoodCallbackRequest, client_ip: str = Depends(get_real_client_ip)
):
    row = await crawler_callback_services.save_hood_result(hood_result=item.model_dump(exclude_none=True))
    try:
        await crawler_callback_services.del_hood_from_normal_cache(hood_data_row=row)
        await crawler_callback_services.apply_shopping_push(
            search_result={
                'task_info': {
                    'airline_code': row['airline_code'],
                    'dep_airport_code': row['dep_airport_code'],
                    'arr_airport_code': row['arr_airport_code'],
                    'dep_date': row['dep_date'],
                },
                'data': {'results': [item.model_dump(exclude_none=True)['flight_info']]},
            }
        )
    except Exception as e:
        logger.exception(e)
    return ApiCodes.SUCCESS.generate_api_result(data=row)


@routers.post("/confirm_pay/result", summary="预占座支付结果", response_model=common_schemas.BaseApiOut)
async def confirm_pay_result(
    item: crawler_callback_schemas.ConfirmPayResultIn, client_ip: str = Depends(get_real_client_ip)
):
    tmp_order_row = await VerifyTmpOrder.get_by_async(VerifyTmpOrder.order_no == item.task_info['order_no'])
    if not tmp_order_row:
        raise Exception(f'订单不存在：{item.task_info["order_no"]}')
    if '已支付' in tmp_order_row['message']:
        raise Exception(f'订单已支付：{item.task_info["order_no"]}')
    if item.error.code == ApiCodes.SUCCESS.value:
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.order_no == item.task_info['order_no'],
            pay_result=orjson.dumps(item.data).decode('utf-8'),
            task_status=TaskStatus.SUCCESS.value,
            code=item.error.code,
            message=item.error.message,
        )
    else:
        await VerifyTmpOrder.update_by_async(
            VerifyTmpOrder.order_no == item.task_info['order_no'],
            task_status=TaskStatus.FAILED.value,
            code=item.error.code,
            message=item.error.message,
        )
    return ApiCodes.SUCCESS.generate_api_result(data='')
