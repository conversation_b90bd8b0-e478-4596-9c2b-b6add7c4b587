from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import List
from typing_extensions import Annotated

from loguru import logger
from fastapi import APIRouter, Depends, HTTPException, Request

from app.services import admin_services
from app.services.mongo_services import FlightFareMongoService
from app.views.schemas import cache_schemas
from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip
from app.config import settings

routers = APIRouter(prefix=f'{settings.API_PERFIX}/cache', tags=['缓存管理接口'])


@routers.post("/search", summary="查询", response_model=cache_schemas.SearchOut)
async def search(
    item: cache_schemas.SearchIn,
    request: Request,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    flight_fare_mongo = FlightFareMongoService('flight_fare')
    offset = (item.page - 1) * item.page_size
    query = item.model_dump(exclude_none=True, exclude_unset=True)
    total, rows = await flight_fare_mongo.search(
        collection_name='airline_fare_cache', limit=item.page_size, offset=offset, **query.get('conditions', {})
    )
    logger.debug(rows)

    result = {'total': total, 'rows': rows}
    return result
    # return {}
