import copy
from datetime import datetime, timedelta

from io import BytesIO
import re
from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Response, UploadFile
from openpyxl import load_workbook
import orjson
import pandas as pd
from sqlalchemy import and_, or_

from app.consts.types import UpdateModeCode
from app.models.fare_rule import FareRule
from app.models.fare_rule_add_on import FareRuleAddOn
from app.models.fare_rule_baggage import FareRuleBaggage
from app.models.fare_rule_op_log import FareRuleOperateLog
from app.models.operate_log import OperateLog
from app.services import admin_services, base_data_service, excel_services, fare_rule_services, schedule_services
from app.services.operate_log_services import FareRuleOpLogServices
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus, SuccessStatus
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import fare_rule_schemas
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/fare_rule', tags=['投放策略管理接口'])

OPERATION_TAG = "fare_rule"


@routers.post(
    "/add", summary="添加投放策略", response_model=fare_rule_schemas.SingleOut, response_model_exclude_none=True
)
async def add(
    item: fare_rule_schemas.CreateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    new_row = fare_rule_services.request_pre_process(item.model_dump(exclude_none=True, exclude_unset=True))
    new_row['dep_city_code'] = (
        new_row['dep_airport_code']
        if new_row['dep_airport_code'] == '*'
        else await base_data_service.get_city_code(airport_code=new_row['dep_airport_code'])
    )
    new_row['arr_city_code'] = (
        new_row['arr_airport_code']
        if new_row['arr_airport_code'] == '*'
        else await base_data_service.get_city_code(airport_code=new_row['arr_airport_code'])
    )

    fare_row = await fare_rule_services.add_rule(new_row)
    await fare_rule_oplog.do_create(id=fare_row['id'], op_data={'params': new_row, 'message': '成功'})

    logger.debug(ApiCodes.SUCCESS.generate_api_result(data=fare_row))
    return ApiCodes.SUCCESS.generate_api_result(data=fare_row)


@routers.post(
    "/batch/add",
    summary="批量添加投放策略",
    response_model=fare_rule_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def batch_add(
    item: fare_rule_schemas.BatchCreateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    # new_row = fare_rule_services.request_pre_process(item.model_dump(exclude_none=True, exclude_unset=True))
    tmp_row = item.model_dump(exclude_none=True, exclude_unset=True)
    lines = re.split(r'[\/\n\r ,]+', tmp_row['lines'])
    del tmp_row['lines']
    for line in lines:
        new_row = copy.deepcopy(tmp_row)
        new_row['dep_airport_code'], new_row['arr_airport_code'] = line.split('-')

        new_row['dep_city_code'] = (
            new_row['dep_airport_code']
            if new_row['dep_airport_code'] == '*'
            else await base_data_service.get_city_code(airport_code=new_row['dep_airport_code'])
        )
        new_row['arr_city_code'] = (
            new_row['arr_airport_code']
            if new_row['arr_airport_code'] == '*'
            else await base_data_service.get_city_code(airport_code=new_row['arr_airport_code'])
        )

        fare_row = await fare_rule_services.add_rule(new_row)
        await fare_rule_oplog.do_create(id=fare_row['id'], op_data={'params': new_row, 'message': '成功'})

        logger.debug(ApiCodes.SUCCESS.generate_api_result(data=fare_row))
    return ApiCodes.SUCCESS.generate_api_result(data=fare_row)


@routers.post(
    "/update", summary="更新投放策略", response_model=fare_rule_schemas.SingleOut, response_model_exclude_none=True
)
async def update(
    item: fare_rule_schemas.UpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )

    update_row = fare_rule_services.request_pre_process(item.model_dump(exclude_none=True, exclude_unset=True))

    try:
        logger.debug(update_row)
        if update_row.get('dep_airport_code'):
            update_row['dep_city_code'] = (
                update_row['dep_airport_code']
                if update_row['dep_airport_code'] == '*'
                else await base_data_service.get_city_code(airport_code=update_row['dep_airport_code'])
            )
        if update_row.get('arr_airport_code'):
            update_row['arr_city_code'] = (
                update_row['arr_airport_code']
                if update_row['arr_airport_code'] == '*'
                else await base_data_service.get_city_code(airport_code=update_row['arr_airport_code'])
            )
        if len(update_row.keys()) == 1:
            raise HTTPException(status_code=400, detail="更新数据不能为空")
        fetch_rule_row = await fare_rule_services.update_rule(update_row)
        await fare_rule_oplog.do_update(id=fetch_rule_row['id'], op_data={'params': update_row, 'message': '成功'})

    except Exception as e:
        logger.error("更新抓取策略异常")
        fetch_rule_row = await FareRule.get_by_async(FareRule.id == item.id)
        await fare_rule_oplog.do_update(id=fetch_rule_row['id'], op_data={'params': update_row, 'message': str(e)})
        raise e
    return ApiCodes.SUCCESS.generate_api_result(data=fetch_rule_row)


@routers.post("/batch/update", summary="批量更新投放策略", response_model=fare_rule_schemas.BaseApiOut)
async def batch_update(
    item: fare_rule_schemas.BatchUpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )

    update_row = fare_rule_services.request_pre_process(item.model_dump(exclude_none=True, exclude_unset=True))

    try:
        logger.debug(update_row)
        if len(update_row.keys()) == 1:
            raise HTTPException(status_code=400, detail="更新数据不能为空")

        if item.update_model == UpdateModeCode.FIX.value:
            await FareRule.update_by_async(
                FareRule.id.in_(item.ids),
                adult_tag_base=update_row['adult_tag_base'],
                child_tag_base=update_row['child_tag_base'],
                infant_tag_base=update_row['infant_tag_base'],
                auto_min_float=update_row['auto_min_float'],
                auto_max_float=update_row['auto_max_float'],
            )
        elif item.update_model == UpdateModeCode.FLOAT.value:
            await FareRule.update_by_async(
                FareRule.id.in_(item.ids),
                adult_tag_base=FareRule.adult_tag_base + update_row['adult_tag_base'],
                child_tag_base=FareRule.child_tag_base + update_row['child_tag_base'],
                infant_tag_base=FareRule.infant_tag_base + update_row['infant_tag_base'],
                auto_min_float=FareRule.auto_min_float + update_row['auto_min_float'],
                auto_max_float=FareRule.auto_max_float + update_row['auto_max_float'],
            )
        else:
            raise HTTPException(status_code=400, detail="更新模式错误")

        for id in item.ids:
            await fare_rule_oplog.do_update(id=id, op_data={'params': update_row, 'message': '成功'})

    except Exception as e:
        logger.error("更新抓取策略异常")
        for id in item.ids:
            await fare_rule_oplog.do_update(id=id, op_data={'params': update_row, 'message': str(e)})
        raise e
    return ApiCodes.SUCCESS.generate_api_result(data="更新成功")


@routers.post(
    "/batch/enable",
    summary="批量启用投放策略",
    response_model=fare_rule_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def batch_enable(
    item: fare_rule_schemas.BatchStatusUpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )

    await FareRule.update_by_async(FareRule.id.in_(item.ids), status=EnableStatus.ENABLED.value)
    for id in item.ids:
        await fare_rule_oplog.do_batch_enable(id=id, op_data={'params': item.model_dump(), 'message': '成功'})
        fare_row = await FareRule.get_by_async(FareRule.id == id)
        await fare_rule_services.apply_shopping_push(fare_row)

    return ApiCodes.SUCCESS.generate_api_result(data="启用成功")


@routers.post(
    "/batch/disable",
    summary="批量禁用投放策略",
    response_model=fare_rule_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def batch_disable(
    item: fare_rule_schemas.BatchStatusUpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    await FareRule.update_by_async(FareRule.id.in_(item.ids), status=EnableStatus.DISABLED.value)
    for id in item.ids:
        await fare_rule_oplog.do_batch_disable(id=id, op_data={'params': item.model_dump(), 'message': '成功'})

    return ApiCodes.SUCCESS.generate_api_result(data="禁用成功")


@routers.post(
    "/batch/delete",
    summary="批量删除投放策略",
    response_model=fare_rule_schemas.BaseApiOut,
    response_model_exclude_none=True,
)
async def batch_delete(
    item: fare_rule_schemas.BatchStatusUpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    await FareRule.delete_all_async(FareRule.id.in_(item.ids), FareRule.status == EnableStatus.DISABLED.value)
    return ApiCodes.SUCCESS.generate_api_result(data="删除成功")


@routers.post(
    "/search", summary="查询投放策略", response_model=fare_rule_schemas.SearchOut, response_model_exclude_none=True
)
async def search(
    item: fare_rule_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        conditions = []
        if item.conditions.airline_code:
            conditions.append(FareRule.airline_code == item.conditions.airline_code)

        if item.conditions.dep_airport_code:
            if item.conditions.dep_airport_code != '*':
                conditions.append(
                    or_(FareRule.dep_airport_code == item.conditions.dep_airport_code, FareRule.dep_airport_code == '*')
                )

        if item.conditions.arr_airport_code:
            if item.conditions.arr_airport_code != '*':

                conditions.append(
                    or_(FareRule.arr_airport_code == item.conditions.arr_airport_code, FareRule.arr_airport_code == '*')
                )
        if item.conditions.fare_date:
            conditions.append(
                and_(
                    FareRule.fare_start_date <= item.conditions.fare_date,
                    FareRule.fare_end_date >= item.conditions.fare_date,
                )
            )

        if item.conditions.flight_date:
            conditions.append(
                and_(
                    FareRule.flight_start_date <= item.conditions.flight_date,
                    FareRule.flight_end_date >= item.conditions.flight_date,
                )
            )

        if item.conditions.status:
            conditions.append(FareRule.status == item.conditions.status)

        if item.conditions.batch_tags:
            conditions.append(FareRule.batch_tags.like(f'%{item.conditions.batch_tags}%'))

        if item.conditions.channel_code:
            conditions.append(FareRule.channel_code == item.conditions.channel_code)

        total = await FareRule.count_async(*conditions)
        page = item.page if item.page else 1
        page_size = item.page_size if item.page_size else 10
        offset = (page - 1) * page_size
        results = await FareRule.get_all_async(
            *conditions, limit=page_size, offset=offset, order_by=[FareRule.id.desc()]
        )
        for result in results:
            result['baggages'] = await FareRuleBaggage.get_all_async(FareRuleBaggage.fare_rule_id == result['id'])
            result['add_ons'] = await FareRuleAddOn.get_all_async(FareRuleAddOn.fare_rule_id == result['id'])
        logger.debug(results)
        return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
    except Exception as e:
        logger.exception("查询抓取策略异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/oplog/list",
    summary="操作日志列表",
    response_model=fare_rule_schemas.OpLogListOut,
    response_model_exclude_none=True,
)
async def oplog_list(
    item: fare_rule_schemas.OpLogListIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_oplog = FareRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    log_rows = await fare_rule_oplog.get_list(item.id)
    return ApiCodes.SUCCESS.generate_api_result(data=log_rows)


@routers.post("/detail", summary="投放策略详情", response_model=fare_rule_schemas.DetailOut)
async def detail(
    item: fare_rule_schemas.DetailIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_rule_row = await FareRule.get_by_async(FareRule.id == item.id)
    if fare_rule_row:
        fare_rule_row['baggages'] = await FareRuleBaggage.get_all_async(
            FareRuleBaggage.fare_rule_id == fare_rule_row['id']
        )
        fare_rule_row['add_ons'] = await FareRuleAddOn.get_all_async(FareRuleAddOn.fare_rule_id == fare_rule_row['id'])
    return ApiCodes.SUCCESS.generate_api_result(data=fare_rule_row)


@routers.post("/delete", summary="删除投放策略", response_model=fare_rule_schemas.BaseApiOut)
async def delete(
    item: fare_rule_schemas.DetailIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fare_row = await FareRule.get_by_async(FareRule.id == item.id)
    if fare_row['status'] != EnableStatus.DISABLED.value:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="只有已禁用的策略才能删除")

    affected = await FareRule.delete_all_async(FareRule.id == item.id, FareRule.status == EnableStatus.DISABLED.value)
    return ApiCodes.SUCCESS.generate_api_result(data=affected)
