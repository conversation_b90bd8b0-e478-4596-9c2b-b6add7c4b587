from datetime import datetime, timed<PERSON>ta

from io import Bytes<PERSON>
from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, File, Form, HTTPException, Response, UploadFile
from openpyxl import load_workbook
import orjson
import pandas as pd
from sqlalchemy import or_

from app.models.fetch_rule import FetchRule
from app.models.operate_log import OperateLog
from app.services import admin_services, excel_services, fetch_rule_services
from app.services.operate_log_services import FetchRuleOpLogServices
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus, SuccessStatus
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import fetch_rule_schemas
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/fetch_rule', tags=['抓取策略管理接口'])

OPERATION_TAG = "fetch_rule"


@routers.post(
    "/add", summary="添加抓取策略", response_model=fetch_rule_schemas.SingleOut, response_model_exclude_none=True
)
async def add(
    item: fetch_rule_schemas.CreateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fetch_rule_oplog = FetchRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    new_row = fetch_rule_services.request_pre_process(item.model_dump(exclude_none=True, exclude_unset=True))

    fetch_rule_row = await FetchRule.create_at_async(**new_row)
    fetch_rule_row = fetch_rule_services.format_out(fetch_rule_row)
    await fetch_rule_oplog.do_create(id=fetch_rule_row['id'], op_data={'params': new_row, 'message': '成功'})

    return ApiCodes.SUCCESS.generate_api_result(data=fetch_rule_row)


@routers.post("/update", summary="", response_model=fetch_rule_schemas.SingleOut, response_model_exclude_none=True)
async def update(
    item: fetch_rule_schemas.UpdateIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fetch_rule_oplog = FetchRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    update_row = fetch_rule_services.request_pre_process(item.model_dump(exclude_none=True, exclude_unset=True))
    try:
        logger.debug(update_row)
        if len(update_row.keys()) == 1:
            raise HTTPException(status_code=400, detail="更新数据不能为空")

        await FetchRule.update_by_async(FetchRule.id == item.id, **update_row)

        fetch_rule_row = await FetchRule.get_by_async(FetchRule.id == item.id)

        await fetch_rule_oplog.do_update(id=fetch_rule_row['id'], op_data={'params': update_row, 'message': '成功'})

    except Exception as e:
        logger.error("更新抓取策略异常")
        fetch_rule_row = await FetchRule.get_by_async(FetchRule.id == item.id)

        await fetch_rule_oplog.do_update(id=fetch_rule_row['id'], op_data={'params': update_row, 'message': str(e)})
        raise e
    fetch_rule_row = fetch_rule_services.format_out(fetch_rule_row)
    return ApiCodes.SUCCESS.generate_api_result(data=fetch_rule_row)


@routers.post(
    "/batch/enable",
    summary="批量启用",
    response_model=fetch_rule_schemas.BatchEnableOrDisableOut,
    response_model_exclude_none=True,
)
async def batch_enable(
    item: fetch_rule_schemas.BatchEnableOrDisableIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fetch_rule_oplog = FetchRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    await FetchRule.update_by_async(FetchRule.id.in_(item.ids), status=EnableStatus.ENABLED.value)

    # 操作日志
    for id in item.ids:
        await fetch_rule_oplog.do_batch_enable(id=id, op_data={'params': item.model_dump(), 'message': '成功'})

    return ApiCodes.SUCCESS.generate_api_result(data=len(item.ids))


@routers.post(
    "/batch/disable",
    summary="批量禁用",
    response_model=fetch_rule_schemas.BatchEnableOrDisableOut,
    response_model_exclude_none=True,
)
async def batch_disable(
    item: fetch_rule_schemas.BatchEnableOrDisableIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fetch_rule_oplog = FetchRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    await FetchRule.update_by_async(FetchRule.id.in_(item.ids), status=EnableStatus.DISABLED.value)

    # 操作日志
    for id in item.ids:
        await fetch_rule_oplog.do_batch_disable(id=id, op_data={'params': item.model_dump(), 'message': '成功'})
    return ApiCodes.SUCCESS.generate_api_result(data=len(item.ids))


@routers.post(
    "/search", summary="查询抓取策略", response_model=fetch_rule_schemas.SearchOut, response_model_exclude_none=True
)
async def search(
    item: fetch_rule_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        conditions = []
        if item.conditions.airline_code:
            conditions.append(FetchRule.airline_code == item.conditions.airline_code)

        if item.conditions.dep_airport_code:
            if item.conditions.dep_airport_code == '*':
                conditions.append(FetchRule.dep_airport_code == item.conditions.dep_airport_code)
            else:
                conditions.append(
                    or_(
                        FetchRule.dep_airport_code == item.conditions.dep_airport_code,
                        FetchRule.dep_airport_code == '*',
                    )
                )

        if item.conditions.arr_airport_code:
            if item.conditions.arr_airport_code == '*':
                conditions.append(FetchRule.arr_airport_code == item.conditions.arr_airport_code)
            else:
                conditions.append(
                    or_(
                        FetchRule.arr_airport_code == item.conditions.arr_airport_code,
                        FetchRule.arr_airport_code == '*',
                    )
                )
        if item.conditions.fetch_type:
            conditions.append(FetchRule.fetch_type == item.conditions.fetch_type)

        if item.conditions.status:
            conditions.append(FetchRule.status == item.conditions.status)

        if item.conditions.batch_tags:
            conditions.append(FetchRule.batch_tags.like(f'%{item.conditions.batch_tags}%'))

        total = await FetchRule.count_async(*conditions)
        page = item.page if item.page else 1
        page_size = item.page_size if item.page_size else 10
        offset = (page - 1) * page_size
        results = await FetchRule.get_all_async(
            *conditions, limit=page_size, offset=offset, order_by=[FetchRule.id.desc()]
        )
        logger.debug(results)
        for result in results:
            result = fetch_rule_services.format_out(result)
        logger.debug(results)
        return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
    except Exception as e:
        logger.exception("查询抓取策略异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post("/detail", summary="抓取策略详情", response_model=fetch_rule_schemas.DetailOut)
async def detail(
    item: fetch_rule_schemas.DetailIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fetch_rule_row = await FetchRule.get_by_async(FetchRule.id == item.id)
    fetch_rule_row = fetch_rule_services.format_out(fetch_rule_row)
    return ApiCodes.SUCCESS.generate_api_result(data=fetch_rule_row)


@routers.post("/oplog/list", summary="抓取策略操作日志列表", response_model=fetch_rule_schemas.OpLogListOut)
async def oplog_list(
    item: fetch_rule_schemas.OpLogListIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    fetch_rule_oplog = FetchRuleOpLogServices(
        admin_id=current_admin['id'], username=current_admin['username'], username_desc=current_admin['username_desc']
    )
    log_rows = await fetch_rule_oplog.get_list(item.id)
    return ApiCodes.SUCCESS.generate_api_result(data=log_rows)
