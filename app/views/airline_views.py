from datetime import datetime, <PERSON><PERSON><PERSON>

from typing_extensions import Annotated
from loguru import logger
from fastapi import APIRouter, Depends, Form, HTTPException
from fastapi.security import OAuth2PasswordRequestForm

from app.models.admin import Admin
from app.models.airline import Airline
from app.models.airline_rate import AirlineRate
from app.services import admin_services, airline_services
from commons.consts.api_codes import ApiCodes
from commons.consts.flight.resource_site import FlightSiteStatus, FlightSiteType
from commons.depends import get_real_client_ip
from app.config import settings
from app.views.schemas import airline_schemas
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/airline', tags=['航司管理接口'])


@routers.post("/add", summary="添加航司", response_model=airline_schemas.SingleOut, response_model_exclude_none=True)
async def add(
    item: airline_schemas.AirlineBase,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        new_row = airline_services.request_pre_process(item)

        rates = new_row.pop('rates', [])
        airline_row = await Airline.create_at_async(**new_row)
        if rates:
            logger.info(f"添加航司汇率：{rates}")
            await AirlineRate.bulk_insert_or_update(records=rates, update_keys=['airline_code', 'currency_code'])
        airline_row['rates'] = await AirlineRate.get_all_async(AirlineRate.airline_code == airline_row['airline_code'])
        return ApiCodes.SUCCESS.generate_api_result(data=airline_row)
    except Exception as e:
        logger.exception("添加航司异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post("/update", summary="更新航司", response_model=airline_schemas.SingleOut, response_model_exclude_none=True)
async def update(
    item: airline_schemas.AirlineFull,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        new_row = airline_services.request_pre_process(item)
        rates = new_row.pop('rates', [])
        if new_row:
            logger.info(f"更新航司：{new_row}")
            await Airline.update_by_async(Airline.id == item.id, **new_row)

        airline_row = await Airline.get_by_async(Airline.id == item.id)

        if rates:
            await AirlineRate.bulk_insert_or_update(records=rates, update_keys=['airline_code', 'currency_code'])
            currency_codes = [rate['currency_code'] for rate in rates]
            await AirlineRate.delete_all_async(
                AirlineRate.airline_code == airline_row['airline_code'],
                AirlineRate.currency_code.notin_(currency_codes),
            )
        else:
            await AirlineRate.delete_all_async(AirlineRate.airline_code == item.airline_code)
        airline_row['rates'] = await AirlineRate.get_all_async(AirlineRate.airline_code == airline_row['airline_code'])
        return ApiCodes.SUCCESS.generate_api_result(data=airline_row)
    except Exception as e:
        logger.exception("更新航司异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/search", summary="航司查询接口", response_model=airline_schemas.SearchOut, response_model_exclude_none=True
)
async def search(
    item: airline_schemas.SearchIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        conditions = []
        if item.conditions.airline_id:
            conditions.append(Airline.id == item.conditions.airline_id)

        if item.conditions.site_status:
            conditions.append(Airline.site_status == item.conditions.site_status)
        total = await Airline.count_async(*conditions)
        page = item.page if item.page else 1
        page_size = item.page_size if item.page_size else 10
        offset = (page - 1) * page_size
        results = await Airline.get_all_async(*conditions, limit=page_size, offset=offset, order_by=[Airline.id.desc()])
        for row in results:
            row['rates'] = await AirlineRate.get_all_async(AirlineRate.airline_code == row['airline_code'])
        return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
    except Exception as e:
        logger.exception("航司查询异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post("/list", summary="航司列表接口", response_model=airline_schemas.ListOut, response_model_exclude_none=True)
async def list(
    item: airline_schemas.ListIn,
    current_admin: dict = Depends(admin_services.get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):
    try:
        # 默认只查询正常的航司
        conditions = [Airline.site_status == FlightSiteStatus.NORMAL.value]
        if item.site_type:
            conditions.append(Airline.site_type == item.site_type)

        columns = [Airline.id, Airline.airline_code, Airline.airline_name, Airline.site_type, Airline.site_code]
        if conditions:
            results = await Airline.get_all_async(*conditions, columns=columns)
        else:
            results = await Airline.get_all_async(columns=columns)
        for row in results:
            row['rates'] = await AirlineRate.get_all_async(AirlineRate.airline_code == row['airline_code'])
        return ApiCodes.SUCCESS.generate_api_result(data=results)
    except Exception as e:
        logger.exception("航司列表异常")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@routers.post(
    "/auto/config",
    summary="航司自动化配置列表",
    response_model=airline_schemas.AutoConfigOut,
    response_model_exclude_none=True,
)
async def auto_config(client_ip: str = Depends(get_real_client_ip)):
    airline_rows = await Airline.get_all_async(Airline.site_type == FlightSiteType.AIRLINE.value)
    result = {}
    for row in airline_rows:
        result[row['airline_code']] = {
            "abandoned_journey": row['abandoned_journey'],
            "auto_book": row['auto_book'],
            "auto_pay": row['auto_pay'],
            "book_check": row['book_check'],
        }
    return ApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post(
    "/detail", summary="航司详情接口", response_model=airline_schemas.SingleOut, response_model_exclude_none=True
)
async def detail(item: airline_schemas.ByIdIn, client_ip: str = Depends(get_real_client_ip)):
    airline_row = await Airline.get_by_async(Airline.id == item.id)
    airline_row['rates'] = await AirlineRate.get_all_async(AirlineRate.airline_code == airline_row['airline_code'])
    return ApiCodes.SUCCESS.generate_api_result(data=airline_row)
