from typing import Any, Dict
from loguru import logger
from pydantic import model_validator


class ModelMixin:

    @model_validator(mode='before')
    @classmethod
    def parse_values(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        logger.debug(values)
        # 自动生成 requestId 和自动填充 customerId
        if 'airline_code' in values:
            values['airline_code'] = values['airline_code'].upper()
        if 'currency_code' in values:
            values['currency_code'] = values['currency_code'].upper()
        if 'dep_airport_code' in values:
            values['dep_airport_code'] = values['dep_airport_code'].upper()
        if 'arr_airport_code' in values:
            values['arr_airport_code'] = values['arr_airport_code'].upper()
        if 'dep_city_code' in values:
            values['dep_city_code'] = values['dep_city_code'].upper()
        if 'arr_city_code' in values:
            values['arr_city_code'] = values['arr_city_code'].upper()
        return values
