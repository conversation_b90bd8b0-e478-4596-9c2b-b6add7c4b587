from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field

from app.views.schemas.airline_rate_schemas import AirlineRateBase, AirlineRateFull
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class SearchConditions(BaseModel):
    '''查询条件'''

    airline_id: Optional[int] = None
    site_status: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''查询入参'''

    conditions: Optional[SearchConditions] = None


class AirlineBase(BaseModel):
    '''航空公司表，基础数据'''

    airline_name: Optional[str] = Field(None, description="航空公司中文名")
    airline_code: Optional[str] = Field(None, description="航班二字码")
    country_name: Optional[str] = Field(None, description="国家中文名")
    site_code: Optional[str] = Field(None, description="站点编码，内部编码，约定后全系统唯一")
    site_name: Optional[str] = Field(None, description="站点名")
    site_url: Optional[str] = Field(None, description="站点URL")
    site_type: Optional[str] = Field(None, description="站点类型")
    site_desc: Optional[str] = Field(None, description="站点描述")
    site_status: Optional[str] = Field(None, description="站点状态")
    abandoned_journey: Optional[str] = Field(None, description="是否开启弃程")
    expire_seconds: Optional[int] = Field(None, description="缓存过期秒数，仅OTA站点有效")

    rates: Optional[List[AirlineRateBase]] = Field(default_factory=list, description="汇率列表")


class AirlineFull(AirlineBase):
    '''更新入参'''

    id: int = Field(..., description="航空公司ID")

    # rates: Optional[List[AirlineRateFull]] = Field(default_factory=list, description="汇率列表")

    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = None

    # class Config:
    #     orm_mode = True


class SingleOut(BaseApiOut):
    '''创建/更新出参'''

    data: AirlineFull


class SearchResult(BaseSearchResult):
    '''查询出参data'''

    rows: List[AirlineFull]


class SearchOut(BaseApiOut):
    '''查询出参'''

    data: SearchResult


class ListIn(BaseModel):
    '''查询入参'''

    site_type: Optional[str] = None


class ListOut(BaseApiOut):
    '''查询出参'''

    data: List[AirlineFull] = Field(default_factory=list, description="航线列表")


class AutoConfigOut(BaseApiOut):
    '''自动化配置出参'''

    data: Optional[Dict[str, Dict[str, Any]]]


class ByIdIn(BaseModel):
    '''根据ID查询入参'''

    id: int = Field(..., description="航空公司ID")
