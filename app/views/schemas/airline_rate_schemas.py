from datetime import date
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator

from app.views.schemas.model_schemas import OperateLogFull
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class AirlineRateBase(BaseModel):
    '''航空公司汇率表，基础数据'''

    airline_code: str = Field(..., example="CA", description="航司(站点)ID，外键")
    currency_code: str = Field(..., example="CNY", description="货币代码")
    fixed_rate: float = Field(0, example=0.0, description="固定汇率，小数点后8位")
    discount_fee: float = Field(0, example=0.0, description="减免手续费，小数点后2位，注意和抓取币种一致")

    @field_validator('airline_code', 'currency_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class AirlineRateFull(AirlineRateBase):
    '''航空公司汇率表，完整数据'''

    id: int = Field(..., description="航空公司汇率表ID")

    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = Field(None, description="更新时间")

    class Config:
        json_encoders = {date: lambda v: v.strftime('%Y-%m-%d')}

    # class Config:
    #     orm_mode = True


class SingleOut(BaseApiOut):
    '''创建/更新出参'''

    data: AirlineRateFull


class SearchResult(BaseSearchResult):
    '''查询出参data'''

    rows: List[AirlineRateFull]


class SearchOut(BaseApiOut):
    '''查询出参'''

    data: SearchResult


class ByIdIn(BaseModel):
    '''根据ID查询入参'''

    id: int = Field(..., description="航空公司汇率表ID")


class DetailOut(BaseApiOut):
    data: Optional[AirlineRateFull] = Field(None, description="航空公司汇率表详情")
