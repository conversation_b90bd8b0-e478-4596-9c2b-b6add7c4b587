from datetime import date
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator

from app.views.schemas.model_schemas import OperateLogFull, VerifyTmpOrderFull
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class Conditions(BaseModel):
    mock_pnr: Optional[str] = None
    real_pnr: Optional[str] = None
    task_status: Optional[str] = None
    scan_status: Optional[str] = None
    dep_airport_code: Optional[str] = None
    arr_airport_code: Optional[str] = None
    order_date: Optional[Union[str, List[str]]] = None
    flight_no: Optional[str] = None


class SearchIn(BaseSearchIn):
    conditions: Optional[Conditions] = None


class SearchResult(BaseSearchResult):
    rows: List[VerifyTmpOrderFull]


class SearchOut(BaseApiOut):
    data: SearchResult


class ScanTurnOn(BaseModel):
    order_no: str = Field(..., description="订单号，如 20250220110250673")


class ScanTurnOff(BaseModel):
    order_no: str = Field(..., description="订单号，如 20250220110250673")


class ScanTurnOut(BaseApiOut):
    status: str = Field(..., description="状态")


class DirectPay(BaseModel):
    order_no: str = Field(..., description="订单号，如 20250220110250673")
    base_float: Optional[int] = Field(0, description="损失浮动")
