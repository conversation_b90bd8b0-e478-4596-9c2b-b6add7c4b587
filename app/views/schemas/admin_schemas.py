from typing import List, Optional
from pydantic import BaseModel, Field

from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class SearchConditions(BaseModel):
    '''查询条件'''

    keyword: Optional[str] = None
    status: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''查询入参'''

    conditions: Optional[SearchConditions] = None


class AdminBase(BaseModel):
    '''Admin 基础结构'''

    username: Optional[str]
    username_desc: Optional[str]
    status: Optional[str] = None
    is_super: Optional[str] = None


class CreateIn(AdminBase):
    '''创建入参'''

    password: str
    confirm_password: str
    roles: Optional[List[str]]


class UpdateIn(AdminBase):
    '''更新入参'''

    id: int
    username: Optional[str] = None
    username_desc: Optional[str] = None
    status: Optional[str] = None
    roles: Optional[List[str]] = None


class AdminView(AdminBase):
    '''完整数据'''

    id: int
    username: str
    username_desc: Optional[str] = None
    status: Optional[str]
    is_super: Optional[str]
    roles: List[str]
    created: Optional[str] = None
    updated: Optional[str] = None


class ResetPasswordIn(BaseModel):
    '''重置密码入参'''

    id: int

    new_password: str
    confirm_password: str


class UpdatePasswordIn(ResetPasswordIn):
    '''更新密码入参'''

    old_password: str


class AdminOut(BaseApiOut):
    '''创建/更新出参'''

    data: AdminView


class SearchResult(BaseSearchResult):
    '''查询出参data'''

    rows: List[AdminView]


class SearchOut(BaseApiOut):
    '''查询出参'''

    data: SearchResult


class ListIn(BaseModel):
    '''查询入参'''

    status: Optional[str] = None


class ListOut(BaseApiOut):
    '''查询出参'''

    data: List[AdminView] = Field(default_factory=list, description="管理员列表")
