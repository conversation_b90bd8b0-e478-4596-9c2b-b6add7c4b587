from typing import List, Optional
from pydantic import BaseModel, Field

from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class SearchConditions(BaseModel):
    '''查询条件'''

    ota_platform_id: Optional[int] = None
    status: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''查询入参'''

    conditions: Optional[SearchConditions] = None


class OtaPlatformBase(BaseModel):
    '''创建入参'''

    ota_name: str
    ota_code: str


class UpdateIn(OtaPlatformBase):
    '''更新入参'''

    id: int
    ota_name: Optional[str] = None
    ota_code: Optional[str] = None
    status: Optional[str] = None


class OtaPlatformFull(UpdateIn):
    '''完整数据'''

    created: Optional[str] = None
    updated: Optional[str] = None


class OtaPlatformOut(BaseApiOut):
    '''创建/更新出参'''

    data: OtaPlatformFull


class SearchResult(BaseSearchResult):
    '''查询出参data'''

    rows: List[OtaPlatformFull]


class SearchOut(BaseApiOut):
    '''查询出参'''

    data: SearchResult


class ListIn(BaseModel):
    '''查询入参'''

    status: Optional[str] = None


class ListOut(BaseApiOut):
    '''查询出参'''

    data: List[OtaPlatformFull] = Field(default_factory=list, description="OTA投放平台列表")
