from typing import List, Optional
from pydantic import BaseModel, Field

from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class SearchConditions(BaseModel):
    '''查询条件'''

    role_name: Optional[str] = None
    status: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''查询入参'''

    conditions: Optional[SearchConditions] = None


class CreateIn(BaseModel):
    '''创建入参'''

    role_name: str
    role_code: str
    privileges: Optional[List[str]] = None


class UpdateIn(CreateIn):
    '''更新入参'''

    id: int
    role_name: Optional[str] = None
    role_code: Optional[str] = None
    status: Optional[str] = None


class AdminRoleFull(UpdateIn):
    '''完整数据'''

    created: Optional[str] = None
    updated: Optional[str] = None


class AdminRoleOut(BaseApiOut):
    '''创建/更新出参'''

    data: AdminRoleFull


class SearchResult(BaseSearchResult):
    '''查询出参data'''

    rows: List[AdminRoleFull]


class SearchOut(BaseApiOut):
    '''查询出参'''

    data: SearchResult


class ListIn(BaseModel):
    '''查询入参'''

    status: Optional[str] = None


class ListOut(BaseApiOut):
    '''查询出参'''

    data: List[AdminRoleFull] = Field(default_factory=list, description="角色列表")
