from datetime import date
from typing import List, Optional, Union
from pydantic import BaseModel, Field

from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class BatchCreateOrUpdateIn(BaseModel):
    '''时刻表批量创建、更新入参'''

    schedules: str


class BatchCreateOrUpdateOut(BaseApiOut):
    '''时刻表批量创建、更新出参'''

    data: int


class UpdateIn(BaseModel):
    '''时刻表更新入参'''

    id: int
    start_date: Optional[Union[str, date]] = None
    end_date: Optional[Union[str, date]] = None
    dep_airport_code: Optional[str] = None
    arr_airport_code: Optional[str] = None
    airline_code: Optional[str] = None
    schedules: Optional[str] = None
    status: Optional[str] = None


class UpdateOut(BaseApiOut):
    '''时刻表更新出参'''

    data: UpdateIn

    class Config:
        json_encoders = {date: lambda v: v.strftime('%Y-%m-%d')}


class SearchConditions(BaseModel):
    '''时刻表查询条件'''

    dep_airport_code: Optional[str] = None
    arr_airport_code: Optional[str] = None
    airline_code: Optional[str] = None
    schedules: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''时刻表查询入参'''

    conditions: Optional[SearchConditions] = None


class ScheduleFull(UpdateIn):

    created: Optional[str] = None
    updated: Optional[str] = None

    # class Config:
    #     json_encoders = {date: lambda v: v.strftime('%Y-%m-%d')}


class SearchResult(BaseSearchResult):
    rows: List[ScheduleFull]


class SearchOut(BaseApiOut):
    '''时刻表查询出参'''

    data: SearchResult


class DeleteIn(BaseModel):
    '''时刻表删除入参'''

    ids: List[int] = Field(default_factory=list, description="时刻表ID列表")


class DetailIn(BaseModel):
    '''时刻表详情入参'''

    id: int


class DetailOut(BaseApiOut):
    '''时刻表详情出参'''

    data: Optional[ScheduleFull] = Field(None, description="时刻表详情")
