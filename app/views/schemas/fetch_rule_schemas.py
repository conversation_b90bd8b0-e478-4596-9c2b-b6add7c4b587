from datetime import date
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator

from app.views.schemas.model_schemas import OperateLogFull
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class SearchConditions(BaseModel):
    '''查询条件'''

    airline_code: Optional[str] = None
    dep_airport_code: Optional[str] = None
    arr_airport_code: Optional[str] = None
    fetch_type: Optional[str] = None
    status: Optional[str] = None
    batch_tags: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''查询入参'''

    conditions: Optional[SearchConditions] = None


class FloatSeconds(BaseModel):
    min: int = Field(..., description="最小余票数")
    max: int = Field(..., description="最大余票数")
    seconds: int = Field(..., description="缓存时间")


class CreateIn(BaseModel):
    '''创建入参'''

    batch_tags: str = Field(..., example="batch_tags", description="批次标签，用于筛选")
    airline_code: str = Field(..., example="CA", description="航司(站点)ID，外键")
    dep_airport_code: str = Field(..., example="PEK", description="出发机场三字码")
    arr_airport_code: str = Field(..., example="BJS", description="到达机场三字码")
    start_days: Optional[int] = Field(None, description="从n天开始抓取（含n）")
    end_days: Optional[int] = Field(None, description="到n天抓取结束（含n）")
    fetch_type: Optional[str] = Field(None, description="抓取类型")
    flight_start_date: Optional[str] = Field(None, description="航班开始日期")
    flight_end_date: Optional[str] = Field(None, description="航班结束日期")
    status: Optional[str] = Field(None, description="抓取策略状态")
    ota_codes: Optional[str] = Field(None, description="需要同时抓取的ota平台：taobao,ctrip")
    priority_level: Optional[int] = Field(20, description="配置冲突时的优先级：1~99，默认20")
    expire_seconds: Optional[int] = Field(0, description="缓存过期时间，精确到秒，0表示不抓取")
    currency_code: Optional[str] = Field(
        None, description="抓取时指定支付币种，传空时爬虫自行使用目标网站(城市)默认币种"
    )
    cache_type: Optional[str] = Field(None, description="缓存类型")
    float_seconds: Optional[List[FloatSeconds]] = Field(
        default_factory=list, description="浮动缓存时间，按余票数计算，精确到秒"
    )

    @field_validator('airline_code', 'dep_airport_code', 'arr_airport_code', mode='before')
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class UpdateIn(CreateIn):
    '''更新入参'''

    id: int
    airline_code: Optional[str] = Field(None, example="CA", description="航司(站点)ID，外键")
    dep_airport_code: Optional[str] = Field(None, example="PEK", description="出发机场三字码")
    arr_airport_code: Optional[str] = Field(None, example="BJS", description="到达机场三字码")
    batch_tags: Optional[str] = Field(None, example="batch_tags", description="批次标签，用于筛选")


class FetchRuleFull(UpdateIn):
    '''完整数据'''

    class Config:
        json_encoders = {date: lambda v: v.strftime('%Y-%m-%d') if v and isinstance(v, date) else None}

    created: Optional[str] = None
    updated: Optional[str] = None

    # class Config:
    #     orm_mode = True


class SingleOut(BaseApiOut):
    '''创建/更新出参'''

    data: FetchRuleFull


class SearchResult(BaseSearchResult):
    '''查询出参data'''

    rows: List[FetchRuleFull]


class SearchOut(BaseApiOut):
    '''查询出参'''

    data: SearchResult


class BatchEnableOrDisableOut(BaseApiOut):
    '''批量启用出参'''

    data: int


class BatchEnableOrDisableIn(BaseModel):
    '''批量启用入参'''

    ids: List[int]


class OpLogListIn(BaseModel):
    '''抓取策略操作日志列表入参'''

    id: int = Field(..., description="抓取策略ID")


class OpLogListOut(BaseApiOut):
    '''抓取策略操作日志列表出参'''

    data: Optional[List[OperateLogFull]] = Field(default_factory=list, description="操作日志")


class DetailIn(BaseModel):
    id: int = Field(..., description="抓取策略ID")


class DetailOut(BaseApiOut):
    data: Optional[FetchRuleFull] = Field(None, description="抓取策略详情")
