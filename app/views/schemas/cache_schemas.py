from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from app.views.schemas.crawler_callback_schemas import (
    BaggageDetail,
    ErrorInfo,
    ExchangeRates,
    FlightAddOns,
    FlightSegment,
    MealDetail,
    TaskInfo,
    TripFare,
)
from commons.cores.base_api_request_model import BaseApiRequestModel, BaseApiResponseModel
from app.views.schemas.common_schemas import FlightInfo
from commons.fastapi.schemas.common_schemas import BaseSearchIn, BaseSearchResult


class SearchConditions(BaseModel):
    airline_code: Optional[str] = Field(None, description="航司二字码")
    dep_airport_code: Optional[str] = Field(None, description="出发机场三字码")
    arr_airport_code: Optional[str] = Field(None, description="到达机场三字码")
    data_range: Optional[List[str]] = Field(None, description="出发日期")
    # return_date: Optional[str] = Field(None, description="返程日期")
    max_quantity: Optional[int] = Field(None, description="最大数量")
    max_price: Optional[float] = Field(None, description="最大价格")
    flight_no: Optional[str] = Field(None, description="航班号")
    is_expired: Optional[Any] = Field(None, description="是否过期")


class SearchIn(BaseSearchIn):
    conditions: SearchConditions


class CacheFlightSegment(FlightSegment):
    batch_key: str
    trip_type: str
    trip_index: int
    dep_city_code: Optional[str] = Field(None, description="出发城市三字码")
    arr_city_code: Optional[str] = Field(None, description="到达城市三字码")

    fares: Dict[str, TripFare] = Field(..., description="乘客类型的票价详情")
    includes: Dict[str, Optional[Union[Dict[str, Union[BaggageDetail, List[BaggageDetail]]], MealDetail, bool]]] = (
        Field(..., description="包含的服务")
    )
    add_ons: FlightAddOns = Field(default_factory=dict, description="附加服务详情")
    exchange: ExchangeRates = Field(None, description="汇率信息")


class CacheInfo(BaseModel):
    task_info: TaskInfo = Field(..., description="任务信息")
    result: CacheFlightSegment = Field(default_factory=dict, description="行程详情")

    error: ErrorInfo = Field(..., description="错误信息")


class SearchOut(BaseSearchResult):
    rows: Optional[List[CacheInfo]] = Field(default_factory=list, description="航班信息")
