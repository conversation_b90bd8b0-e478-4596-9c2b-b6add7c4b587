from datetime import date
from typing import List, Optional, Union
from pydantic import BaseModel, Field

from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class SearchConditions(BaseModel):
    '''时刻表查询条件'''

    dep_airport_code: Optional[str] = None
    arr_airport_code: Optional[str] = None
    airline_code: Optional[str] = None
    schedules: Optional[str] = None


class SearchIn(BaseSearchIn):
    '''时刻表查询入参'''

    conditions: Optional[SearchConditions] = None


class IdListIn(BaseModel):
    '''删除入参'''

    ids: Optional[List[str]] = Field(..., description="主键ID列表")


class StopInfo(BaseModel):
    stop_index: int = Field(..., description="经停记录顺序")
    stop_airport_code: str = Field(..., description="经停机场二字码")
    stop_airport_name: str = Field(..., description="经停机场名称")
    stop_arr_time: str = Field(..., description="经停到达时间")
    stop_dep_time: str = Field(..., description="经停起飞时间")


class FlightInfo(BaseModel):
    """
    'airline_code': row[0].upper().strip(),
               'flight_no': row[1].upper().strip(),
               'dep_airport_code': row[2].upper().strip(),
               'arr_airport_code': row[3].upper().strip(),
               'dep_time': dep_time,
               'arr_time': arr_time,
               'share_code': True if row[6].upper().strip() == 'Y' else False,
               'aircraft_code': str(row[9]).upper().strip(),
               'start_date': start_date,
               'end_date': end_date,
               'stops': parse_stops(row[10]),
               'schedules': str(int(row[11])).strip(),
               'days': int(row[12]),
    """

    airline_code: Optional[str] = Field(None, description="航司二字码")
    flight_no: Optional[str] = Field(None, description="航班号")
    dep_airport_code: Optional[str] = Field(None, description="出发机场二字码")
    arr_airport_code: Optional[str] = Field(None, description="到达机场二字码")
    dep_time: Optional[str] = Field(None, description="起飞时间")
    arr_time: Optional[str] = Field(None, description="降落时间")
    share_code: Optional[bool] = Field(False, description="是否共享")
    aircraft_code: Optional[str] = Field(None, description="机型")
    start_date: Optional[Union[str, date]] = Field(None, description="起飞日期")
    end_date: Optional[Union[str, date]] = Field(None, description="结束日期")
    schedules: Optional[str] = Field(None, description="时刻表")
    stops: Optional[List[StopInfo]] = Field(default_factory=list, description="经停")
    days: Optional[int] = Field(None, description="跨天")


class UpdateIn(FlightInfo):
    '''更新入参'''

    id: str = Field(..., description="主键ID")
