from datetime import date
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator

from app.views.schemas.model_schemas import (
    FareRuleAddOnBase,
    FareRuleAddOnFull,
    FareRuleBaggageBase,
    FareRuleBaggageFull,
    FareRuleBase,
    FareRuleFull,
    OperateLogFull,
)
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class HoodDataBase(BaseModel):
    airline_code: str = Field(..., description="航司(站点)ID，外键")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    dep_date: date = Field(..., description="航班开始日期")
    flight_no: str = Field(..., description="航班号")
    cabin_code: str = Field(..., description="舱位编码")
    cabin_level: str = Field(..., description="舱位等级")
    quantity: int = Field(..., description="占座数量")
    adult_base: float = Field(..., description="压位价格(人民币)")
    adult_tax: float = Field(..., description="税费(人民币)")
    src_adult_base: float = Field(..., description="压位原始价格")
    src_adult_tax: float = Field(..., description="税费")
    src_currency_code: str = Field(..., description="原始币种")
    expired_time: str = Field(..., description="过期时间")
    flight_info: Optional[Union[dict, str]] = Field(None, description="航班信息")
    extra_info: Optional[Union[dict, str]] = Field(None, description="额外信息")
    ex_rate: float = Field(0, description="汇率")
    discount_fee: float = Field(0, description="减免手续费")


class HoodDataFull(HoodDataBase):
    id: int = Field(..., description="压位记录ID")
    created: str = Field(..., description="创建时间")
    updated: str = Field(..., description="更新时间")


class SearchConditions(BaseModel):
    '''查询条件'''

    airline_code: Optional[str] = Field(None, description="航空公司")
    dep_airport_code: Optional[str] = Field(None, description="起飞机场")
    arr_airport_code: Optional[str] = Field(None, description="降落机场")
    dep_date: Optional[str] = Field(None, description="起飞日期")
    flight_no: Optional[str] = Field(None, description="航班号")
    show_all: Optional[bool] = Field(False, description="是否显示所有")


class SearchIn(BaseSearchIn):
    '''查询入参'''

    conditions: Optional[SearchConditions] = None


class SearchResult(BaseSearchResult):
    rows: List[HoodDataFull]


class SearchOut(BaseApiOut):
    data: SearchResult
