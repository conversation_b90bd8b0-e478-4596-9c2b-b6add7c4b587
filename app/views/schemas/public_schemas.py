from typing import Any, List, Optional, Dict, Union
from pydantic import BaseModel, Field

from app.views.schemas.base_schemas import ModelMixin
from app.views.schemas.crawler_callback_schemas import ErrorInfo, ResultItem, SearchData
from app.views.schemas.model_schemas import VerifyTmpOrderBase, VerifyTmpOrderFull
from commons.fastapi.schemas.common_schemas import BaseApiOut


class BaggageDetail(BaseModel):
    count: int = Field(0, description="数量")
    weight: Optional[Union[int, float]] = Field(None, description="单件重量")
    all_weight: Optional[Union[int, float]] = Field(None, description="总重量")


class Baggages(BaseModel):
    cabin_baggage: Optional[List[BaggageDetail]] = Field(None, description="随身行李额")
    hand_baggage: Optional[List[BaggageDetail]] = Field(None, description="手提行李额")
    checked_baggage: Optional[List[BaggageDetail]] = Field(None, description="托运行李额")


class SimpleFlightIncludes(BaseModel):
    baggage: Optional[Baggages] = Field(None, description="额外行李选项")
    meals: Optional[BaggageDetail] = Field(None, description="餐食选项")
    seat: Optional[BaggageDetail] = Field(None, description="座位选择")
    board_first: Optional[BaggageDetail] = Field(None, description="优先登机选项")
    flex: Optional[BaggageDetail] = Field(None, description="灵活改签选项")
    wifi: Optional[BaggageDetail] = Field(None, description="Wi-Fi 选项")


class SimpleStop(BaseModel):
    stop_index: int = Field(..., description="经停机场序号")
    stop_airport_code: str = Field(..., description="经停机场三字码")
    stop_airport_name: str = Field(..., description="经停机场名称")
    stop_arr_time: str = Field(..., description="经停机场到达时间")
    stop_dep_time: str = Field(..., description="经停机场起飞时间")


class SimpleFlightSegment(BaseModel):

    segment_index: int = Field(..., description="航段序号，用于拼接后排序")
    airline_code: str = Field(..., description="航空公司二字码")
    flight_no: str = Field(..., description="完整航班号，如 TR101")
    dep_airport_code: str = Field(..., description="航段起飞机场三字码")
    arr_airport_code: str = Field(..., description="航段到达机场三字码")
    dep_date: str = Field(..., description="航段起飞日期")
    dep_time: str = Field(..., description="航段起飞时间")
    arr_date: str = Field(..., description="航段到达日期")
    arr_time: str = Field(..., description="航段到达时间")
    share_code: bool = Field(False, description="航班共享码")
    aircraft_code: str = Field('', description="航班机型")  # Aircraftcode
    stop_times: int = Field(0, description="经停次数")
    stops: Optional[List[SimpleStop]] = Field(default_factory=list, description="经停记录")


class FareInfo(BaseModel):
    base: Union[int, float] = Field(..., description="基础价，原币种")
    tax: Union[int, float] = Field(..., description="税费")
    total: Union[int, float] = Field(..., description="含税总价")
    quantity: Union[int, float] = Field(..., description="库存数量")
    src_cny_base: Union[int, float] = Field(None, description="基础价，人民币")
    src_cny_tax: Union[int, float] = Field(None, description="税费")
    src_cny_total: Union[int, float] = Field(None, description="含税总价")
    src_base: Union[int, float] = Field(..., description="基础价，原币种")
    src_tax: Union[int, float] = Field(..., description="税费")
    src_total: Union[int, float] = Field(..., description="含税总价")
    src_currency: str = Field(..., description="币种")


class Product(BaseModel):
    fare_type: str = Field(..., description="ID 或编码")
    fare_key: str = Field(..., description="主键ID")
    cabin: str = Field(..., description="舱位详情")
    cabin_class: str = Field(..., description="舱位详情")
    adult: FareInfo = Field(..., description="成人票价")
    child: Optional[FareInfo] = Field(None, description="儿童票价")
    infant: Optional[FareInfo] = Field(None, description="婴儿票价")
    includes: Optional[SimpleFlightIncludes] = Field(None, description="附加服务详情")
    child_includes: Optional[SimpleFlightIncludes] = Field(None, description="儿童附加服务详情")
    infant_includes: Optional[SimpleFlightIncludes] = Field(None, description="婴儿附加服务详情")
    add_ons: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="辅营列表")
    baggages: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="行李列表")
    enable_auto_float: bool = Field(False, description="是否开启自动浮动")
    max_float: float = Field(0, description="最大浮动")
    min_float: float = Field(0, description="最小浮动")
    actual_float: float = Field(0, description="实际浮动")


class FlightInfo(BaseModel):
    airline_code: str = Field(..., description="航空公司二字码")
    trip_type: str = Field(..., description="行程类型: ow (单程), ct (标准联程), rt (往返)")
    expire_seconds: int = Field(0, description="航班过期时间，单位秒")
    flight_no: str = Field(..., description="完整航班号，如 TR101")
    dep_airport_code: str = Field(..., description="航段起飞机场三字码")
    arr_airport_code: str = Field(..., description="航段到达机场三字码")
    dep_date: str = Field(..., description="航段起飞日期")
    dep_time: str = Field(..., description="航段起飞时间")
    arr_date: str = Field(..., description="航段到达日期")
    arr_time: str = Field(..., description="航段到达时间")
    segments: List[SimpleFlightSegment] = Field(..., description="航段列表")


class SimpleFlight(BaseModel):
    flight_info: Optional[FlightInfo] = Field(None, description="航班信息")
    products: Optional[List[Product]] = Field(None, description="乘客类型的票价详情")


class SearchIn(BaseModel, ModelMixin):
    channel_code: str = Field(..., description="产品渠道编码")
    dep_city_code: str = Field(..., description="出发城市三字码")
    arr_city_code: str = Field(..., description="到达城市三字码")
    # dep_airport_code: str = Field(..., description="出发机场三字码")
    # arr_airport_code: str = Field(..., description="到达机场三字码")
    dep_date: str = Field(..., description="出发日期")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    # fare_type: Optional[str] = Field(None, description="ID 或编码")


class SearchOut(BaseApiOut):
    # data: List[Optional[ResultItem]] = Field(default_factory=list, description="行程详情")
    data: List[Optional[SimpleFlight]] = Field(default_factory=list, description="行程详情")


class VerifyIn(BaseModel, ModelMixin):
    fare_type: Optional[str] = Field(None, description="运价类型")
    fare_key: str = Field(..., description="运价key")
    dep_airport_code: str = Field(..., description="出发机场三字码")
    arr_airport_code: str = Field(..., description="到达机场三字码")
    flight_no: str = Field(..., description="航班号")
    adult: Optional[int] = Field(1, description="成人数量")
    child: Optional[int] = Field(0, description="儿童数量")
    infant: Optional[int] = Field(0, description="婴儿数量")
    use_cache: Optional[bool] = Field(True, description="使用缓存数据进行校验")


class VerifyOut(BaseApiOut):
    data: Optional[SimpleFlight] = Field(None, description="行程详情")


class FlightVerifyOrderInfo(VerifyTmpOrderFull):
    src_currency: Optional[str] = Field(None, description="原币种")


class FlightVerifyOrderOut(BaseApiOut):
    data: FlightVerifyOrderInfo


class TicketChannelMap(BaseModel):
    verify_book: VerifyTmpOrderFull = Field(..., description="行程详情")
    hood_book: Optional[List[Dict[str, Any]]] = Field(default_factory=list, description="行程详情")


class GetTicketChannelsOut(BaseApiOut):
    data: TicketChannelMap = Field(..., description="行程详情")


class ExchangeRate(BaseModel):
    airline_code: Optional[str] = Field(None, description="航空公司代码")
    src_currency: str = Field(..., description="原币种")
    dst_currency: str = Field(..., description="目标币种")
    src_price: Union[int, float] = Field(..., description="原价")
    dst_price: Union[int, float] = Field(..., description="目标价")
    rate: float = Field(..., description="汇率")


class GetExchangeRateOut(BaseApiOut):
    data: ExchangeRate = Field(..., description="汇率详情")
