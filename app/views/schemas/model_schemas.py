from datetime import date
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, field_validator

from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class FareRuleBase(BaseModel):
    batch_tags: str = Field(None, example="batch_tags", description="批次标签，用于筛选")
    airline_code: Optional[str] = Field(None, description="航空公司")
    channel_code: Optional[str] = Field(None, description="OTA代码")

    fare_valid_type: Optional[str] = Field(None, description="运价有效类型")
    fare_start_date: Optional[Union[date, str]] = Field(None, description="航班开始日期")
    fare_end_date: Optional[Union[date, str]] = Field(None, description="航班结束日期")

    flight_date_type: Optional[str] = Field(None, description="航班日期类型")
    start_days: Optional[int] = Field(0, description="从n天开始投放（含n）")
    end_days: Optional[int] = Field(0, description="到n天投放结束（含n）")
    flight_start_date: Optional[Union[date, str]] = Field(None, description="航班开始日期")
    flight_end_date: Optional[Union[date, str]] = Field(None, description="航班结束日期")
    dep_airport_code: Optional[str] = Field(None, description="出发机场三字码")
    arr_airport_code: Optional[str] = Field(None, description="到达机场三字码")
    flight_nos: Optional[str] = Field(None, description="航班号")
    cabin_class: Optional[str] = Field(None, description="舱位等级")
    cabin_codes: Optional[str] = Field(None, description="舱位编码")
    priority_level: Optional[int] = Field(0, description="优先级")
    status: Optional[str] = Field(None, description="状态")
    adult_markup_type: str = Field(None, description="成人票加价类型")
    child_markup_type: Optional[str] = Field('', description="儿童票加价类型")
    infant_markup_type: Optional[str] = Field('', description="婴儿票加价类型")
    adult_tag_base: Optional[float] = Field(None, description="成人票原价，精确到分")
    child_tag_base: Optional[float] = Field(None, description="儿童票原价，精确到分")
    infant_tag_base: Optional[float] = Field(None, description="婴儿票原价，精确到分")
    adult_tag_tax: Optional[float] = Field(None, description="成人票税费原价，精确到分")
    child_tag_tax: Optional[float] = Field(None, description="儿童票税费原价，精确到分")
    infant_tag_tax: Optional[float] = Field(None, description="婴儿票税费原价，精确到分")
    adult_cabin_code: Optional[str] = Field(None, description="成人舱位编码")
    child_cabin_code: Optional[str] = Field(None, description="儿童舱位编码")
    infant_cabin_code: Optional[str] = Field(None, description="婴儿舱位编码")
    odbn: Optional[str] = Field(None, description="OTA抓取批次")
    auto_float: Optional[str] = Field(None, description="是否启用浮动")

    auto_min_float: Optional[float] = Field(None, description="自动调价最低浮动值，精确到分")
    auto_max_float: Optional[float] = Field(None, description="自动调价最高浮动值，精确到分")
    fare_type: Optional[str] = Field(None, description="运价类型")

    @field_validator(
        'airline_code',
        'dep_airport_code',
        'arr_airport_code',
        # 'flight_nos',
        # 'cabin_codes',
        # 'cabin_class',
        'adult_cabin_code',
        'child_cabin_code',
        'infant_cabin_code',
        mode='before',
    )
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v

    class Config:
        json_encoders = {date: lambda v: v if isinstance(v, str) else v.strftime('%Y-%m-%d')}


class FareRuleFull(FareRuleBase):
    id: Optional[int] = Field(None, description="主键ID")
    created: Optional[str] = None
    updated: Optional[str] = None


class FareRuleBaggageBase(BaseModel):

    fare_rule_id: Optional[int] = Field(None, description="票价规则ID")
    baggage_code: Optional[str] = Field(None, description="行李编码")
    baggage_type: Optional[str] = Field(None, description="行李类型")
    baggage_weight: Optional[int] = Field(..., description="行李重量")

    markup_type: Optional[str] = Field('', description="政策类型")
    baggage_tag_price: Optional[float] = Field(None, description="附加项投放价，精确到分")

    class Config:
        json_encoders = {date: lambda v: v if isinstance(v, str) else v.strftime('%Y-%m-%d')}


class FareRuleBaggageFull(FareRuleBaggageBase):
    id: Optional[int] = Field(None, description="主键ID")
    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = Field(None, description="更新时间")


class FareRuleAddOnBase(BaseModel):

    fare_rule_id: Optional[int] = Field(None, description="票价规则ID")
    add_on_type: Optional[str] = Field(None, description="附加项类型")
    add_on_code: Optional[str] = Field(None, description="附加项编码")
    add_on_name: Optional[str] = Field(None, description="附加项名称")
    add_on_desc: Optional[str] = Field(None, description="附加项描述")
    markup_type: Optional[str] = Field('', description="政策类型")
    add_on_tag_price: Optional[float] = Field(None, description="附加项投放价，精确到分")

    class Config:
        json_encoders = {date: lambda v: v if isinstance(v, str) else v.strftime('%Y-%m-%d')}


class FareRuleAddOnFull(FareRuleAddOnBase):
    id: Optional[int] = Field(None, description="主键ID")
    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = Field(None, description="更新时间")


class OperateLogBase(BaseModel):
    # op_table: Optional[str] = Field(None, description="操作的数据表")
    row_id: Optional[int] = Field(None, description="操作的数据ID")

    admin_id: Optional[int] = Field(None, description="账号ID")
    username: Optional[str] = Field(None, description="用户名")
    op_type: Optional[str] = Field(None, description="操作的数据标识")
    op_time: Optional[str] = Field(None, description="操作时间")
    op_desc: Optional[str] = Field(None, description="操作说明")
    # op_data: Optional[str] = Field(None, description="原始json数据")

    class Config:
        json_encoders = {date: lambda v: v if isinstance(v, str) else v.strftime('%Y-%m-%d')}


class OperateLogFull(OperateLogBase):
    id: Optional[int] = Field(None, description="主键ID")
    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = Field(None, description="更新时间")


class VerifyTmpOrderBase(BaseModel):
    id: Optional[int] = Field(None, description="主键ID")
    order_no: Optional[str] = Field(None, description="订单号")
    mock_pnr: Optional[str] = Field(None, description="模拟PNR")
    real_pnr: Optional[str] = Field(None, description="真实PNR")
    scan_pnr: Optional[str] = Field(None, description="扫描出票PNR")
    expire_time: Optional[str] = Field(None, description="当前系统时区的订单过期时间")
    scan_expire_time: Optional[str] = Field(None, description="扫描出票成功后的过期时间")
    dep_airport_code: Optional[str] = Field(None, description="出发机场")
    arr_airport_code: Optional[str] = Field(None, description="到达机场")
    dep_date: Optional[str] = Field(None, description="出发日期")
    flight_no: Optional[str] = Field(None, description="航班号")


class VerifyTmpOrderFull(VerifyTmpOrderBase):
    sure_expire_time: Optional[str] = Field(None, description="真实过期时间")
    task_status: Optional[str] = Field(None, description="任务状态")
    scan_status: Optional[str] = Field(None, description="扫描任务状态")
    code: Optional[int] = Field(None, description="爬虫回调状态")
    message: Optional[str] = Field(None, description="爬虫回调信息")
    created: Optional[str] = Field(None, description="创建时间")
    updated: Optional[str] = Field(None, description="更新时间")
    order_info: Optional[Union[str, dict]] = Field(None, description="订单信息")
    book_result: Optional[Union[str, dict]] = Field(None, description="验价占座结果")

    scan_times: Optional[int] = Field(None, description="扫描次数")
    scan_book_result: Optional[Union[str, dict]] = Field(None, description="扫描占座结果")
    pay_result: Optional[Union[str, dict]] = Field(None, description="最新支付结果")

    latest_run_time: Optional[str] = Field(None, description="最新运行时间")

    base_float: Optional[int] = Field(0, description="损失浮动")
    product_type: Optional[str] = Field(None, description="产品类型")
