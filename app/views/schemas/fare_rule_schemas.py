from datetime import date
from typing import Any, List, Optional, Union
from pydantic import BaseModel, Field, field_validator

from app.views.schemas.model_schemas import (
    FareRuleAddOnBase,
    FareRuleAddOnFull,
    FareRuleBaggageBase,
    FareRuleBaggageFull,
    FareRuleBase,
    FareRuleFull,
    OperateLogFull,
)
from commons.fastapi.schemas.common_schemas import BaseApiOut, BaseSearchResult, BaseSearchIn


class CreateIn(FareRuleBase):
    baggages: Optional[List[FareRuleBaggageBase]] = Field(..., description="行李信息")
    add_ons: Optional[List[FareRuleAddOnBase]] = Field(default_factory=list, description="附加项信息")


class BatchCreateIn(CreateIn):
    batch_tags: str = Field(..., example="batch_tags", description="批次标签，用于筛选")
    lines: str = Field(..., example="batch_tags", description="航线列表")

    @field_validator(
        'airline_code',
        'dep_airport_code',
        'arr_airport_code',
        # 'flight_nos',
        # 'cabin_codes',
        # 'cabin_class',
        'adult_cabin_code',
        'child_cabin_code',
        'infant_cabin_code',
        'lines',
        mode='before',
    )
    @classmethod
    def to_upper(cls, v: Any):
        if isinstance(v, str):
            return v.upper()
        return v


class UpdateIn(FareRuleFull):

    airline_code: Optional[str] = None
    channel_code: Optional[str] = None
    fare_start_date: Optional[Union[date, str]] = None
    fare_end_date: Optional[Union[date, str]] = None
    flight_start_date: Optional[Union[date, str]] = None
    flight_end_date: Optional[Union[date, str]] = None
    baggages: Optional[List[FareRuleBaggageFull]] = Field(default_factory=list, description="行李信息")
    add_ons: Optional[List[FareRuleAddOnFull]] = Field(default_factory=list, description="附加项信息")


class BatchStatusUpdateIn(BaseModel):
    ids: List[int]


class SingleOut(BaseApiOut):
    data: UpdateIn


class Conditions(BaseModel):
    airline_code: Optional[str] = None
    channel_code: Optional[str] = None
    dep_airport_code: Optional[str] = None
    arr_airport_code: Optional[str] = None
    fare_date: Optional[str] = None
    flight_date: Optional[str] = None
    status: Optional[str] = None
    batch_tags: Optional[str] = None


class SearchIn(BaseSearchIn):
    conditions: Optional[Conditions] = None


class SearchResult(BaseSearchResult):
    rows: List[UpdateIn]


class SearchOut(BaseApiOut):
    data: SearchResult


class OpLogListIn(BaseModel):
    '''投放策略操作日志列表入参'''

    id: int = Field(..., description="投放策略ID")


class OpLogListOut(BaseApiOut):
    '''投放策略操作日志列表出参'''

    data: Optional[List[OperateLogFull]] = Field(default_factory=list, description="操作日志")


class DetailIn(BaseModel):
    id: int = Field(..., description="投放策略ID")


class DetailOut(BaseApiOut):
    data: Optional[UpdateIn] = Field(None, description="投放策略详情")


class BatchUpdateIn(BaseModel):
    ids: List[int] = Field(..., description="投放策略ID列表")
    update_model: str = Field(..., description="更新模式")
    adult_tag_base: Optional[float] = Field(0, description="成人票浮动值，精确到分")
    child_tag_base: Optional[float] = Field(0, description="儿童票浮动值，精确到分")
    infant_tag_base: Optional[float] = Field(0, description="婴儿票浮动值，精确到分")
    auto_min_float: Optional[float] = Field(0, description="自动调价最小浮动值，精确到分")
    auto_max_float: Optional[float] = Field(0, description="自动调价最大浮动值，精确到分")
