from datetime import datetime, timed<PERSON>ta
from fastapi import APIRouter, Depends, HTTPException
from loguru import logger
from pydantic import BaseModel
from sqlalchemy import and_, or_

from app.config import settings


from app.models.hood_data import HoodData
from app.services.admin_services import get_current_admin
from app.views.schemas import hood_data_schemas
from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip
from commons.fastapi.schemas import common_schemas


routers = APIRouter(prefix=f'{settings.API_PERFIX}/hood_data', tags=['压位记录接口'])


@routers.post("/search", summary="压位记录搜索", response_model=hood_data_schemas.SearchOut)
async def search(
    item: hood_data_schemas.SearchIn,
    current_admin: dict = Depends(get_current_admin),
    client_ip: str = Depends(get_real_client_ip),
):

    conditions = []

    if item.conditions.airline_code:
        conditions.append(HoodData.airline_code == item.conditions.airline_code)
    if item.conditions.dep_airport_code:
        conditions.append(HoodData.dep_airport_code == item.conditions.dep_airport_code)
    if item.conditions.arr_airport_code:
        conditions.append(HoodData.arr_airport_code == item.conditions.arr_airport_code)
    if item.conditions.dep_date:
        conditions.append(HoodData.dep_date == item.conditions.dep_date)
    if item.conditions.flight_no:
        conditions.append(HoodData.flight_no == item.conditions.flight_no)
    if item.conditions.show_all is True:
        pass
    else:
        conditions.append(HoodData.expired_time >= datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

    total = await HoodData.count_async(*conditions)
    page = item.page if item.page else 1
    page_size = item.page_size if item.page_size else 10
    offset = (page - 1) * page_size
    results = await HoodData.get_all_async(
        *conditions, limit=page_size, offset=offset, order_by=[HoodData.expired_time.desc()]
    )
    logger.debug(results)
    return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})
