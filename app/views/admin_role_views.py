from http.client import HTT<PERSON><PERSON>x<PERSON>
from loguru import logger
from fastapi import APIRouter, Depends
from fastapi.security import OAuth2PasswordRequestForm
import orj<PERSON>

from app.models.admin_role import AdminRole
from app.models.operate_log import OperateLog
from app.services import admin_role_services, admin_services
from app.views.schemas import admin_role_schemas
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import SuccessStatus
from commons.consts.common_types import YesOrNo
from commons.depends import get_real_client_ip
from app.config import settings
from fastapi import status

routers = APIRouter(prefix=f'{settings.API_PERFIX}/admin_role', tags=['角色管理接口'])


@routers.post(
    "/add", summary="添加角色", response_model=admin_role_schemas.AdminRoleOut, response_model_exclude_none=True
)
async def add(item: admin_role_schemas.CreateIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    admin_role_row = await AdminRole.get_by_async(AdminRole.role_code == item.role_code)
    if admin_role_row:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"角色 {item.role_code} 已存在")

    new_admin_role = admin_role_services.request_pre_process(item)

    admin_role_row = await AdminRole.create_at_async(**new_admin_role)

    admin_role_row = admin_role_services.response_pre_process(admin_role_row)

    return ApiCodes.SUCCESS.generate_api_result(data=admin_role_row)


@routers.post(
    "/update", summary="修改角色", response_model=admin_role_schemas.AdminRoleOut, response_model_exclude_none=True
)
async def update(item: admin_role_schemas.UpdateIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    update_admin_role = admin_role_services.request_pre_process(item)

    await AdminRole.update_by_async(AdminRole.id == item.id, **update_admin_role)
    admin_role_row = await AdminRole.get_by_async(AdminRole.id == item.id)
    admin_role_row = admin_role_services.response_pre_process(admin_role_row)

    return ApiCodes.SUCCESS.generate_api_result(data=admin_role_row)


@routers.post(
    "/search", summary="搜索角色", response_model=admin_role_schemas.SearchOut, response_model_exclude_none=True
)
async def search(item: admin_role_schemas.SearchIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    conditions = []
    if item.conditions:
        if item.conditions.role_name:
            conditions.append(AdminRole.role_name.like(f'%{item.conditions.role_name}%'))
        if item.conditions.status:
            conditions.append(AdminRole.status == item.conditions.status)

    total = await AdminRole.count_async(*conditions)
    page = item.page if item.page else 1
    page_size = item.page_size if item.page_size else 10
    offset = (page - 1) * page_size
    results = await AdminRole.get_all_async(*conditions, limit=page_size, offset=offset, order_by=[AdminRole.id.desc()])
    for result in results:
        result = admin_role_services.response_pre_process(result)
    return ApiCodes.SUCCESS.generate_api_result(data={'total': total, 'rows': results})


@routers.post("/list", summary="角色列表", response_model=admin_role_schemas.ListOut, response_model_exclude_none=True)
async def list(item: admin_role_schemas.ListIn, current_admin: dict = Depends(admin_services.get_current_admin)):
    results = await AdminRole.get_all_async()
    for result in results:
        result = admin_role_services.response_pre_process(result)
    return ApiCodes.SUCCESS.generate_api_result(data=results)
