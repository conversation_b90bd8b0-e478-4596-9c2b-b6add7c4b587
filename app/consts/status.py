from enum import unique
from commons.cores.base_const import BaseEnum


@unique
class AdminUserStatus(BaseEnum):
    """币种"""

    NORMAL = ('normal', '正常')
    FROZEN = ('frozen', '冻结')
    DELETED = ('deleted', '删除')


@unique
class TaskStatus(BaseEnum):
    """任务状态"""

    PENDING = ('pending', '等待中')
    RUNNING = ('running', '运行中')
    SUCCESS = ('success', '成功')
    FAILED = ('failed', '失败')
    CANCELLED = ('cancelled', '取消')


@unique
class ScanTaskStatus(BaseEnum):
    """扫描任务状态"""

    UNKNOW = ('unknow', '未知')  # 在预占座未成功时的状态，默认值
    INIT = ('init', '可开启')  # 预占座成功时初始化
    PREPARE = ('prepare', '已准备')  # 点击开启后状态
    LOCKED = ('locked', '已锁定')  # 脚本扫描锁定准备推送
    RUNNING = ('running', '运行中')  # 脚本扫描推送爬虫之后
    SUCCESS = ('success', '成功')
    FAILED = ('failed', '失败')
    OUTTIME = ('outtime', '超时取消')
    CLOSED = ('closed', '关闭')  # 手动关闭 的状态
