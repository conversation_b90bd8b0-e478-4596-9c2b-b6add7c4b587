from enum import unique
from commons.cores.base_const import BaseEnum


@unique
class FareRuleMarkupType(BaseEnum):
    """投放策略价格浮动类型"""

    UN_SALE = ('', '不投放')
    AMOUNT = ('amount', '浮动金额')
    FIX_AMOUNT = ('fix_amount', '固定金额')
    PERCENT = ('percent', '百分比')


class BaggageType(BaseEnum):
    HAND = ('hand', '手提行李')
    CABIN = ('cabin', '随身行李')
    CHECKED = ('checked', '托运行李')


class AddOnType(BaseEnum):
    MEALS = ('meals', '餐食')
    SEAT = ('seat', '选座')
    BOARD_FIRST = ('board_first', '优先登机')
    FLEX = ('flex', '灵活改签')
    WIFI = ('wifi', 'Wi-Fi')


class FetchType(BaseEnum):
    DAYS = ('days', '按天抓取')
    DATE = ('date', '按日期抓取')


class FareValidType(BaseEnum):
    ALWAYS = ('always', '长期有效')
    DATE = ('date', '按日期有效')


class FareFlightDateType(BaseEnum):
    DAYS = ('days', '按天投放')
    DATE = ('date', '按日期投放')


class FareType(BaseEnum):
    NORMAL = ('normal', '标准运价')
    PRE_ORDER = ('fpo', '压位运价')


class FareChannelType(BaseEnum):
    TB_NORMAL = ('tb_norm', '淘宝-普通')
    TB_GOLD = ('tb_gold', '淘宝-金牌')
    TB_BAGGAGE = ('tb_baggage', '淘宝-行李')
    TB_DELAY = ('tb_delay', '淘宝-延迟出票')
    TB_DELAY2 = ('tb_delay2', '淘宝-延迟出票2')
    TB_DELAY3 = ('tb_delay3', '淘宝-延迟出票3')


class UpdateModeCode(BaseEnum):
    FIX = ('fix', '固定值')
    FLOAT = ('float', '浮动值')


class FetchCacheType(BaseEnum):
    FIXED = ('fixed', '固定值')
    FLOAT_TICKET = ('float_ticket', '浮动值')
