from datetime import datetime, timedelta


def calculate_dates(start_days: int, end_days: int, schedule: str):
    # # 获取当前日期
    today = datetime.today().date()

    # # 计算起始日期
    start_date = today + timedelta(days=start_days)
    end_date = today + timedelta(days=end_days)
    return calculate_dates_by_data_range(str(start_date), str(end_date), schedule)

    # # 将班期字符串转换为集合并处理周日为7的情况
    # valid_days = set(int(day) for day in schedule)

    # # 结果列表，用于存储符合条件的日期
    # result_dates = []

    # # 从起始日期开始遍历，直到找到足够多的符合条件的日期
    # current_date = start_date
    # while len(result_dates) < days:
    #     # 获取当前日期的星期几，注意周日处理为7
    #     weekday = current_date.isoweekday()  # 1=周一, 7=周日

    #     # 如果当前日期的星期几在班期中，则添加到结果列表中
    #     if weekday in valid_days:
    #         result_dates.append(str(current_date))

    #     # 继续向后移动一天
    #     current_date += timedelta(days=1)

    # return result_dates


def calculate_dates_by_data_range(flight_start_date: str, flight_end_date: str, schedule: str):
    start_date = datetime.strptime(flight_start_date, '%Y-%m-%d').date()
    end_date = datetime.strptime(flight_end_date, '%Y-%m-%d').date()
    # 将班期字符串转换为集合并处理周日为7的情况
    valid_days = set(int(day) for day in schedule)
    result_dates = []
    while start_date <= end_date:
        weekday = start_date.isoweekday()  # 1=周一, 7=周日
        # 如果当前日期的星期几在班期中，则添加到结果列表中
        if weekday in valid_days:
            result_dates.append(start_date.strftime('%Y-%m-%d'))
        start_date += timedelta(days=1)
    return result_dates
