# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import sys

from typing import List

import requests
import json

from alibabacloud_dingtalk.robot_1_0.client import Client as dingtalkrobot_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.robot_1_0 import models as dingtalkrobot__1__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Robot:
    @staticmethod
    def send(token, msg, msg_type='text', title='') -> None:

        # 钉钉机器人 Webhook 地址，替换 'xxxxxxxx' 为你的 access_token
        url = 'https://oapi.dingtalk.com/robot/send?access_token=' + token
        print(url)

        # 设置请求头
        headers = {'Content-Type': 'application/json'}
        if msg_type == 'text':
            # 设置请求体
            data = {"msgtype": msg_type, "text": {"content": msg}}
        if msg_type == 'markdown':
            data = {"msgtype": msg_type, "markdown": {"title": title, "text": msg}}

        # 发送 POST 请求
        response = requests.post(url, headers=headers, data=json.dumps(data))

        # 输出响应结果
        print("Status Code:", response.status_code)
        print("Response:", response.text)


if __name__ == '__main__':
    Robot.send('5255a0549e82aa162f55acda2469f7b850f410da1479a780eae6c96deec152e3', 'flight test')
