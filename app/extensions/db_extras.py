import asyncio
from contextlib import contextmanager, asynccontextmanager
import glob
import importlib
import os
import re


from typing import AsyncGenerator


from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_scoped_session, async_sessionmaker


from app.config import settings


async_engine = create_async_engine(
    settings.SQLALCHEMY_DATABASE_URI,
    echo=settings.SQLALCHEMY_ECHO,
    pool_recycle=settings.SQLALCHEMY_POOL_RECYCLE,
    pool_size=settings.SQLALCHEMY_POOL_SIZE,
    pool_timeout=settings.SQLALCHEMY_POOL_TIMEOUT,
    max_overflow=settings.SQLALCHEMY_MAX_OVERFLOW,
)

AsyncSessionLocal = async_scoped_session(
    async_sessionmaker(async_engine, expire_on_commit=False, class_=AsyncSession), scopefunc=asyncio.current_task
)


def import_all_models(models_path=os.path.join(settings.ROOT_PATH, 'app', 'models', '*.py')):
    model_files = glob.glob(models_path)

    for file in model_files:
        # print(file)
        # module_name = file[:-3].replace(f'{AllConfig.ROOT_PATH}/', '').replace('/', '.')
        module_name = file[:-3].replace(settings.ROOT_PATH, '')
        module_name = re.sub(r'[\\\/]', '.', module_name[1:])
        # print(module_name)
        if module_name.split('.')[-1] in ('__init__', 'base'):
            continue
        mod = importlib.import_module(module_name)


@asynccontextmanager
async def get_db_session_async() -> AsyncGenerator:
    session = None
    try:
        session = AsyncSessionLocal
        yield session
        await session.commit()
    except Exception as e:
        if session:
            await session.rollback()
        raise e
    finally:
        if session:
            await session.remove()
            await session.close()


# class AsyncDatabase:
#     def __init__(self, db_uri: str, expire_on_commit: bool = False, **kwargs):
#         # SQLAlchemy setup
#         self.sql_engine = create_async_engine(db_uri, **kwargs)
#         self.async_session_local = async_scoped_session(
#             async_sessionmaker(self.sql_engine, expire_on_commit=expire_on_commit, class_=AsyncSession),
#             scopefunc=asyncio.current_task,
#         )

#     @asynccontextmanager
#     async def get_db_session_async(self) -> AsyncGenerator:
#         session = None
#         try:
#             session = self.async_session_local()
#             yield session
#             await session.commit()
#         except Exception as e:
#             if session:
#                 await session.rollback()
#             raise e
#         finally:
#             if session:
#                 await session.close()


from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.server_api import ServerApi


# # Set the Stable API version when creating a new client
mongo_client = AsyncIOMotorClient(settings.MONGO_URL, server_api=ServerApi('1'), **settings.MONGO_KWARGS)


# async def get_mongo_db(db_name: str) -> AsyncIOMotorClient:
#     mongo_db = mongo_client[db_name]
#     return mongo_db
