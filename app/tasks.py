from celery import Celery
from loguru import logger
import orjson

# import dramatiq
# from dramatiq.brokers.redis import Redis<PERSON>roker
# from dramatiq.middleware.asyncio import AsyncIO
from app.config import SYNC_REDIS_CFG, settings
from app.services import task_services
from commons.extensions.redis_extras import SyncRedisPool

# 模块自身worker
# celery_app = Celery(broker=settings.CELERY_BROKER_URL)
# celery_app.conf.update(
#     task_serializer=settings.CELERY_TASK_SERIALIZER,
#     accept_content=settings.CELERY_ACCEPT_CONTENT,  # Ignore other content
#     result_serializer=settings.CELERY_RESULT_SERIALIZER,
#     broker_connection_retry=settings.CELERY_BROKER_CONNECTION_RETRY,
#     broker_connection_retry_on_startup=settings.CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP,
#     worker_hijack_root_logger=settings.CELERY_WORKER_HIJACK_ROOT_LOGGER,
# )

# 爬虫worker,用来向爬虫模块发送任务
crawler_celery_app = Celery(broker=settings.CELERY_CRAWLER_BROKER_URL)
crawler_celery_app.conf.update(
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,  # Ignore other content
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    broker_connection_retry=settings.CELERY_BROKER_CONNECTION_RETRY,
    broker_connection_retry_on_startup=settings.CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP,
    worker_hijack_root_logger=settings.CELERY_WORKER_HIJACK_ROOT_LOGGER,
)

crawler_celery_tasks = {}
for task_name, queue_name in settings.CELERY_CRAWLER_TASK_ROUTES.items():
    crawler_celery_tasks[task_name] = crawler_celery_app.signature(task_name, queue=queue_name)

# 适配worker,用来向适配模块发送任务
platform_celery_app = Celery(broker=settings.CELERY_PLATFORM_BROKER_URL)
platform_celery_app.conf.update(
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,  # Ignore other content
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    broker_connection_retry=settings.CELERY_BROKER_CONNECTION_RETRY,
    broker_connection_retry_on_startup=settings.CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP,
    worker_hijack_root_logger=settings.CELERY_WORKER_HIJACK_ROOT_LOGGER,
)


def simple_task_push(task_name: str, data: dict):
    """
    向redis队列中推送任务
    """
    task_queue = settings.SPECIAL_CRAWLER_TASK_ROUTES.get(task_name)
    if not task_queue:
        logger.error(f'任务{task_name}未找到对应的队列')
        return
    with SyncRedisPool(**SYNC_REDIS_CFG) as redis:
        redis.lpush(task_queue, orjson.dumps(data).decode('utf-8'))
        # logger.info(f'任务{task_name}已推送到队列{task_queue}')


platform_celery_workers = {}
for task_name, queue_name in settings.CELERY_PLATFORM_TASK_ROUTES.items():
    platform_celery_workers[task_name] = platform_celery_app.signature(task_name, queue=queue_name)

# 配置 Dramatiq
# dramatiq_broker = RedisBroker(url=settings.DRAMATIQ_BROKER_URL)
# dramatiq_broker.add_middleware(middleware=AsyncIO())
# dramatiq.set_broker(dramatiq_broker)


# @dramatiq.actor(actor_name='tb_fetch_task', queue_name=settings.DRAMATIQ_TASK_QUEUES['tb_fetch_task'])
# async def tb_fetch_task(data: dict):
#     logger.debug('执行tb_fetch_task任务')
#     await task_services.tb_data_fetch(data)
