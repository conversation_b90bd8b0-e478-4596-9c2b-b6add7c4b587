import json
import random
from typing import Optional, <PERSON><PERSON>

from curl_cffi import requests
from loguru import logger
import orjson

BROWSER_TYPES = [
    # Edge
    "edge99",
    "edge101",
    # Chrome
    "chrome99",
    "chrome100",
    "chrome101",
    "chrome104",
    "chrome107",
    "chrome110",
    "chrome116",
    "chrome119",
    "chrome120",
    "chrome123",
    # "chrome124",
    # "chrome99_android",
    # Safari
    "safari15_3",
    "safari15_5",
    "safari17_0",
    # "safari17_2_ios",
    # alias
    # "chrome",
    # "edge",
    # "safari",
    # "safari_ios",
    # "chrome_android",
]


class TBClient:
    def __init__(
        self,
        cookies: dict,
        headers: dict,
        timeout: int = 30,
        proxies: dict = None,
        proxy: str = None,
        proxy_auth: Optional[Tuple[str, str]] = None,
        impersonate: str = None,
    ):
        self.session = requests.Session()
        self.session.headers = headers
        self.session.cookies = cookies
        self.session.timeout = timeout
        self.session.proxies = proxies
        self.session.proxy = proxy
        self.session.proxy_auth = proxy_auth
        self.impersonate = impersonate if impersonate else random.choice(BROWSER_TYPES)

    async def async_request(self, method, url, **kwargs):
        check_params = ['timeout', 'cookies', 'headers', 'proxies', 'proxy', 'proxy_auth', 'impersonate']
        for param in check_params:
            if param not in kwargs:
                kwargs[param] = getattr(self.session, param)

        result = None
        response = None
        async with requests.AsyncSession() as s:
            try:
                response = await s.request(method, url, **kwargs)
                result = response.json()
                self.session.cookies.update(response.cookies.get_dict())
                logger.warning(f'response status code: {response.status_code}')
            # json decode错误
            except (json.decoder.JSONDecodeError, orjson.JSONDecodeError):
                result = None
                logger.warning(f'返回内容不是json: {response.text}')
            except Exception as e:
                logger.exception(e)
                raise
            finally:
                logger.debug(f'kwargs: {kwargs}, response: {result}')

        return result

    def request(self, method, url, **kwargs):

        result = None
        response = None
        try:
            response = self.session.request(method, url, **kwargs)
            result = response.json()
            self.session.cookies.update(response.cookies.get_dict())
            logger.warning(f'response status code: {response.status_code}')
        # json decode错误
        except (json.decoder.JSONDecodeError, orjson.JSONDecodeError):
            result = None
            logger.warning(f'返回内容不是json: {response.text}')
        except Exception as e:
            logger.exception(e)
            raise
        finally:
            logger.debug(f'kwargs: {kwargs}, response: {result}')

        return result
