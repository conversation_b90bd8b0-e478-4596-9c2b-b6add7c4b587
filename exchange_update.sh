#!/bin/bash

# 动态生成当天日期
DATE=$(date +%Y%m%d)

# 使用 curl 下载当天的 JSON 数据
curl -o tmp.json https://m.unionpayintl.com/jfimg/${DATE}.json

# 检查 tmp.json 文件是否有效且非空
if [ -s tmp.json ] && jq empty tmp.json >/dev/null 2>&1; then
    echo "tmp.json 内容有效，覆盖 exchange_rate.json"
    # 覆盖 exchange_rate.json
    cp -f tmp.json /home/<USER>/workspace/flight/flight_fare/configs/exchange_rate.json
    cp -f tmp.json /home/<USER>/workspace/flight/flight_pre_order/statics/exchange_rate.json
    cp -f tmp.json /home/<USER>/workspace/flight/flight_order/statics/exchange_rate.json
else
    echo "tmp.json 内容无效，操作中止"
fi

